-- =====================================================
-- 企业信息核心库 - 数据库表结构创建脚本
-- 创建日期: 2025-07-29
-- 描述: 为企业服务系统创建核心数据表结构
-- =====================================================

-- 1. 公司主体表 (Companies)
-- 存储企业的核心标识信息
CREATE TABLE companies (
    id UUID PRIMARY KEY,
    unified_social_credit_code VARCHAR(18) UNIQUE NOT NULL,
    company_name VARCHAR(500) NOT NULL
);

-- 2. 公司历史版本表 (Companies History)
-- 追踪公司信息的所有变更历史
CREATE TABLE companies_history (
    history_id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    version_number INTEGER NOT NULL,
    unified_social_credit_code VARCHAR(18),
    company_name VARCHAR(500),
    operation_type VARCHAR(10) NOT NULL, -- e.g., 'INSERT', 'UPDATE'
    operated_by_user_id UUID NOT NULL,
    operation_reason TEXT,
    effective_from TIMESTAMP WITH TIME ZONE NOT NULL
);

-- 3. 自然人表 (Persons)
-- 存储与企业相关的自然人信息
CREATE TABLE persons (
    id UUID PRIMARY KEY,
    id_number VARCHAR(50) UNIQUE NOT NULL,
    id_type VARCHAR(20) NOT NULL, -- '身份证', '护照'
    person_name VARCHAR(100) NOT NULL
);

-- 4. 自然人历史版本表 (Persons History)
-- 追踪自然人信息的所有变更历史
CREATE TABLE persons_history (
    history_id UUID PRIMARY KEY,
    person_id UUID NOT NULL REFERENCES persons(id),
    version_number INTEGER NOT NULL,
    id_number VARCHAR(50),
    id_type VARCHAR(20),
    person_name VARCHAR(100),
    operation_type VARCHAR(10) NOT NULL,
    operated_by_user_id UUID NOT NULL,
    operation_reason TEXT,
    effective_from TIMESTAMP WITH TIME ZONE NOT NULL
);

-- 5. 角色定义表 (Roles)
-- 定义企业中人员可能担任的角色
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL -- '法定代表人', '授权经办人', '财务负责人'
);

-- 6. 公司与人员关系表 (Company-Person Roles)
-- 建立企业与人员之间的角色关系
CREATE TABLE company_person_roles (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    person_id UUID NOT NULL REFERENCES persons(id),
    role_id INTEGER NOT NULL REFERENCES roles(id),
    start_date DATE NOT NULL,
    end_date DATE,
    is_active BOOLEAN DEFAULT true
);

-- 7. 标签定义表 (Tags)
-- 定义可用于分类企业和关系的标签
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    tag_name VARCHAR(100) UNIQUE NOT NULL,
    tag_category VARCHAR(50) -- '企业资质', '关系类型'
);

-- 8. 公司标签关联表 (Company-Tag Links)
-- 为企业分配相应的标签
CREATE TABLE company_tags (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    tag_id INTEGER NOT NULL REFERENCES tags(id),
    effective_from DATE,
    effective_to DATE
);

-- 创建索引以优化查询性能
CREATE INDEX idx_companies_credit_code ON companies(unified_social_credit_code);
CREATE INDEX idx_persons_id_number ON persons(id_number);
CREATE INDEX idx_company_person_roles_company ON company_person_roles(company_id);
CREATE INDEX idx_company_person_roles_person ON company_person_roles(person_id);
CREATE INDEX idx_company_tags_company ON company_tags(company_id);
CREATE INDEX idx_companies_history_company ON companies_history(company_id);
CREATE INDEX idx_persons_history_person ON persons_history(person_id);

-- 添加注释
COMMENT ON TABLE companies IS '企业主体信息表';
COMMENT ON TABLE companies_history IS '企业信息变更历史表';
COMMENT ON TABLE persons IS '自然人信息表';
COMMENT ON TABLE persons_history IS '自然人信息变更历史表';
COMMENT ON TABLE roles IS '角色定义表';
COMMENT ON TABLE company_person_roles IS '企业人员角色关系表';
COMMENT ON TABLE tags IS '标签定义表';
COMMENT ON TABLE company_tags IS '企业标签关联表';
