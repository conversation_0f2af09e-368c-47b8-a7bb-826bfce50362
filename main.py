#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业服务文档生成系统 - 主入口
本地化模块化Python系统，支持多种业务文档自动生成
"""

import sys
import argparse
from pathlib import Path
from typing import Optional, List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.data_service import UnifiedDataService
from services.document_generator import DocumentGenerator
from services.template_registry import TemplateRegistry
from utils.logger import SystemLogger
from utils.font_tester import FontPreservationTester


class EnterpriseServiceSystem:
    """企业服务文档生成系统主类 - CLI模式运行文档生成"""

    def __init__(self):
        """初始化系统"""
        self.logger = SystemLogger("main_system")

        try:
            # 初始化各个组件
            self.data_service = UnifiedDataService()
            self.template_registry = TemplateRegistry()
            self.document_generator = DocumentGenerator(
                data_service=self.data_service
            )
            self.font_tester = FontPreservationTester()

            self.logger.info("企业服务文档生成系统初始化完成")

        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            print(f"❌ 系统初始化失败: {e}")
            sys.exit(1)

    def list_companies(self) -> List[Dict[str, str]]:
        """列出所有可用的企业"""
        try:
            companies = self.data_service.get_companies_list()
            if companies:
                print("\n📋 可用企业列表:")
                print("-" * 60)
                for i, company in enumerate(companies, 1):
                    print(f"{i:2d}. {company['company_name']}")
                    print(f"    ID: {company['id']}")
                    print(f"    信用代码: {company.get('unified_social_credit_code', '未知')}")
                    print()
            else:
                print("\n❌ 未找到任何企业数据")
            return companies
        except Exception as e:
            self.logger.error(f"获取企业列表失败: {e}")
            print(f"❌ 获取企业列表失败: {e}")
            return []

    def list_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """列出所有可用的模板"""
        try:
            templates = self.template_registry.get_available_templates()
            if templates:
                print("\n📄 可用模板列表:")
                print("-" * 60)
                template_index = 1
                for category, template_list in templates.items():
                    print(f"\n🏷️  {category}:")
                    for template in template_list:
                        print(f"   {template_index:2d}. {template['name']}")
                        print(f"       ID: {template['id']}")
                        print(f"       描述: {template['description']}")
                        print(f"       类型: {template['file_type']}")
                        print()
                        template_index += 1
            else:
                print("\n❌ 未找到任何模板配置")
            return templates
        except Exception as e:
            self.logger.error(f"获取模板列表失败: {e}")
            print(f"❌ 获取模板列表失败: {e}")
            return {}

    def get_template_list_for_selection(self) -> List[Dict[str, str]]:
        """获取用于选择的模板列表"""
        try:
            templates = self.template_registry.get_available_templates()
            template_list = []

            for category, template_configs in templates.items():
                for template in template_configs:
                    template_list.append({
                        'id': template['id'],
                        'name': template['name'],
                        'category': category,
                        'description': template['description']
                    })

            return template_list
        except Exception as e:
            self.logger.error(f"获取模板选择列表失败: {e}")
            return []

    def interactive_mode(self):
        """交互式模式 - 用户输入公司ID或名称，选择模板ID"""
        print("\n🏢 企业服务文档生成系统")
        print("=" * 60)
        print("欢迎使用企业服务文档生成系统！")
        print("本系统支持协定存款、合同放款、客户接洽等多种业务文档的自动生成。")

        while True:
            try:
                print("\n" + "=" * 60)
                print("请选择操作:")
                print("1. 生成文档")
                print("2. 查看企业列表")
                print("3. 查看模板列表")
                print("4. 测试字体格式")
                print("5. 退出系统")

                choice = input("\n请输入选项编号 (1-5): ").strip()

                if choice == '1':
                    self._generate_document_interactive()
                elif choice == '2':
                    self.list_companies()
                elif choice == '3':
                    self.list_templates()
                elif choice == '4':
                    self._test_font_interactive()
                elif choice == '5':
                    print("\n👋 感谢使用企业服务文档生成系统，再见！")
                    break
                else:
                    print("❌ 无效的选项，请输入 1-5")

            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作，系统退出")
                break
            except Exception as e:
                self.logger.error(f"交互模式异常: {e}")
                print(f"❌ 系统异常: {e}")

    def _generate_document_interactive(self):
        """交互式文档生成"""
        try:
            # 1. 选择企业
            print("\n📋 第一步：选择企业")
            companies = self.data_service.get_companies_list()
            if not companies:
                print("❌ 未找到任何企业数据")
                return

            # 显示企业列表
            print("\n可用企业:")
            for i, company in enumerate(companies, 1):
                print(f"{i:2d}. {company['company_name']} (ID: {company['id']})")

            # 用户选择企业
            while True:
                choice = input(f"\n请选择企业编号 (1-{len(companies)}) 或输入企业名称/ID: ").strip()

                if choice.isdigit():
                    company_index = int(choice) - 1
                    if 0 <= company_index < len(companies):
                        selected_company = companies[company_index]
                        break
                    else:
                        print(f"❌ 无效的编号，请输入 1-{len(companies)}")
                else:
                    # 尝试按名称或ID查找
                    company_data = self.data_service.get_company_data(choice)
                    if company_data:
                        selected_company = company_data
                        break
                    else:
                        print("❌ 未找到该企业，请重新输入")

            print(f"✅ 已选择企业: {selected_company['company_name']}")

            # 2. 选择模板
            print("\n📄 第二步：选择模板")
            template_list = self.get_template_list_for_selection()
            if not template_list:
                print("❌ 未找到任何模板配置")
                return

            # 显示模板列表
            print("\n可用模板:")
            for i, template in enumerate(template_list, 1):
                print(f"{i:2d}. [{template['category']}] {template['name']}")
                print(f"     ID: {template['id']}")
                print(f"     描述: {template['description']}")
                print()

            # 用户选择模板
            while True:
                choice = input(f"\n请选择模板编号 (1-{len(template_list)}) 或输入模板ID: ").strip()

                if choice.isdigit():
                    template_index = int(choice) - 1
                    if 0 <= template_index < len(template_list):
                        selected_template = template_list[template_index]
                        break
                    else:
                        print(f"❌ 无效的编号，请输入 1-{len(template_list)}")
                else:
                    # 尝试按ID查找
                    template_info = self.template_registry.get_template_info(choice)
                    if template_info:
                        selected_template = {'id': choice, 'name': template_info.get('name', choice)}
                        break
                    else:
                        print("❌ 未找到该模板，请重新输入")

            print(f"✅ 已选择模板: {selected_template['name']}")

            # 3. 生成文档
            print(f"\n🔄 第三步：生成文档")
            self.generate_document_by_params(
                company_identifier=selected_company['id'],
                template_id=selected_template['id'],
                company_name=selected_company['company_name']
            )

        except Exception as e:
            self.logger.error(f"交互式文档生成失败: {e}")
            print(f"❌ 文档生成失败: {e}")

    def _test_font_interactive(self):
        """交互式字体格式测试"""
        try:
            file_path = input("\n请输入要测试的文档路径: ").strip()
            if file_path and Path(file_path).exists():
                self.run_font_test(file_path)
            else:
                print("❌ 文件不存在或路径无效")
        except Exception as e:
            print(f"❌ 字体测试失败: {e}")

    def generate_document_by_params(self, company_identifier: str, template_id: str, company_name: str = None):
        """根据参数生成单个文档"""
        try:
            print(f"\n🔄 开始生成文档...")
            print(f"   企业: {company_name or company_identifier}")
            print(f"   模板ID: {template_id}")

            # 调用DocumentGenerator生成文档
            result = self.document_generator.generate_document(
                company_id=company_identifier,
                template_id=template_id
            )

            if result['success']:
                print(f"\n✅ 文档生成成功!")
                print(f"📁 输出文件: {result['output_path']}")
                print(f"📊 替换字段: {result.get('total_replacements', 0)}个")
                print(f"📈 成功字段: {result.get('successful_fields', 0)}/{result.get('total_fields', 0)}")

                # 显示验证结果
                validation_result = result.get('validation_result', {})
                if validation_result.get('warnings'):
                    print(f"⚠️  数据警告: {len(validation_result['warnings'])}个")
                    for warning in validation_result['warnings']:
                        print(f"   - {warning}")

                # 可选：运行字体格式测试
                test_choice = input("\n是否运行字体格式测试? (y/N): ").strip().lower()
                if test_choice == 'y':
                    self.run_font_test(result['output_path'])

                print(f"\n🎉 文档生成完成！文件保存在: {result['output_path']}")

            else:
                print(f"\n❌ 文档生成失败!")
                print(f"错误信息: {result.get('error', '未知错误')}")

                # 显示验证结果
                validation_result = result.get('validation_result', {})
                if validation_result and not validation_result.get('is_valid'):
                    print(f"\n📋 数据校验问题:")
                    if validation_result.get('missing_fields'):
                        print(f"   缺失必填字段: {', '.join(validation_result['missing_fields'])}")
                    if validation_result.get('invalid_fields'):
                        print(f"   格式错误字段: {', '.join(validation_result['invalid_fields'])}")

        except Exception as e:
            self.logger.error(f"文档生成异常: {e}")
            print(f"❌ 文档生成异常: {e}")

    def generate_document_by_cli(self, company_id: str, template_id: str):
        """命令行模式生成文档"""
        try:
            # 获取企业信息
            company_data = self.data_service.get_company_data(company_id)
            if not company_data:
                print(f"❌ 未找到企业数据: {company_id}")
                return

            # 获取模板信息
            template_info = self.template_registry.get_template_info(template_id)
            if not template_info:
                print(f"❌ 未找到模板配置: {template_id}")
                return

            print(f"📋 企业: {company_data['company_name']}")
            print(f"📄 模板: {template_info.get('name', template_id)}")

            # 生成文档
            self.generate_document_by_params(company_id, template_id, company_data['company_name'])

        except Exception as e:
            self.logger.error(f"命令行文档生成失败: {e}")
            print(f"❌ 命令行文档生成失败: {e}")
    
    def run_font_test(self, file_path: str):
        """运行字体格式测试"""
        try:
            print(f"\n🔤 运行字体格式测试...")
            result = self.font_tester.test_document(file_path)
            
            if result['passed']:
                print(f"✅ 字体格式测试通过 (保持率: {result['preservation_rate']:.1f}%)")
            else:
                print(f"⚠️ 字体格式测试未完全通过 (保持率: {result['preservation_rate']:.1f}%)")
                print(f"   详细信息: {result.get('details', '')}")
                
        except Exception as e:
            self.logger.error(f"字体格式测试失败: {e}")
            print(f"❌ 字体格式测试失败: {e}")
    
    def batch_generate(self, company_ids: List[str], template_keys: List[str]):
        """批量生成文档"""
        try:
            print(f"\n🔄 开始批量生成...")
            print(f"   企业数量: {len(company_ids)}")
            print(f"   模板数量: {len(template_keys)}")
            
            results = self.document_generator.batch_generate(
                company_ids=company_ids,
                template_keys=template_keys
            )
            
            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)
            
            print(f"\n📊 批量生成完成:")
            print(f"   成功: {success_count}/{total_count}")
            print(f"   失败: {total_count - success_count}/{total_count}")
            
            # 显示失败的详情
            failed_results = [r for r in results if not r['success']]
            if failed_results:
                print(f"\n❌ 失败详情:")
                for result in failed_results:
                    print(f"   {result['company_id']} + {result['template_key']}: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            self.logger.error(f"批量生成异常: {e}")
            print(f"❌ 批量生成异常: {e}")


def main():
    """主函数 - CLI模式运行文档生成"""
    parser = argparse.ArgumentParser(
        description='企业服务文档生成系统 - 本地化模块化Python系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py                                    # 交互模式
  python main.py --list-companies                   # 列出所有企业
  python main.py --list-templates                   # 列出所有模板
  python main.py --company-id 1 --template-id deposit_agreement  # 生成文档
  python main.py --test-fonts output/document.docx  # 测试字体格式
        """
    )

    parser.add_argument('--company-id', help='企业ID或企业名称')
    parser.add_argument('--template-id', help='模板ID')
    parser.add_argument('--list-companies', action='store_true', help='列出所有企业')
    parser.add_argument('--list-templates', action='store_true', help='列出所有模板')
    parser.add_argument('--test-fonts', help='测试指定文档的字体格式')
    parser.add_argument('--validate-template', help='验证指定模板的配置')
    parser.add_argument('--version', action='version', version='企业服务文档生成系统 v2.0')

    args = parser.parse_args()

    # 初始化系统
    try:
        system = EnterpriseServiceSystem()
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        sys.exit(1)

    try:
        # 根据命令行参数执行相应操作
        if args.list_companies:
            print("\n📋 企业列表:")
            system.list_companies()

        elif args.list_templates:
            print("\n📄 模板列表:")
            system.list_templates()

        elif args.test_fonts:
            if Path(args.test_fonts).exists():
                system.run_font_test(args.test_fonts)
            else:
                print(f"❌ 文件不存在: {args.test_fonts}")

        elif args.validate_template:
            result = system.template_registry.validate_template_config(args.validate_template)
            print(f"\n🔍 模板配置验证结果:")
            print(f"模板ID: {args.validate_template}")
            print(f"状态: {'✅ 有效' if result['valid'] else '❌ 无效'}")
            if result['errors']:
                print(f"错误: {', '.join(result['errors'])}")
            if result['warnings']:
                print(f"警告: {', '.join(result['warnings'])}")

        elif args.company_id and args.template_id:
            print(f"\n🔄 命令行模式生成文档:")
            system.generate_document_by_cli(args.company_id, args.template_id)

        elif args.company_id or args.template_id:
            print("❌ 生成文档需要同时指定 --company-id 和 --template-id")
            print("使用 --help 查看帮助信息")

        else:
            # 默认进入交互模式
            system.interactive_mode()

    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作，系统退出")
    except Exception as e:
        system.logger.error(f"系统运行异常: {e}")
        print(f"❌ 系统运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
