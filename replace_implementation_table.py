#!/usr/bin/env python3
"""
精确替换落实情况表中的三个部分
"""

import docx
from pathlib import Path

def replace_implementation_table():
    # 源文件
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    target_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    
    print('=== 开始替换落实情况表 ===\n')
    
    # 1. 提取源数据
    print('📖 提取源数据...')
    precondition_data = extract_precondition_data(quota_file)
    continuous_data = extract_continuous_data(quota_file)
    loan_condition_data = extract_loan_condition_data(business_file)
    
    # 2. 替换目标文件
    print('🔄 替换目标文件...')
    replace_target_file(target_file, precondition_data, continuous_data, loan_condition_data)
    
    print('✅ 替换完成！')

def extract_precondition_data(file_path):
    """提取用信前提条件数据"""
    doc = docx.Document(file_path)
    table = doc.tables[4]  # 第5个表格（索引4）
    
    # 提取第2行第2列的内容
    precondition_text = table.rows[1].cells[1].text.strip()
    print(f'✅ 提取用信前提条件: {precondition_text[:50]}...')
    return precondition_text

def extract_continuous_data(file_path):
    """提取持续条件数据"""
    doc = docx.Document(file_path)
    table = doc.tables[5]  # 第6个表格（索引5）
    
    continuous_data = []
    # 从第2行开始提取（跳过表头）
    for row_idx in range(1, len(table.rows)):
        row = table.rows[row_idx]
        if len(row.cells) >= 4:
            condition_name = row.cells[0].text.strip()
            current_setting = row.cells[1].text.strip()
            previous_setting = row.cells[2].text.strip()
            current_status = row.cells[3].text.strip()
            
            continuous_data.append({
                'condition_name': condition_name,
                'current_setting': current_setting,
                'previous_setting': previous_setting,
                'current_status': current_status
            })
    
    print(f'✅ 提取持续条件: {len(continuous_data)}条')
    return continuous_data

def extract_loan_condition_data(file_path):
    """提取贷款条件数据"""
    doc = docx.Document(file_path)
    table = doc.tables[6]  # 第7个表格（索引6）
    
    loan_conditions = []
    for row_idx in range(len(table.rows)):
        row = table.rows[row_idx]
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            loan_conditions.append(f"{number}. {content}")
    
    print(f'✅ 提取贷款条件: {len(loan_conditions)}条')
    return loan_conditions

def replace_target_file(file_path, precondition_data, continuous_data, loan_condition_data):
    """替换目标文件内容"""
    doc = docx.Document(file_path)
    
    # 查找并替换表格内容
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                
                # 替换用信前提条件
                if '用信前提条件' in cell_text and '本次设置' in cell_text:
                    print(f'🔄 替换用信前提条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    cell.text = precondition_data
                
                # 替换持续条件（需要重建整个表格结构）
                elif '持续条件' in cell_text and '持续的评级水平' in cell_text:
                    print(f'🔄 替换持续条件表格 (表格{table_idx+1})')
                    replace_continuous_conditions_table(table, continuous_data)
                
                # 替换贷款条件
                elif '贷款条件' in cell_text and ('担保措施' in cell_text or '贸易背景' in cell_text):
                    print(f'🔄 替换贷款条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    # 将贷款条件合并为一个文本
                    loan_text = '\n'.join(loan_condition_data)
                    cell.text = loan_text
    
    # 保存文件
    output_path = file_path.parent / f'{file_path.stem}_updated{file_path.suffix}'
    doc.save(output_path)
    print(f'💾 保存到: {output_path}')

def replace_continuous_conditions_table(table, continuous_data):
    """替换持续条件表格"""
    # 清空现有行（保留表头）
    while len(table.rows) > 1:
        table._element.remove(table.rows[-1]._element)
    
    # 添加新的数据行
    for data in continuous_data:
        row = table.add_row()
        row.cells[0].text = data['condition_name']
        row.cells[1].text = data['current_setting']
        row.cells[2].text = data['previous_setting']
        row.cells[3].text = data['current_status']

if __name__ == "__main__":
    replace_implementation_table()
