#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档内容提取器 - 从申报书文档中提取关键内容
根据用户要求：
1. 从业务申报书搜索关键字"担保措施落实计划："提取整个表格
2. 从额度申报书提取持续条件和前提条件
"""

import docx
import re
from pathlib import Path
from datetime import datetime

class DocumentContentExtractor:
    """文档内容提取器"""
    
    def __init__(self):
        """初始化"""
        self.base_dir = Path(__file__).parent
        self.test_output_dir = self.base_dir / "test_output"
        self.test_output_dir.mkdir(exist_ok=True)
    
    def extract_from_business_report(self, doc_path):
        """从业务申报书中提取担保措施落实计划表格"""
        print(f"🔍 开始从业务申报书提取内容: {doc_path}")
        
        try:
            doc = docx.Document(doc_path)
            
            # 搜索关键字"担保措施落实计划："
            guarantee_content = []
            found_guarantee_section = False
            
            # 在段落中搜索
            for para in doc.paragraphs:
                text = para.text.strip()
                if "担保措施落实计划" in text:
                    print(f"✅ 找到担保措施落实计划段落: {text[:50]}...")
                    found_guarantee_section = True
                    guarantee_content.append(text)
            
            # 在表格中搜索
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text = cell.text.strip()
                        if "担保措施落实计划" in text:
                            print(f"✅ 找到担保措施落实计划表格")
                            found_guarantee_section = True
                            
                            # 提取整个表格内容
                            table_content = []
                            for table_row in table.rows:
                                row_content = []
                                for table_cell in table_row.cells:
                                    row_content.append(table_cell.text.strip())
                                table_content.append(row_content)
                            
                            guarantee_content.append({
                                "type": "table",
                                "content": table_content
                            })
                            break
            
            if found_guarantee_section:
                print(f"✅ 成功提取担保措施落实计划内容")
                return {
                    "担保措施落实计划": guarantee_content
                }
            else:
                print(f"❌ 未找到担保措施落实计划相关内容")
                return None
                
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return None
    
    def extract_from_quota_report(self, doc_path):
        """从额度申报书中提取持续条件和前提条件"""
        print(f"🔍 开始从额度申报书提取内容: {doc_path}")
        
        try:
            doc = docx.Document(doc_path)
            
            extracted_content = {
                "持续条件": [],
                "前提条件": []
            }
            
            # 搜索持续条件
            for para in doc.paragraphs:
                text = para.text.strip()
                if any(keyword in text for keyword in ["持续条件", "持续的评级", "资产负债率", "流动比率"]):
                    print(f"✅ 找到持续条件相关内容: {text[:50]}...")
                    extracted_content["持续条件"].append(text)
            
            # 搜索前提条件
            for para in doc.paragraphs:
                text = para.text.strip()
                if any(keyword in text for keyword in ["前提条件", "用信前提", "支用条件"]):
                    print(f"✅ 找到前提条件相关内容: {text[:50]}...")
                    extracted_content["前提条件"].append(text)
            
            # 在表格中搜索
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text = cell.text.strip()
                        if any(keyword in text for keyword in ["持续条件", "前提条件"]):
                            print(f"✅ 找到条件相关表格")
                            
                            # 提取整个表格内容
                            table_content = []
                            for table_row in table.rows:
                                row_content = []
                                for table_cell in table_row.cells:
                                    row_content.append(table_cell.text.strip())
                                table_content.append(row_content)
                            
                            if "持续条件" in text:
                                extracted_content["持续条件"].append({
                                    "type": "table",
                                    "content": table_content
                                })
                            elif "前提条件" in text:
                                extracted_content["前提条件"].append({
                                    "type": "table", 
                                    "content": table_content
                                })
            
            return extracted_content
                
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return None
    
    def create_extracted_conditions_document(self, business_content=None, quota_content=None, company_name="公司"):
        """根据提取的内容创建条件汇总文档"""
        print("🔧 开始根据提取内容创建条件汇总文档...")
        
        try:
            from docx import Document
            from docx.shared import Inches, RGBColor
            from docx.enum.table import WD_TABLE_ALIGNMENT
            
            # 创建新文档
            doc = Document()
            
            # 添加标题
            title = doc.add_heading(f'{company_name}条件汇总表（提取版）', 0)
            
            # 1. 添加持续条件部分
            if quota_content and quota_content.get("持续条件"):
                doc.add_heading('一、持续条件', level=1)
                for item in quota_content["持续条件"]:
                    if isinstance(item, dict) and item.get("type") == "table":
                        # 创建表格
                        table_data = item["content"]
                        if table_data:
                            table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                            table.style = 'Table Grid'
                            
                            for i, row_data in enumerate(table_data):
                                for j, cell_data in enumerate(row_data):
                                    table.rows[i].cells[j].text = cell_data
                                    # 设置为绿色
                                    for para in table.rows[i].cells[j].paragraphs:
                                        for run in para.runs:
                                            run.font.color.rgb = RGBColor(0, 128, 0)
                    else:
                        # 添加段落
                        para = doc.add_paragraph(str(item))
                        for run in para.runs:
                            run.font.color.rgb = RGBColor(0, 128, 0)
            
            # 2. 添加前提条件部分
            if quota_content and quota_content.get("前提条件"):
                doc.add_heading('二、前提条件', level=1)
                for item in quota_content["前提条件"]:
                    if isinstance(item, dict) and item.get("type") == "table":
                        # 创建表格
                        table_data = item["content"]
                        if table_data:
                            table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                            table.style = 'Table Grid'
                            
                            for i, row_data in enumerate(table_data):
                                for j, cell_data in enumerate(row_data):
                                    table.rows[i].cells[j].text = cell_data
                                    # 设置为绿色
                                    for para in table.rows[i].cells[j].paragraphs:
                                        for run in para.runs:
                                            run.font.color.rgb = RGBColor(0, 128, 0)
                    else:
                        # 添加段落
                        para = doc.add_paragraph(str(item))
                        for run in para.runs:
                            run.font.color.rgb = RGBColor(0, 128, 0)
            
            # 3. 添加担保措施落实计划部分
            if business_content and business_content.get("担保措施落实计划"):
                doc.add_heading('三、担保措施落实计划', level=1)
                for item in business_content["担保措施落实计划"]:
                    if isinstance(item, dict) and item.get("type") == "table":
                        # 创建表格
                        table_data = item["content"]
                        if table_data:
                            table = doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                            table.style = 'Table Grid'
                            
                            for i, row_data in enumerate(table_data):
                                for j, cell_data in enumerate(row_data):
                                    table.rows[i].cells[j].text = cell_data
                                    # 设置为绿色
                                    for para in table.rows[i].cells[j].paragraphs:
                                        for run in para.runs:
                                            run.font.color.rgb = RGBColor(0, 128, 0)
                    else:
                        # 添加段落
                        para = doc.add_paragraph(str(item))
                        for run in para.runs:
                            run.font.color.rgb = RGBColor(0, 128, 0)
            
            # 保存文档
            output_path = self.test_output_dir / f"提取版条件汇总表_{company_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            doc.save(output_path)
            
            print(f"\n✅ 提取版条件汇总文档生成完成: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def process_documents(self, business_report_path=None, quota_report_path=None, company_name="公司"):
        """处理申报书文档并生成条件汇总表"""
        print("=" * 80)
        print("📋 文档内容提取器")
        print("=" * 80)
        print("🎯 提取策略:")
        print("  📄 业务申报书 → 搜索'担保措施落实计划：'")
        print("  📄 额度申报书 → 搜索持续条件、前提条件")
        print("=" * 80)
        
        business_content = None
        quota_content = None
        
        # 提取业务申报书内容
        if business_report_path and Path(business_report_path).exists():
            business_content = self.extract_from_business_report(business_report_path)
        
        # 提取额度申报书内容
        if quota_report_path and Path(quota_report_path).exists():
            quota_content = self.extract_from_quota_report(quota_report_path)
        
        # 生成汇总文档
        if business_content or quota_content:
            result = self.create_extracted_conditions_document(
                business_content=business_content,
                quota_content=quota_content,
                company_name=company_name
            )
            
            if result:
                print(f"\n🎉 文档处理完成！")
                print(f"📄 输出文件: {result}")
                return result
        
        print(f"\n❌ 未能提取到有效内容")
        return None

def main():
    """主函数"""
    extractor = DocumentContentExtractor()
    
    # 示例：处理文档（需要提供实际的文档路径）
    # business_path = "path/to/business_report.docx"
    # quota_path = "path/to/quota_report.docx"
    # result = extractor.process_documents(business_path, quota_path, "示例公司")
    
    print("📋 文档内容提取器已准备就绪")
    print("🔧 使用方法:")
    print("  extractor = DocumentContentExtractor()")
    print("  result = extractor.process_documents(business_path, quota_path, company_name)")

if __name__ == "__main__":
    main()
