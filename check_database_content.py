#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库内容检查脚本
查看SQLite数据库的表结构和数据内容
"""

import sqlite3
import os

def check_database():
    db_path = 'database/enterprise_service.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 查看所有表
        print("=" * 60)
        print("数据库表列表:")
        print("=" * 60)
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print("数据库中没有表")
            return
            
        for table in tables:
            print(f"- {table[0]}")
        
        print()
        
        # 2. 查看每个表的结构和数据
        for table in tables:
            table_name = table[0]
            print("=" * 60)
            print(f"表: {table_name}")
            print("=" * 60)
            
            # 查看表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("表结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
            
            # 查看数据
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"\n数据行数: {count}")
            
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5;")
                rows = cursor.fetchall()
                
                print("\n前5行数据:")
                for i, row in enumerate(rows, 1):
                    print(f"  行{i}: {row}")
            
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"查看数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
