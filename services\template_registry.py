#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板注册模块
负责模板路径管理、字段配置、模板发现
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, List, Optional

from utils.logger import SystemLogger


class TemplateRegistry:
    """模板注册管理类 - 读取template_config.yaml并提供模板信息查询功能"""

    def __init__(self, config_path: Optional[str] = None):
        """初始化模板注册器"""
        self.logger = SystemLogger("template_registry")

        # 设置配置文件路径
        self.config_path = config_path or self._get_default_config_path()
        self.templates_root = Path(__file__).parent.parent / "templates"

        # 加载配置
        self.config = self._load_config()

        # 验证模板配置
        self._validate_templates()

        self.logger.info("TemplateRegistry 初始化完成")

    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "template_config.yaml")

    def _load_config(self) -> Dict[str, Any]:
        """加载模板配置"""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    templates = config.get('templates', {})
                    self.logger.info(f"模板配置加载成功，共 {len(templates)} 个模板")
                    return config or {}
            else:
                self.logger.warning("模板配置文件不存在，使用默认配置")
                return self._get_default_config()
        except Exception as e:
            self.logger.error(f"加载模板配置失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认模板配置"""
        return {
            'version': '2.0',
            'categories': {
                'deposit_services': {
                    'name': '协定存款服务',
                    'description': '协定存款相关文档模板',
                    'icon': '🏦'
                },
                'contract_disbursement': {
                    'name': '合同放款',
                    'description': '合同放款相关文档模板',
                    'icon': '📋'
                },
                'customer_engagement': {
                    'name': '客户接洽',
                    'description': '客户接洽相关文档模板',
                    'icon': '🤝'
                }
            },
            'templates': {},
            'global_settings': {
                'output_base_directory': 'output',
                'file_naming_pattern': '{template_id}_{company_name}_{timestamp}',
                'timestamp_format': '%Y%m%d_%H%M%S'
            }
        }

    def _validate_templates(self):
        """验证模板配置的正确性"""
        try:
            templates = self.config.get('templates', {})
            valid_count = 0
            invalid_count = 0

            for template_id, template_config in templates.items():
                if self._validate_single_template(template_id, template_config):
                    valid_count += 1
                else:
                    invalid_count += 1

            self.logger.info(f"模板配置验证完成: {valid_count}个有效, {invalid_count}个无效")

        except Exception as e:
            self.logger.error(f"模板配置验证失败: {e}")

    def _validate_single_template(self, template_id: str, template_config: Dict[str, Any]) -> bool:
        """验证单个模板配置"""
        try:
            # 检查必要字段
            required_fields = ['name', 'template_file']
            for field in required_fields:
                if field not in template_config:
                    self.logger.warning(f"模板 {template_id} 缺少必要字段: {field}")
                    return False

            # 检查模板文件是否存在
            template_file_config = template_config.get('template_file', {})
            template_path = template_file_config.get('path', '')

            if not template_path:
                self.logger.warning(f"模板 {template_id} 未指定模板文件路径")
                return False

            # 检查文件是否存在（相对于项目根目录）
            project_root = Path(__file__).parent.parent
            full_path = project_root / template_path

            if not full_path.exists():
                self.logger.warning(f"模板 {template_id} 文件不存在: {template_path}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证模板 {template_id} 失败: {e}")
            return False

    def get_all_template_ids(self) -> List[str]:
        """获取所有模板ID列表"""
        try:
            templates = self.config.get('templates', {})
            return list(templates.keys())
        except Exception as e:
            self.logger.error(f"获取模板ID列表失败: {e}")
            return []

    def get_all_template_names(self) -> Dict[str, str]:
        """获取所有模板ID和名称的映射"""
        try:
            templates = self.config.get('templates', {})
            name_mapping = {}

            for template_id, template_config in templates.items():
                name_mapping[template_id] = template_config.get('name', template_id)

            return name_mapping
        except Exception as e:
            self.logger.error(f"获取模板名称映射失败: {e}")
            return {}

    def get_template_info(self, template_id: str) -> Optional[Dict[str, Any]]:
        """根据模板ID获取模板的所有配置信息"""
        try:
            templates = self.config.get('templates', {})

            # 直接查找模板ID
            if template_id in templates:
                template_config = templates[template_id].copy()

                # 添加验证信息
                template_config['is_valid'] = self._validate_single_template(template_id, template_config)

                return template_config

            # 在所有模板中查找
            for template_name, template_config in templates.items():
                if template_config.get('id') == template_id:
                    config_copy = template_config.copy()
                    config_copy['is_valid'] = self._validate_single_template(template_name, template_config)
                    return config_copy

            self.logger.warning(f"未找到模板: {template_id}")
            return None

        except Exception as e:
            self.logger.error(f"获取模板信息失败: {e}")
            return None

    def get_available_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有可用模板，按分类组织"""
        try:
            available_templates = {}
            templates = self.config.get('templates', {})
            categories = self.config.get('categories', {})

            # 按分类组织模板
            for template_id, template_config in templates.items():
                category = template_config.get('category', 'other')
                category_info = categories.get(category, {'name': category})
                category_name = category_info.get('name', category)

                # 验证模板是否有效
                if self._validate_single_template(template_id, template_config):
                    if category_name not in available_templates:
                        available_templates[category_name] = []

                    template_info = {
                        'id': template_id,
                        'name': template_config.get('name', template_id),
                        'description': template_config.get('description', ''),
                        'category': category,
                        'file_type': template_config.get('template_file', {}).get('type', 'docx'),
                        'required_fields': template_config.get('fields', {}).get('required', []),
                        'ai_fields': template_config.get('fields', {}).get('ai_generated', [])
                    }
                    available_templates[category_name].append(template_info)

            return available_templates

        except Exception as e:
            self.logger.error(f"获取可用模板失败: {e}")
            return {}

    def validate_template_config(self, template_id: str) -> Dict[str, Any]:
        """校验模板配置是否正确"""
        try:
            template_config = self.get_template_info(template_id)

            if not template_config:
                return {
                    'valid': False,
                    'errors': [f'模板不存在: {template_id}'],
                    'warnings': []
                }

            errors = []
            warnings = []

            # 检查必要字段
            required_config_fields = ['name', 'template_file', 'fields']
            for field in required_config_fields:
                if field not in template_config:
                    errors.append(f'缺少必要配置字段: {field}')

            # 检查模板文件
            template_file_config = template_config.get('template_file', {})
            template_path = template_file_config.get('path', '')

            if not template_path:
                errors.append('未指定模板文件路径')
            else:
                project_root = Path(__file__).parent.parent
                full_path = project_root / template_path
                if not full_path.exists():
                    errors.append(f'模板文件不存在: {template_path}')

            # 检查字段配置
            fields_config = template_config.get('fields', {})
            if not fields_config.get('required'):
                warnings.append('未定义必填字段')

            # 检查字段映射
            field_mappings = template_config.get('field_mappings', {})
            if not field_mappings:
                warnings.append('未定义字段映射')

            is_valid = len(errors) == 0

            return {
                'valid': is_valid,
                'errors': errors,
                'warnings': warnings,
                'summary': f"配置{'有效' if is_valid else '无效'}: {len(errors)}个错误, {len(warnings)}个警告"
            }

        except Exception as e:
            self.logger.error(f"校验模板配置失败: {e}")
            return {
                'valid': False,
                'errors': [f'校验异常: {e}'],
                'warnings': [],
                'summary': f'校验异常: {e}'
            }
    
    def register_template(self, category: str, template_id: str, 
                         template_config: Dict[str, Any]) -> bool:
        """手动注册模板"""
        try:
            # 确保分类存在
            if category not in self.config.get('templates', {}):
                self.config.setdefault('templates', {})[category] = {}
            
            # 验证模板文件
            template_path = Path(template_config['template_path'])
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            # 注册模板
            self.config['templates'][category][template_id] = template_config
            
            # 保存配置
            self._save_config()
            
            self.logger.info(f"手动注册模板成功: {category}.{template_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"手动注册模板失败: {e}")
            return False
    
    def unregister_template(self, category: str, template_id: str) -> bool:
        """注销模板"""
        try:
            if (category in self.config.get('templates', {}) and 
                template_id in self.config['templates'][category]):
                
                del self.config['templates'][category][template_id]
                
                # 如果分类为空，删除分类
                if not self.config['templates'][category]:
                    del self.config['templates'][category]
                
                # 保存配置
                self._save_config()
                
                self.logger.info(f"注销模板成功: {category}.{template_id}")
                return True
            else:
                self.logger.warning(f"模板不存在: {category}.{template_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"注销模板失败: {e}")
            return False
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            config_file = Path(self.config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, sort_keys=False)
            
            self.logger.info("模板配置保存成功")
            
        except Exception as e:
            self.logger.error(f"保存模板配置失败: {e}")
    
    def get_field_mappings(self, category: str, template_id: str) -> Dict[str, str]:
        """获取字段映射"""
        try:
            template_config = self.get_template_config(category, template_id)
            if template_config:
                return template_config.get('field_mappings', {})
            else:
                return self.config['field_mappings'].get('common', {})
                
        except Exception as e:
            self.logger.error(f"获取字段映射失败: {e}")
            return {}
