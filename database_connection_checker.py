#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专家数据库连接检查器 - 确认数据库结构和数据
"""

import sqlite3
from pathlib import Path
import json

class DatabaseConnectionChecker:
    """数据库连接检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
    def check_database_exists(self):
        """检查数据库文件是否存在"""
        print("🔍 检查数据库文件...")
        print(f"数据库路径: {self.db_path}")
        
        if self.db_path.exists():
            print("✅ 数据库文件存在")
            print(f"文件大小: {self.db_path.stat().st_size} 字节")
            return True
        else:
            print("❌ 数据库文件不存在")
            return False
    
    def check_database_structure(self):
        """检查数据库表结构"""
        print("\n📋 检查数据库表结构...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"数据库包含 {len(tables)} 个表:")
            
            table_info = {}
            for table in tables:
                table_name = table[0]
                print(f"\n📊 表: {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                
                print("  列结构:")
                column_info = []
                for col in columns:
                    col_info = {
                        'name': col[1],
                        'type': col[2],
                        'not_null': bool(col[3]),
                        'default': col[4],
                        'primary_key': bool(col[5])
                    }
                    column_info.append(col_info)
                    print(f"    - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")
                
                # 获取数据行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                row_count = cursor.fetchone()[0]
                print(f"  数据行数: {row_count}")
                
                table_info[table_name] = {
                    'columns': column_info,
                    'row_count': row_count
                }
            
            conn.close()
            return table_info
            
        except Exception as e:
            print(f"❌ 检查数据库结构失败: {e}")
            return None
    
    def check_enterprises_data(self):
        """检查企业数据"""
        print("\n👥 检查企业数据...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查enterprises表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='enterprises';")
            if not cursor.fetchone():
                print("❌ enterprises表不存在")
                conn.close()
                return None
            
            # 获取所有企业数据
            cursor.execute("SELECT * FROM enterprises;")
            enterprises = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(enterprises);")
            columns = [col[1] for col in cursor.fetchall()]
            
            print(f"企业数据表包含 {len(enterprises)} 条记录:")
            print(f"列名: {columns}")
            
            # 显示前几条数据
            for i, enterprise in enumerate(enterprises[:3]):
                print(f"\n  企业 {i+1}:")
                for j, value in enumerate(enterprise):
                    print(f"    {columns[j]}: {value}")
            
            if len(enterprises) > 3:
                print(f"  ... 还有 {len(enterprises) - 3} 条记录")
            
            # 查找中科卓尔
            cursor.execute("SELECT * FROM enterprises WHERE name LIKE '%卓尔%' OR name LIKE '%中科%';")
            zhuoer_data = cursor.fetchall()
            
            if zhuoer_data:
                print(f"\n🎯 找到中科卓尔相关数据 ({len(zhuoer_data)} 条):")
                for enterprise in zhuoer_data:
                    print(f"  - {enterprise[1]} (联系人: {enterprise[2]})")
            else:
                print("\n⚠️ 未找到中科卓尔相关数据")
            
            conn.close()
            return enterprises
            
        except Exception as e:
            print(f"❌ 检查企业数据失败: {e}")
            return None
    
    def test_database_connection(self):
        """测试数据库连接"""
        print("\n🔗 测试数据库连接...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 执行简单查询
            cursor.execute("SELECT sqlite_version();")
            version = cursor.fetchone()[0]
            print(f"✅ SQLite版本: {version}")
            
            # 测试事务
            cursor.execute("BEGIN;")
            cursor.execute("ROLLBACK;")
            print("✅ 事务测试通过")
            
            conn.close()
            print("✅ 数据库连接测试成功")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False
    
    def generate_connection_config(self):
        """生成连接配置"""
        print("\n⚙️ 生成数据库连接配置...")
        
        config = {
            "database_path": str(self.db_path),
            "database_exists": self.db_path.exists(),
            "connection_string": f"sqlite:///{self.db_path}",
            "recommended_settings": {
                "timeout": 30,
                "check_same_thread": False,
                "isolation_level": None
            }
        }
        
        # 保存配置
        config_file = self.project_root / "database_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存到: {config_file}")
        return config
    
    def comprehensive_check(self):
        """综合检查"""
        print("🎯 专家数据库连接综合检查")
        print("=" * 60)
        
        # 1. 检查文件存在
        if not self.check_database_exists():
            print("\n❌ 数据库文件不存在，无法继续")
            return False
        
        # 2. 测试连接
        if not self.test_database_connection():
            print("\n❌ 数据库连接失败，无法继续")
            return False
        
        # 3. 检查结构
        table_info = self.check_database_structure()
        if not table_info:
            print("\n❌ 无法获取数据库结构")
            return False
        
        # 4. 检查企业数据
        enterprises = self.check_enterprises_data()
        
        # 5. 生成配置
        config = self.generate_connection_config()
        
        # 6. 总结报告
        print(f"\n📊 检查总结:")
        print("=" * 40)
        print(f"✅ 数据库文件: 存在")
        print(f"✅ 数据库连接: 正常")
        print(f"✅ 数据表数量: {len(table_info)}")
        print(f"✅ 企业数据: {len(enterprises) if enterprises else 0} 条")
        
        # 关键确认点
        print(f"\n🔍 关键确认点:")
        print("=" * 40)
        
        if 'enterprises' in table_info:
            print(f"✅ enterprises表存在 ({table_info['enterprises']['row_count']} 条记录)")
        else:
            print(f"❌ enterprises表不存在")
        
        # 检查必要的列
        if 'enterprises' in table_info:
            required_columns = ['id', 'name', 'contact_person', 'phone', 'address']
            existing_columns = [col['name'] for col in table_info['enterprises']['columns']]
            
            print(f"\n📋 enterprises表列检查:")
            for col in required_columns:
                if col in existing_columns:
                    print(f"  ✅ {col}")
                else:
                    print(f"  ❌ {col} (缺失)")
        
        return True

def main():
    """主函数"""
    checker = DatabaseConnectionChecker()
    success = checker.comprehensive_check()
    
    if success:
        print(f"\n🎉 数据库检查完成！")
        print(f"💡 建议:")
        print(f"  1. 数据库连接正常，可以在桌面应用中使用")
        print(f"  2. 如果有缺失的列，需要先更新数据库结构")
        print(f"  3. 确认中科卓尔数据是否完整")
    else:
        print(f"\n❌ 数据库检查失败！")
        print(f"💡 建议:")
        print(f"  1. 检查数据库文件路径是否正确")
        print(f"  2. 确认数据库文件是否损坏")
        print(f"  3. 检查文件权限")

if __name__ == "__main__":
    main()
