#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信贷业务申请书生成器
支持自动替换企业信息和半自动输入贷款信息
"""

import openpyxl
from openpyxl.styles import PatternFill
import sqlite3
from pathlib import Path
import logging
from datetime import datetime
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CreditApplicationGenerator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "信贷业务申请书.xlsx"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        
        # 高亮样式
        self.green_fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")  # 自动填充
        self.yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")  # 半自动
        
    def generate_application(self, company_id, loan_info=None):
        """生成信贷业务申请书"""
        logger.info(f"🏦 开始生成信贷业务申请书，企业ID: {company_id}")
        
        try:
            # 1. 获取企业数据
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到企业数据: {company_id}")
            
            # 2. 准备输出目录
            self.output_dir.mkdir(exist_ok=True)
            
            # 3. 复制模板文件
            output_path = self.output_dir / f"信贷业务申请书_{company_data['company_name']}.xlsx"
            shutil.copy2(self.template_path, output_path)
            
            # 4. 加载工作簿
            workbook = openpyxl.load_workbook(output_path)
            worksheet = workbook.active
            
            # 5. 自动替换企业信息
            self._fill_company_info(worksheet, company_data)
            
            # 6. 自动生成日期
            self._fill_application_date(worksheet)
            
            # 7. 半自动填入贷款信息（如果提供）
            if loan_info:
                self._fill_loan_info(worksheet, loan_info)
            
            # 8. 保存文件
            workbook.save(output_path)
            
            logger.info(f"✅ 信贷业务申请书生成完成: {output_path}")
            return output_path, self._generate_summary(company_data, loan_info)
            
        except Exception as e:
            logger.error(f"❌ 生成失败: {e}")
            raise
    
    def _get_company_data(self, company_id):
        """从数据库获取企业数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    company_name, unified_social_credit_code, legal_representative,
                    registration_date, registered_capital, business_scope, business_description,
                    contact_phone, finance_manager_name, finance_manager_phone,
                    total_assets, total_liabilities
                FROM companies 
                WHERE id = ?
            """, (company_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'company_name': result[0],
                    'unified_social_credit_code': result[1],
                    'legal_representative': result[2],
                    'registration_date': result[3],
                    'registered_capital': result[4],
                    'business_scope': result[5],
                    'business_description': result[6],
                    'contact_phone': result[7],
                    'finance_manager_name': result[8],
                    'finance_manager_phone': result[9],
                    'total_assets': result[10],
                    'total_liabilities': result[11]
                }
            return None
            
        except Exception as e:
            logger.error(f"获取企业数据失败: {e}")
            return None
    
    def _fill_company_info(self, worksheet, company_data):
        """填充企业基本信息（自动替换）"""
        logger.info("📋 填充企业基本信息...")
        
        # 企业基本信息映射
        company_mappings = [
            ('B4', company_data['company_name'], '企业名称'),
            ('H4', company_data['unified_social_credit_code'], '统一社会信用代码'),
            ('B8', company_data['legal_representative'], '法定代表人'),
            ('E8', company_data['contact_phone'], '联系电话'),
            ('E7', company_data['registration_date'], '注册时间'),
            ('H7', company_data['registered_capital'], '注册资本'),
            ('B11', company_data['business_scope'], '经营范围'),
            ('B12', company_data['business_description'], '主营业务描述'),
            ('B10', company_data['finance_manager_name'], '财务主管'),
            ('E10', company_data['finance_manager_phone'], '财务主管电话'),
            ('B13', company_data['total_assets'], '资产总额'),
            ('E13', company_data['total_liabilities'], '负债总额')
        ]
        
        filled_count = 0
        for cell_address, value, description in company_mappings:
            if value is not None:
                cell = worksheet[cell_address]
                cell.value = value
                cell.fill = self.green_fill  # 绿色表示自动填充
                filled_count += 1
                logger.info(f"   ✅ {cell_address}: {description} = {value}")
            else:
                logger.warning(f"   ⚠️ {cell_address}: {description} 数据缺失")
        
        logger.info(f"   📊 企业信息填充完成: {filled_count}/12 个字段")
    
    def _fill_application_date(self, worksheet):
        """填充申请日期（自动生成）"""
        logger.info("📅 填充申请日期...")
        
        now = datetime.now()
        date_mappings = [
            ('D33', f"{now.year}年", '申请年份'),
            ('E33', f"{now.month:02d}月", '申请月份'),
            ('F33', f"{now.day:02d}日", '申请日期')
        ]
        
        for cell_address, value, description in date_mappings:
            cell = worksheet[cell_address]
            cell.value = value
            cell.fill = self.green_fill  # 绿色表示自动填充
            logger.info(f"   ✅ {cell_address}: {description} = {value}")
        
        logger.info("   📅 申请日期填充完成")
    
    def _fill_loan_info(self, worksheet, loan_info):
        """填充贷款信息（半自动）"""
        logger.info("💰 填充贷款信息...")
        
        loan_mappings = [
            ('B14', loan_info.get('loan_type'), '贷款类型'),
            ('B15', loan_info.get('loan_amount_text'), '贷款金额大写'),
            ('E15', loan_info.get('loan_term'), '贷款期限'),
            ('B17', loan_info.get('loan_purpose'), '贷款用途')
        ]
        
        filled_count = 0
        for cell_address, value, description in loan_mappings:
            if value is not None:
                cell = worksheet[cell_address]
                cell.value = value
                cell.fill = self.yellow_fill  # 黄色表示半自动填充
                filled_count += 1
                logger.info(f"   ✅ {cell_address}: {description} = {value}")
            else:
                logger.info(f"   ⚠️ {cell_address}: {description} 未提供")
        
        logger.info(f"   💰 贷款信息填充完成: {filled_count}/4 个字段")
    
    def _generate_summary(self, company_data, loan_info):
        """生成填充摘要"""
        summary = {
            'company_name': company_data['company_name'],
            'auto_filled_fields': 15,  # 12个企业信息 + 3个日期
            'manual_fields_needed': 4 if not loan_info else 0,
            'completion_rate': '100%' if loan_info else '79%',
            'missing_fields': [] if loan_info else ['贷款类型', '贷款金额', '贷款期限', '贷款用途']
        }
        return summary

def main():
    """测试函数"""
    print("🏦 信贷业务申请书生成器测试")
    print("="*50)
    
    generator = CreditApplicationGenerator()
    
    # 测试神光光学
    company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
    
    # 示例贷款信息
    loan_info = {
        'loan_type': '流动资金贷款',
        'loan_amount_text': '人民币壹仟万元整',
        'loan_term': '12个月',
        'loan_purpose': '用于公司置换其他金融机构流动资金性质借款需求'
    }
    
    try:
        output_path, summary = generator.generate_application(company_id, loan_info)
        
        print(f"✅ 生成成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"📊 自动填充: {summary['auto_filled_fields']} 个字段")
        print(f"📈 完成率: {summary['completion_rate']}")
        
        if summary['missing_fields']:
            print(f"⚠️ 缺失字段: {', '.join(summary['missing_fields'])}")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
