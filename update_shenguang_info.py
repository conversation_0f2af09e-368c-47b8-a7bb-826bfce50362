#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新神光光学集团有限公司的法人代表和注册地址信息
"""

import requests
import json

def update_shenguang_info():
    """更新神光光学集团有限公司信息"""
    
    # 神光光学集团有限公司ID
    company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
    api_url = f"http://localhost:5000/api/company/{company_id}"
    
    # 更新数据
    update_data = {
        "registered_address": "四川省成都市成华区建设南街9号2层",
        "legal_representative": "贾秉炜",
        "operated_by_user_id": "12345678-1234-1234-1234-123456789012",
        "operation_reason": "补充神光光学集团有限公司法人代表和注册地址信息"
    }
    
    print("🔄 更新神光光学集团有限公司信息...")
    print("=" * 60)
    print(f"🏢 公司ID: {company_id}")
    print(f"👤 法定代表人: {update_data['legal_representative']}")
    print(f"📍 注册地址: {update_data['registered_address']}")
    print()
    
    try:
        # 发送PUT请求更新公司信息
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        print("📤 发送更新请求...")
        response = requests.put(
            api_url,
            json=update_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📡 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 更新成功!")
            print("📄 响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 验证更新结果
            verify_update(company_id)
            
        else:
            print("❌ 更新失败!")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API调用异常: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def verify_update(company_id):
    """验证更新结果"""
    print("\n🔍 验证更新结果...")
    
    try:
        verify_url = f"http://localhost:5000/api/company/{company_id}"
        response = requests.get(verify_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            company_data = result.get('data', {})
            
            print("✅ 验证成功! 更新后的公司信息:")
            print(f"   🏢 公司名称: {company_data.get('company_name')}")
            print(f"   🆔 信用代码: {company_data.get('unified_social_credit_code')}")
            print(f"   👤 法定代表人: {company_data.get('legal_representative')}")
            print(f"   📍 注册地址: {company_data.get('registered_address')}")
            print(f"   📋 业务描述: {company_data.get('business_description', '')[:50]}...")
            
            # 检查关键字段是否正确更新
            if company_data.get('legal_representative') == "贾秉炜":
                print("   ✅ 法定代表人更新正确")
            else:
                print("   ❌ 法定代表人更新失败")
                
            if "四川省成都市成华区建设南街9号2层" in str(company_data.get('registered_address', '')):
                print("   ✅ 注册地址更新正确")
            else:
                print("   ❌ 注册地址更新失败")
                
        else:
            print(f"⚠️ 验证失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"⚠️ 验证过程出错: {e}")

def show_before_after():
    """显示更新前后对比"""
    company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
    
    print("📊 更新前后对比:")
    print("-" * 60)
    
    try:
        response = requests.get(f"http://localhost:5000/api/company/{company_id}", timeout=10)
        
        if response.status_code == 200:
            data = response.json()["data"]
            
            print("🔄 更新后的完整信息:")
            print(f"   🏢 公司名称: {data.get('company_name')}")
            print(f"   🆔 统一社会信用代码: {data.get('unified_social_credit_code')}")
            print(f"   👤 法定代表人: {data.get('legal_representative')}")
            print(f"   📍 注册地址: {data.get('registered_address')}")
            print(f"   📞 联系电话: {data.get('contact_phone', '待补充')}")
            print(f"   📧 联系邮箱: {data.get('contact_email', '待补充')}")
            print(f"   📋 业务描述: {data.get('business_description')}")
            
        else:
            print(f"❌ 获取公司信息失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取信息异常: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 神光光学集团有限公司信息更新")
    print("📋 任务: 补充法人代表和注册地址")
    print("=" * 60)
    
    # 执行更新
    update_shenguang_info()
    
    print("\n" + "=" * 60)
    print("🎉 信息更新完成!")
    
    # 显示更新后的完整信息
    show_before_after()
    
    print("=" * 60)
    print("✅ 神光光学集团有限公司信息已完善")
    print("🚀 现在可以为该公司提供完整的企业服务")
    print("=" * 60)
