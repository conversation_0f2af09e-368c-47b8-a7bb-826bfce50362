#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板引擎模块
负责Word/Excel文档处理、字段替换、格式保持
"""

import docx
import shutil
import json
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from docx.shared import Pt
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import RGBColor

try:
    import openpyxl
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

from utils.logger import SystemLogger


class TemplateEngine:
    """模板引擎类 - 支持Word和Excel模板的字段替换，保持原始格式"""

    def __init__(self, field_mapping_path: Optional[str] = None):
        """初始化模板引擎"""
        self.logger = SystemLogger("template_engine")
        self.yellow_color = WD_COLOR_INDEX.YELLOW

        # 加载字段映射配置
        self.field_mapping_path = field_mapping_path or self._get_default_field_mapping_path()
        self.field_dictionary = self._load_field_mapping()

        # 替换统计
        self.replacement_stats = {
            'total_replacements': 0,
            'successful_replacements': 0,
            'failed_replacements': 0,
            'processed_placeholders': []
        }

        self.logger.info("TemplateEngine 初始化完成")

    def _get_default_field_mapping_path(self) -> str:
        """获取默认字段映射配置路径"""
        project_root = Path(__file__).parent.parent
        return str(project_root / "config" / "field_mapping.json")

    def _load_field_mapping(self) -> Dict[str, Any]:
        """加载字段映射配置"""
        try:
            config_path = Path(self.field_mapping_path)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('field_dictionary', {})
            else:
                self.logger.warning("字段映射配置文件不存在")
                return {}
        except Exception as e:
            self.logger.error(f"加载字段映射失败: {e}")
            return {}

    def replace_in_word(self, template_path: str, output_path: str,
                       field_data: Dict[str, Any],
                       field_mappings: Dict[str, str]) -> Dict[str, Any]:
        """处理Word模板文件，进行字段替换并保持格式"""
        try:
            self.logger.info(f"开始处理Word模板: {template_path}")

            # 验证模板文件
            if not Path(template_path).exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            # 重置统计
            self._reset_stats()

            # 复制模板文件到输出路径
            shutil.copy2(template_path, output_path)

            # 加载Word文档
            doc = docx.Document(output_path)

            # 执行字段替换
            for field_name, field_value in field_data.items():
                # 获取该字段的所有占位符
                placeholders = self._get_field_placeholders(field_name, field_mappings)

                for placeholder in placeholders:
                    if placeholder:
                        # 格式化字段值
                        formatted_value = self._format_field_value(field_name, field_value)

                        # 在文档中替换
                        replacements = self._replace_text_in_document(
                            doc, placeholder, formatted_value
                        )

                        if replacements > 0:
                            self.replacement_stats['total_replacements'] += replacements
                            self.replacement_stats['processed_placeholders'].append({
                                'field': field_name,
                                'placeholder': placeholder,
                                'value': formatted_value,
                                'count': replacements
                            })
                            self.logger.debug(f"替换成功: {placeholder} -> {formatted_value} ({replacements}次)")

            # 保存文档
            doc.save(output_path)

            # 统计成功和失败的字段
            successful_fields = len([p for p in self.replacement_stats['processed_placeholders'] if p['count'] > 0])
            total_fields = len(field_data)

            result = {
                'success': True,
                'output_path': output_path,
                'total_replacements': self.replacement_stats['total_replacements'],
                'successful_fields': successful_fields,
                'total_fields': total_fields,
                'processed_placeholders': self.replacement_stats['processed_placeholders']
            }

            self.logger.info(f"Word模板处理完成: 替换{self.replacement_stats['total_replacements']}处，成功字段{successful_fields}/{total_fields}")
            return result

        except Exception as e:
            self.logger.error(f"Word模板处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'output_path': output_path,
                'total_replacements': 0,
                'successful_fields': 0,
                'total_fields': len(field_data) if field_data else 0
            }

    def _reset_stats(self):
        """重置替换统计"""
        self.replacement_stats = {
            'total_replacements': 0,
            'successful_replacements': 0,
            'failed_replacements': 0,
            'processed_placeholders': []
        }

    def _get_field_placeholders(self, field_name: str, field_mappings: Dict[str, str]) -> List[str]:
        """获取字段的所有占位符"""
        placeholders = []

        # 从field_mappings中获取
        for placeholder, mapped_field in field_mappings.items():
            if mapped_field == field_name:
                placeholders.append(placeholder)

        # 从字段配置中获取
        field_info = self.field_dictionary.get(field_name, {})
        config_placeholders = field_info.get('placeholders', [])
        placeholders.extend(config_placeholders)

        # 去重
        return list(set(placeholders))

    def _format_field_value(self, field_name: str, field_value: Any) -> str:
        """格式化字段值"""
        try:
            if not field_value:
                return ""

            field_info = self.field_dictionary.get(field_name, {})
            field_type = field_info.get('type', 'text')
            format_rule = field_info.get('format', '')

            # 根据字段类型格式化
            if field_type == 'money':
                try:
                    amount = float(field_value)
                    if 'unit:万元' in format_rule:
                        return f"{amount:.0f}万元"
                    else:
                        return f"{amount:.2f}"
                except (ValueError, TypeError):
                    return str(field_value)

            elif field_type == 'percent':
                try:
                    percent = float(field_value)
                    return f"{percent:.2f}%"
                except (ValueError, TypeError):
                    return str(field_value)

            elif field_type == 'date':
                return str(field_value)

            else:
                return str(field_value).strip()

        except Exception as e:
            self.logger.warning(f"格式化字段值失败 {field_name}: {e}")
            return str(field_value)

    def _replace_text_in_document(self, doc: docx.Document, old_text: str, new_text: str) -> int:
        """在Word文档中替换文本，保持格式"""
        replacement_count = 0

        try:
            # 在段落中替换
            for paragraph in doc.paragraphs:
                if old_text in paragraph.text:
                    count = self._replace_text_in_paragraph(paragraph, old_text, new_text)
                    replacement_count += count

            # 在表格中替换
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if old_text in paragraph.text:
                                count = self._replace_text_in_paragraph(paragraph, old_text, new_text)
                                replacement_count += count

            # 在页眉页脚中替换
            for section in doc.sections:
                # 页眉
                if section.header:
                    for paragraph in section.header.paragraphs:
                        if old_text in paragraph.text:
                            count = self._replace_text_in_paragraph(paragraph, old_text, new_text)
                            replacement_count += count

                # 页脚
                if section.footer:
                    for paragraph in section.footer.paragraphs:
                        if old_text in paragraph.text:
                            count = self._replace_text_in_paragraph(paragraph, old_text, new_text)
                            replacement_count += count

            return replacement_count

        except Exception as e:
            self.logger.error(f"文档文本替换失败: {e}")
            return 0
    
    def _replace_text_in_paragraph(self, paragraph, old_text: str, new_text: str) -> int:
        """在段落中替换文本，保持格式"""
        try:
            # 检查段落文本是否包含目标文本
            if old_text not in paragraph.text:
                return 0
            
            # 简单情况：整个段落就是目标文本
            if paragraph.text.strip() == old_text:
                return self._replace_entire_paragraph(paragraph, new_text)
            
            # 复杂情况：在runs中查找和替换
            return self._replace_text_in_runs(paragraph, old_text, new_text)
            
        except Exception as e:
            self.logger.error(f"段落文本替换失败: {e}")
            return 0
    
    def _replace_entire_paragraph(self, paragraph, new_text: str) -> int:
        """替换整个段落的文本"""
        try:
            # 清空段落
            paragraph.clear()
            
            # 添加新文本
            run = paragraph.add_run(new_text)
            
            # 应用标准格式
            self._apply_standard_format(run)
            
            return 1
            
        except Exception as e:
            self.logger.error(f"整段替换失败: {e}")
            return 0
    
    def _replace_text_in_runs(self, paragraph, old_text: str, new_text: str) -> int:
        """在runs中替换文本"""
        try:
            # 查找包含目标文本的run
            for run in paragraph.runs:
                if old_text in run.text:
                    # 保存原有格式
                    original_format = self._extract_run_format(run)
                    
                    # 替换文本
                    run.text = run.text.replace(old_text, new_text)
                    
                    # 应用格式
                    self._apply_format_to_run(run, original_format)
                    
                    # 添加黄色高亮
                    run.font.highlight_color = self.yellow_color
                    
                    return 1
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Run文本替换失败: {e}")
            return 0
    
    def _extract_run_format(self, run) -> Dict[str, Any]:
        """提取run的格式信息"""
        try:
            return {
                'font_name': run.font.name,
                'font_size': run.font.size,
                'bold': run.font.bold,
                'italic': run.font.italic,
                'underline': run.font.underline,
                'color': run.font.color.rgb if run.font.color and run.font.color.rgb else None
            }
        except Exception as e:
            self.logger.warning(f"提取格式信息失败: {e}")
            return {}
    
    def _apply_format_to_run(self, run, format_info: Dict[str, Any]):
        """应用格式信息到run"""
        try:
            if format_info.get('font_name'):
                run.font.name = format_info['font_name']
            else:
                run.font.name = '宋体'  # 默认字体
            
            if format_info.get('font_size'):
                run.font.size = format_info['font_size']
            else:
                run.font.size = Pt(14)  # 默认字体大小
            
            if format_info.get('bold') is not None:
                run.font.bold = format_info['bold']
            
            if format_info.get('italic') is not None:
                run.font.italic = format_info['italic']
            
            if format_info.get('underline') is not None:
                run.font.underline = format_info['underline']
            
            if format_info.get('color'):
                run.font.color.rgb = format_info['color']
                
        except Exception as e:
            self.logger.warning(f"应用格式失败: {e}")
    
    def _apply_standard_format(self, run):
        """应用标准格式"""
        try:
            run.font.name = '宋体'
            run.font.size = Pt(14)
            run.font.highlight_color = self.yellow_color
        except Exception as e:
            self.logger.warning(f"应用标准格式失败: {e}")
    
    def replace_in_excel(self, template_path: str, output_path: str,
                        field_data: Dict[str, Any],
                        field_mappings: Dict[str, str]) -> Dict[str, Any]:
        """处理Excel模板文件，进行字段替换"""
        try:
            self.logger.info(f"开始处理Excel模板: {template_path}")

            if not EXCEL_AVAILABLE:
                raise ImportError("openpyxl库未安装，无法处理Excel文件")

            # 验证模板文件
            if not Path(template_path).exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            # 重置统计
            self._reset_stats()

            # 复制模板文件
            shutil.copy2(template_path, output_path)

            # 加载Excel工作簿
            workbook = openpyxl.load_workbook(output_path)

            # 遍历所有工作表
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]

                # 遍历所有单元格
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and isinstance(cell.value, str):
                            original_value = cell.value
                            new_value = original_value

                            # 执行字段替换
                            for field_name, field_value in field_data.items():
                                placeholders = self._get_field_placeholders(field_name, field_mappings)

                                for placeholder in placeholders:
                                    if placeholder and placeholder in new_value:
                                        formatted_value = self._format_field_value(field_name, field_value)
                                        new_value = new_value.replace(placeholder, formatted_value)

                                        self.replacement_stats['total_replacements'] += 1
                                        self.replacement_stats['processed_placeholders'].append({
                                            'field': field_name,
                                            'placeholder': placeholder,
                                            'value': formatted_value,
                                            'cell': f"{sheet_name}!{cell.coordinate}"
                                        })

                            # 更新单元格值
                            if new_value != original_value:
                                cell.value = new_value

            # 保存工作簿
            workbook.save(output_path)
            workbook.close()

            successful_fields = len(set([p['field'] for p in self.replacement_stats['processed_placeholders']]))
            total_fields = len(field_data)

            result = {
                'success': True,
                'output_path': output_path,
                'total_replacements': self.replacement_stats['total_replacements'],
                'successful_fields': successful_fields,
                'total_fields': total_fields,
                'processed_placeholders': self.replacement_stats['processed_placeholders']
            }

            self.logger.info(f"Excel模板处理完成: 替换{self.replacement_stats['total_replacements']}处，成功字段{successful_fields}/{total_fields}")
            return result

        except Exception as e:
            self.logger.error(f"Excel模板处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'output_path': output_path,
                'total_replacements': 0,
                'successful_fields': 0,
                'total_fields': len(field_data) if field_data else 0
            }

    def process_template(self, template_path: str, output_path: str,
                        field_data: Dict[str, Any],
                        field_mappings: Dict[str, str]) -> Dict[str, Any]:
        """统一的模板处理接口，自动识别文件类型"""
        try:
            template_file = Path(template_path)
            file_extension = template_file.suffix.lower()

            if file_extension == '.docx':
                return self.replace_in_word(template_path, output_path, field_data, field_mappings)
            elif file_extension in ['.xlsx', '.xlsm']:
                return self.replace_in_excel(template_path, output_path, field_data, field_mappings)
            else:
                raise ValueError(f"不支持的模板文件格式: {file_extension}")

        except Exception as e:
            self.logger.error(f"模板处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'output_path': output_path,
                'total_replacements': 0,
                'successful_fields': 0,
                'total_fields': len(field_data) if field_data else 0
            }

    def validate_template(self, template_path: str) -> Dict[str, Any]:
        """验证模板文件"""
        try:
            template_file = Path(template_path)

            if not template_file.exists():
                return {
                    'valid': False,
                    'error': '模板文件不存在'
                }

            file_extension = template_file.suffix.lower()

            if file_extension == '.docx':
                return self._validate_word_template(template_path)
            elif file_extension in ['.xlsx', '.xlsm']:
                return self._validate_excel_template(template_path)
            else:
                return {
                    'valid': False,
                    'error': f'不支持的模板格式: {file_extension}'
                }

        except Exception as e:
            self.logger.error(f"模板验证失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }

    def _validate_word_template(self, template_path: str) -> Dict[str, Any]:
        """验证Word模板"""
        try:
            doc = docx.Document(template_path)

            # 检查文档基本信息
            paragraph_count = len(doc.paragraphs)
            table_count = len(doc.tables)

            # 查找占位符
            placeholders = self._find_placeholders_in_word(doc)

            return {
                'valid': True,
                'type': 'word',
                'paragraphs': paragraph_count,
                'tables': table_count,
                'placeholders': placeholders,
                'placeholder_count': len(placeholders)
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f'Word模板验证失败: {e}'
            }

    def _validate_excel_template(self, template_path: str) -> Dict[str, Any]:
        """验证Excel模板"""
        try:
            if not EXCEL_AVAILABLE:
                return {
                    'valid': False,
                    'error': 'openpyxl库未安装，无法验证Excel模板'
                }

            workbook = openpyxl.load_workbook(template_path)
            sheet_count = len(workbook.sheetnames)

            # 查找占位符
            placeholders = self._find_placeholders_in_excel(workbook)

            workbook.close()

            return {
                'valid': True,
                'type': 'excel',
                'sheets': sheet_count,
                'sheet_names': workbook.sheetnames,
                'placeholders': placeholders,
                'placeholder_count': len(placeholders)
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f'Excel模板验证失败: {e}'
            }

    def _find_placeholders_in_word(self, doc: docx.Document) -> List[str]:
        """在Word文档中查找占位符"""
        placeholders = set()

        try:
            # 在段落中查找
            for paragraph in doc.paragraphs:
                placeholders.update(self._extract_placeholders_from_text(paragraph.text))

            # 在表格中查找
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            placeholders.update(self._extract_placeholders_from_text(paragraph.text))

        except Exception as e:
            self.logger.warning(f"查找Word占位符失败: {e}")

        return list(placeholders)

    def _find_placeholders_in_excel(self, workbook) -> List[str]:
        """在Excel工作簿中查找占位符"""
        placeholders = set()

        try:
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                for row in sheet.iter_rows():
                    for cell in row:
                        if cell.value and isinstance(cell.value, str):
                            placeholders.update(self._extract_placeholders_from_text(cell.value))

        except Exception as e:
            self.logger.warning(f"查找Excel占位符失败: {e}")

        return list(placeholders)

    def _extract_placeholders_from_text(self, text: str) -> List[str]:
        """从文本中提取占位符"""
        import re
        placeholders = []

        if not text:
            return placeholders

        # 匹配【】格式的占位符
        bracket_pattern = r'【[^】]+】'
        placeholders.extend(re.findall(bracket_pattern, text))

        # 匹配{{}}格式的占位符
        brace_pattern = r'\{\{[^}]+\}\}'
        placeholders.extend(re.findall(brace_pattern, text))

        # 匹配特殊的占位符文本
        if '待补充' in text:
            placeholders.append('待补充')

        return placeholders
