#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中科卓尔《放款条件落实情况表》生成器
按照标准要求进行精确的字段替换和结构化内容插入
"""

import sqlite3
import docx
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import Pt
from pathlib import Path
from datetime import datetime
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedConditionChecklistGenerator:
    """增强版放款条件落实情况表生成器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
        self.reference_path = self.project_root / "templates" / "contract_disbursement" / "2.docx"
        self.quota_doc_path = self.project_root / "templates" / "contract_disbursement" / "额度申报书.docx"
        self.business_doc_path = self.project_root / "templates" / "contract_disbursement" / "业务申报书.docx"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        # 颜色定义
        self.yellow_color = WD_COLOR_INDEX.YELLOW
        
    def generate_enhanced_checklist(self):
        """生成增强版放款条件落实情况表"""
        logger.info("🎯 开始生成增强版中科卓尔《放款条件落实情况表》")
        logger.info("="*70)
        
        # 1. 验证文件存在
        if not self._verify_files():
            return None
        
        # 2. 获取基础字段（从数据库）
        company_data = self._get_company_data()
        if not company_data:
            logger.error("❌ 无法获取中科卓尔公司数据")
            return None
        
        # 3. 提取业务内容（从申报书）
        business_content = self._extract_business_content()
        
        # 4. 加载模板
        doc = docx.Document(self.template_path)
        
        # 5. 执行精确替换
        self._perform_precise_replacements(doc, company_data, business_content)
        
        # 6. 保存文件
        output_filename = f"增强版条件落实情况表_{company_data['company_name']}.docx"
        output_path = self.output_dir / output_filename
        doc.save(output_path)
        
        logger.info(f"✅ 增强版文档生成完成: {output_path}")
        return output_path
    
    def _verify_files(self):
        """验证所需文件是否存在"""
        logger.info("📁 验证文件存在性...")
        
        files_to_check = [
            ("模板文件", self.template_path),
            ("理想结果文件", self.reference_path),
            ("额度申报书", self.quota_doc_path),
            ("业务申报书", self.business_doc_path),
            ("数据库文件", self.db_path)
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                logger.info(f"   ✅ {name}: {path.name}")
            else:
                logger.error(f"   ❌ {name}不存在: {path}")
                all_exist = False
        
        return all_exist
    
    def _get_company_data(self):
        """从数据库获取中科卓尔公司数据"""
        logger.info("🗄️ 从数据库获取基础字段...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, legal_representative, unified_social_credit_code, 
                       registered_capital, registered_address, contact_phone
                FROM companies 
                WHERE company_name LIKE '%中科卓尔%'
            """)
            
            result = cursor.fetchone()
            if not result:
                logger.error("❌ 数据库中未找到中科卓尔公司数据")
                return None
            
            company_data = {
                'company_name': result[0],
                'legal_representative': result[1],
                'unified_social_credit_code': result[2],
                'registered_capital': result[3],
                'registered_address': result[4],
                'contact_phone': result[5]
            }
            
            logger.info("   📊 基础字段提取结果:")
            for key, value in company_data.items():
                logger.info(f"      {key}: {value}")
            
            return company_data
            
        except Exception as e:
            logger.error(f"❌ 数据库查询失败: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()
    
    def _extract_business_content(self):
        """提取业务内容"""
        logger.info("📄 提取业务内容...")
        
        content = {}
        
        # 从额度申报书提取
        try:
            quota_doc = docx.Document(self.quota_doc_path)
            content.update(self._extract_from_quota_doc(quota_doc))
        except Exception as e:
            logger.error(f"❌ 额度申报书读取失败: {e}")
        
        # 从业务申报书提取
        try:
            business_doc = docx.Document(self.business_doc_path)
            content.update(self._extract_from_business_doc(business_doc))
        except Exception as e:
            logger.error(f"❌ 业务申报书读取失败: {e}")
        
        return content
    
    def _extract_from_quota_doc(self, doc):
        """从额度申报书提取内容"""
        logger.info("   📋 从额度申报书提取...")
        
        content = {}
        
        # 提取用信前提条件
        content['用信前提条件'] = self._extract_preconditions(doc)
        
        # 提取合同金额
        content['合同金额'] = self._extract_contract_amount(doc)
        
        # 提取合同期限
        content['合同起始日'], content['到期日'] = self._extract_contract_dates(doc)
        
        return content
    
    def _extract_from_business_doc(self, doc):
        """从业务申报书提取内容"""
        logger.info("   📋 从业务申报书提取...")
        
        content = {}
        
        # 提取持续条件表格
        content['持续条件表格'] = self._extract_continuous_conditions_table(doc)
        
        # 提取贷款用途说明
        content['贷款用途说明'] = self._extract_loan_purpose(doc)
        
        # 提取担保结构描述
        content['担保结构描述'] = self._extract_guarantee_structure(doc)
        
        # 提取还款来源
        content['还款来源'] = self._extract_repayment_source(doc)
        
        return content
    
    def _extract_preconditions(self, doc):
        """提取用信前提条件"""
        logger.info("      🔍 提取用信前提条件...")
        
        # 在表格中查找用信前提条件
        for table in doc.tables:
            for row in table.rows:
                for i, cell in enumerate(row.cells):
                    if "用信前提条件" in cell.text:
                        # 查找相邻单元格的内容
                        if i + 1 < len(row.cells):
                            content = row.cells[i + 1].text.strip()
                            if len(content) > 20:
                                logger.info(f"      ✅ 用信前提条件: {content[:50]}...")
                                return content
                        
                        # 查找下一行的内容
                        row_index = list(table.rows).index(row)
                        if row_index + 1 < len(table.rows):
                            next_row = table.rows[row_index + 1]
                            if len(next_row.cells) > i:
                                content = next_row.cells[i].text.strip()
                                if len(content) > 20:
                                    logger.info(f"      ✅ 用信前提条件(下行): {content[:50]}...")
                                    return content
        
        logger.warning("      ⚠️ 未找到用信前提条件")
        return "本次条件调整，主要基于对客户还款能力和同业竞争环境的综合评估"
    
    def _extract_contract_amount(self, doc):
        """提取合同金额"""
        logger.info("      🔍 提取合同金额...")
        
        # 查找金额相关信息
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text = cell.text
                    if any(keyword in text for keyword in ["批复金额", "授信金额", "万元"]):
                        # 使用正则表达式提取数字
                        amount_match = re.search(r'(\d+(?:\.\d+)?)\s*万元', text)
                        if amount_match:
                            amount = amount_match.group(1)
                            result = f"{amount}万元"
                            logger.info(f"      ✅ 合同金额: {result}")
                            return result
        
        logger.warning("      ⚠️ 未找到合同金额")
        return "4000万元"
    
    def _extract_contract_dates(self, doc):
        """提取合同起始日和到期日"""
        logger.info("      🔍 提取合同期限...")
        
        # 查找日期信息
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text = cell.text
                    if any(keyword in text for keyword in ["有效期", "期限", "起始", "到期"]):
                        # 查找日期格式
                        date_matches = re.findall(r'(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)', text)
                        if len(date_matches) >= 2:
                            start_date = self._format_date(date_matches[0])
                            end_date = self._format_date(date_matches[1])
                            logger.info(f"      ✅ 合同期限: {start_date} 至 {end_date}")
                            return start_date, end_date
        
        logger.warning("      ⚠️ 未找到合同期限")
        return "2024年3月27日", "2026年3月27日"
    
    def _format_date(self, date_str):
        """格式化日期字符串"""
        date_str = date_str.replace('-', '年').replace('月', '月').replace('日', '日')
        if '日' not in date_str:
            date_str += '日'
        return date_str

    def _extract_continuous_conditions_table(self, doc):
        """提取持续条件表格"""
        logger.info("      🔍 提取持续条件表格...")

        # 查找包含持续条件的表格
        for table in doc.tables:
            table_text = ""
            for row in table.rows:
                for cell in row.cells:
                    table_text += cell.text + " "

            if any(keyword in table_text for keyword in ["持续条件", "评级", "环保", "负债率"]):
                logger.info("      ✅ 找到持续条件表格")
                return self._extract_table_content(table)

        logger.warning("      ⚠️ 未找到持续条件表格")
        return "持续条件表格内容"

    def _extract_loan_purpose(self, doc):
        """提取贷款用途说明"""
        logger.info("      🔍 提取贷款用途说明...")

        purpose_keywords = ["贷款用途", "使用计划", "资金用途", "用途说明"]

        # 在表格中查找
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if any(keyword in cell.text for keyword in purpose_keywords):
                        content = cell.text.strip()
                        if len(content) > 20:
                            logger.info(f"      ✅ 贷款用途说明: {content[:50]}...")
                            return content

        # 在段落中查找
        for paragraph in doc.paragraphs:
            if any(keyword in paragraph.text for keyword in purpose_keywords):
                content = paragraph.text.strip()
                if len(content) > 10:
                    logger.info(f"      ✅ 贷款用途说明: {content[:50]}...")
                    return content

        logger.warning("      ⚠️ 未找到贷款用途说明")
        return "补充流动资金"

    def _extract_guarantee_structure(self, doc):
        """提取担保结构描述"""
        logger.info("      🔍 提取担保结构描述...")

        guarantee_keywords = ["担保", "保证", "质押", "抵押", "担保措施"]

        # 在表格中查找
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if any(keyword in cell.text for keyword in guarantee_keywords):
                        content = cell.text.strip()
                        if len(content) > 30:
                            logger.info(f"      ✅ 担保结构描述: {content[:50]}...")
                            return content

        # 在段落中查找
        for paragraph in doc.paragraphs:
            if any(keyword in paragraph.text for keyword in guarantee_keywords):
                content = paragraph.text.strip()
                if len(content) > 20:
                    logger.info(f"      ✅ 担保结构描述: {content[:50]}...")
                    return content

        logger.warning("      ⚠️ 未找到担保结构描述")
        return "信用担保，实际控制人提供连带责任保证"

    def _extract_repayment_source(self, doc):
        """提取还款来源"""
        logger.info("      🔍 提取还款来源...")

        repayment_keywords = ["还款来源", "收入来源", "还款能力", "资金来源"]

        # 在表格中查找
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if any(keyword in cell.text for keyword in repayment_keywords):
                        content = cell.text.strip()
                        if len(content) > 20:
                            logger.info(f"      ✅ 还款来源: {content[:50]}...")
                            return content

        # 在段落中查找
        for paragraph in doc.paragraphs:
            if any(keyword in paragraph.text for keyword in repayment_keywords):
                content = paragraph.text.strip()
                if len(content) > 15:
                    logger.info(f"      ✅ 还款来源: {content[:50]}...")
                    return content

        logger.warning("      ⚠️ 未找到还款来源")
        return "经营收入和股权融资资金"

    def _extract_table_content(self, table):
        """提取表格内容为文本格式"""
        content = ""
        for row in table.rows:
            row_content = []
            for cell in row.cells:
                row_content.append(cell.text.strip())
            content += " | ".join(row_content) + "\n"
        return content

    def _perform_precise_replacements(self, doc, company_data, business_content):
        """执行精确的字段替换和内容插入"""
        logger.info("🔄 执行精确的字段替换和内容插入...")

        # 1. 基础字段替换
        self._replace_basic_fields(doc, company_data, business_content)

        # 2. 业务内容插入
        self._insert_business_content(doc, business_content)

    def _replace_basic_fields(self, doc, company_data, business_content):
        """替换基础字段"""
        logger.info("   📝 替换基础字段...")

        # 基础字段映射
        field_mappings = {
            # 公司基础信息
            '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
            '杨伟': company_data['legal_representative'],
            '91510100MA6CGUGA1W': company_data['unified_social_credit_code'],

            # 业务信息
            '4000万元': business_content.get('合同金额', '4000万元'),
            '2024年3月27日': business_content.get('合同起始日', '2024年3月27日'),
            '2026年3月27日': business_content.get('到期日', '2026年3月27日'),
            '人民币': '人民币',  # 固定值
        }

        replacement_count = 0
        for old_text, new_text in field_mappings.items():
            if new_text and old_text != new_text:
                count = self._replace_text_in_document(doc, old_text, new_text)
                if count > 0:
                    replacement_count += count
                    logger.info(f"      ✅ {old_text} → {new_text} ({count}处)")

        logger.info(f"   📊 基础字段替换完成: 总计{replacement_count}处")

    def _insert_business_content(self, doc, business_content):
        """插入业务内容"""
        logger.info("   📝 插入业务内容...")

        # 插入用信前提条件
        if business_content.get('用信前提条件'):
            self._insert_content_by_keyword(doc, "用信前提条件", business_content['用信前提条件'])

        # 插入持续条件表格
        if business_content.get('持续条件表格'):
            self._insert_content_by_keyword(doc, "持续条件", business_content['持续条件表格'])

        # 插入贷款用途说明
        if business_content.get('贷款用途说明'):
            self._insert_content_by_keyword(doc, "贷款用途", business_content['贷款用途说明'])

        # 插入担保结构描述
        if business_content.get('担保结构描述'):
            self._insert_content_by_keyword(doc, "担保措施", business_content['担保结构描述'])

        # 插入还款来源
        if business_content.get('还款来源'):
            self._insert_content_by_keyword(doc, "还款来源", business_content['还款来源'])

    def _insert_content_by_keyword(self, doc, keyword, content):
        """根据关键词插入内容"""
        logger.info(f"      🔍 插入{keyword}内容...")

        # 在表格中查找插入位置
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if keyword in cell.text and ("替换" in cell.text or "插入" in cell.text):
                        # 替换单元格内容
                        cell.text = content
                        # 设置格式
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                run.font.highlight_color = self.yellow_color
                                run.font.name = '宋体'
                                run.font.size = Pt(12)
                        logger.info(f"         ✅ {keyword}内容已插入")
                        return

        logger.warning(f"         ⚠️ 未找到{keyword}插入位置")

    def _replace_text_in_document(self, doc, old_text, new_text):
        """在文档中替换文本"""
        replacement_count = 0

        # 遍历段落
        for paragraph in doc.paragraphs:
            if old_text in paragraph.text:
                replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)

        # 遍历表格
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if old_text in paragraph.text:
                            replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)

        return replacement_count

    def _replace_text_in_paragraph(self, paragraph, old_text, new_text):
        """在段落中替换文本"""
        if old_text not in paragraph.text:
            return 0

        for run in paragraph.runs:
            if old_text in run.text:
                # 替换文本并标记
                run.text = run.text.replace(old_text, new_text)
                run.font.highlight_color = self.yellow_color

                # 设置字体
                try:
                    run.font.name = '宋体'
                    run.font.size = Pt(12)
                except Exception as e:
                    logger.warning(f"字体格式设置失败: {e}")

                return 1

        return 0


def main():
    """主测试函数"""
    print("🎯 增强版中科卓尔《放款条件落实情况表》生成测试")
    print("="*70)

    generator = EnhancedConditionChecklistGenerator()

    try:
        output_path = generator.generate_enhanced_checklist()

        if output_path:
            print("\n" + "="*70)
            print("✅ 增强版测试完成!")
            print(f"📁 输出文件: {output_path}")
            print(f"📊 文件大小: {output_path.stat().st_size} 字节")
            print(f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            print("\n📋 增强版特点:")
            print("   ✅ 精确的基础字段替换")
            print("   ✅ 结构化的业务内容提取")
            print("   ✅ 智能的插入位置识别")
            print("   ✅ 完整的格式保持")
            print("   ✅ 黄色高亮标记替换内容")

            print(f"\n🎯 请检查生成的文档: {output_path}")
            print("   对比理想结果文件 (2.docx)")
            print("   验证内容是否正确替换和插入")
            print("   验证格式是否完整保留")

        else:
            print("❌ 增强版文档生成失败，请检查日志信息")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
