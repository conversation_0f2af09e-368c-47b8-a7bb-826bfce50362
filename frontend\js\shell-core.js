/**
 * 企业服务驾驶舱 - 外壳应用核心
 * 职责：模块加载器 + 上下文提供者
 * 严格禁止包含任何业务逻辑
 */

class ShellApplication {
    constructor() {
        this.currentCustomer = null;
        this.currentModule = null;
        this.loadedModules = new Map();
        this.moduleManifest = [];
        
        this.init();
    }

    async init() {
        console.log('🚀 外壳应用初始化...');
        
        try {
            // 加载模块清单
            await this.loadModuleManifest();
            
            // 初始化界面
            this.initializeUI();
            
            // 加载客户列表
            await this.loadCustomerList();
            
            console.log('✅ 外壳应用初始化完成');
            this.updateStatus('系统就绪');
            
        } catch (error) {
            console.error('❌ 外壳应用初始化失败:', error);
            this.updateStatus('初始化失败', 'error');
        }
    }

    async loadModuleManifest() {
        try {
            const response = await fetch('/module_manifest.json');
            if (!response.ok) {
                throw new Error(`加载模块清单失败: ${response.status}`);
            }
            this.moduleManifest = await response.json();
            console.log('📋 模块清单加载成功:', this.moduleManifest);
        } catch (error) {
            console.error('❌ 加载模块清单失败:', error);
            throw error;
        }
    }

    initializeUI() {
        // 绑定客户选择事件
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.addEventListener('change', (e) => {
                if (e.target.value) {
                    this.selectCustomer(e.target.value);
                }
            });
        }

        // 绑定更换客户按钮
        const changeCustomerBtn = document.getElementById('change-customer-btn');
        if (changeCustomerBtn) {
            changeCustomerBtn.addEventListener('click', () => {
                this.showCustomerSelection();
            });
        }

        // 绑定更换模块按钮
        const changeModuleBtn = document.getElementById('change-module-btn');
        if (changeModuleBtn) {
            changeModuleBtn.addEventListener('click', () => {
                this.showModuleSelection();
            });
        }
    }

    async loadCustomerList() {
        try {
            const response = await fetch('http://127.0.0.1:5000/api/companies');
            if (!response.ok) {
                throw new Error(`获取客户列表失败: ${response.status}`);
            }
            
            const result = await response.json();
            const companies = result.data || [];
            
            const customerSelect = document.getElementById('customer-select');
            if (customerSelect) {
                // 清空现有选项
                customerSelect.innerHTML = '<option value="">请选择客户...</option>';
                
                // 添加客户选项
                companies.forEach(company => {
                    const option = document.createElement('option');
                    option.value = company.id;
                    option.textContent = company.company_name;
                    customerSelect.appendChild(option);
                });
            }
            
            console.log(`📊 客户列表加载完成，共 ${companies.length} 个客户`);
            
        } catch (error) {
            console.error('❌ 加载客户列表失败:', error);
            this.updateStatus('客户列表加载失败', 'error');
        }
    }

    async selectCustomer(customerId) {
        try {
            this.updateStatus('正在加载客户信息...');
            
            // 获取客户详细信息
            const response = await fetch(`http://127.0.0.1:5000/api/company/${customerId}`);
            if (!response.ok) {
                throw new Error(`获取客户信息失败: ${response.status}`);
            }
            
            const result = await response.json();
            this.currentCustomer = result.data;
            
            console.log('👤 客户选择成功:', this.currentCustomer);
            
            // 更新界面显示
            this.updateCustomerDisplay();
            
            // 显示模块选择界面
            this.showModuleSelection();
            
            this.updateStatus('客户信息加载完成');
            
        } catch (error) {
            console.error('❌ 选择客户失败:', error);
            this.updateStatus('客户信息加载失败', 'error');
        }
    }

    showModuleSelection() {
        // 隐藏其他阶段
        this.hideAllStages();
        
        // 显示模块选择阶段
        const moduleStage = document.getElementById('module-selection-stage');
        if (moduleStage) {
            moduleStage.style.display = 'block';
            
            // 生成模块卡片
            this.renderModuleCards();
        }
        
        // 隐藏当前模块显示
        const currentModuleDisplay = document.getElementById('current-module-display');
        if (currentModuleDisplay) {
            currentModuleDisplay.style.display = 'none';
        }
    }

    renderModuleCards() {
        const modulesGrid = document.getElementById('modules-grid');
        if (!modulesGrid) return;
        
        modulesGrid.innerHTML = '';
        
        this.moduleManifest.forEach(module => {
            const moduleCard = document.createElement('div');
            moduleCard.className = 'module-card';
            moduleCard.innerHTML = `
                <div class="module-icon">${module.icon}</div>
                <h3 class="module-title">${module.name}</h3>
                <p class="module-description">${module.description}</p>
                <div class="module-features">
                    ${module.features.map(feature => 
                        `<span class="feature-tag">${feature}</span>`
                    ).join('')}
                </div>
                <button class="module-select-btn" onclick="shellApp.loadModule('${module.id}')">
                    选择此模块
                </button>
            `;
            modulesGrid.appendChild(moduleCard);
        });
    }

    async loadModule(moduleId) {
        try {
            this.showLoading('正在加载模块...');
            
            // 查找模块配置
            const moduleConfig = this.moduleManifest.find(m => m.id === moduleId);
            if (!moduleConfig) {
                throw new Error(`未找到模块配置: ${moduleId}`);
            }
            
            console.log('🔄 开始加载模块:', moduleConfig);
            
            // 如果模块已加载，先销毁
            if (this.currentModule) {
                await this.unloadCurrentModule();
            }
            
            // 动态加载模块脚本
            const moduleInstance = await this.loadModuleScript(moduleConfig);
            
            // 获取模块容器
            const container = document.getElementById('module-container');
            if (!container) {
                throw new Error('模块容器未找到');
            }
            
            // 初始化模块
            await moduleInstance.init(container, this.currentCustomer);
            
            // 记录当前模块
            this.currentModule = {
                id: moduleId,
                config: moduleConfig,
                instance: moduleInstance
            };
            
            // 更新界面
            this.showModuleContainer();
            this.updateModuleDisplay();
            
            console.log('✅ 模块加载成功:', moduleId);
            this.updateStatus(`模块 "${moduleConfig.name}" 已加载`);
            
        } catch (error) {
            console.error('❌ 模块加载失败:', error);
            this.updateStatus(`模块加载失败: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadModuleScript(moduleConfig) {
        // 检查是否已缓存
        if (this.loadedModules.has(moduleConfig.id)) {
            return this.loadedModules.get(moduleConfig.id);
        }
        
        // 动态加载CSS
        if (moduleConfig.css_path) {
            await this.loadCSS(moduleConfig.css_path);
        }
        
        // 动态加载JavaScript
        const moduleClass = await this.loadJS(moduleConfig.script_path);
        
        // 创建模块实例
        const moduleInstance = new moduleClass();
        
        // 缓存模块
        this.loadedModules.set(moduleConfig.id, moduleInstance);
        
        return moduleInstance;
    }

    loadCSS(cssPath) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = cssPath;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    loadJS(scriptPath) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptPath;
            script.onload = () => {
                // 假设模块暴露了一个全局类
                const className = this.getModuleClassName(scriptPath);
                const moduleClass = window[className];
                if (moduleClass) {
                    resolve(moduleClass);
                } else {
                    reject(new Error(`模块类 ${className} 未找到`));
                }
            };
            script.onerror = reject;
            document.body.appendChild(script);
        });
    }

    getModuleClassName(scriptPath) {
        // 根据脚本路径推断类名
        if (scriptPath.includes('yingqi_zhilian')) {
            return 'YingqiZhilianModule';
        } else if (scriptPath.includes('customer_engagement')) {
            return 'CustomerEngagementModule';
        }
        return 'UnknownModule';
    }

    async unloadCurrentModule() {
        if (this.currentModule && this.currentModule.instance) {
            try {
                await this.currentModule.instance.destroy();
                console.log('🗑️ 模块已卸载:', this.currentModule.id);
            } catch (error) {
                console.error('❌ 模块卸载失败:', error);
            }
        }
        this.currentModule = null;
    }

    showModuleContainer() {
        this.hideAllStages();
        const container = document.getElementById('module-container');
        if (container) {
            container.style.display = 'block';
        }
    }

    showCustomerSelection() {
        this.hideAllStages();
        const customerStage = document.getElementById('customer-selection-stage');
        if (customerStage) {
            customerStage.style.display = 'block';
        }
        
        // 重置客户选择
        this.currentCustomer = null;
        this.updateCustomerDisplay();
        
        // 卸载当前模块
        this.unloadCurrentModule();
    }

    hideAllStages() {
        const stages = [
            'customer-selection-stage',
            'module-selection-stage',
            'module-container'
        ];
        
        stages.forEach(stageId => {
            const stage = document.getElementById(stageId);
            if (stage) {
                stage.style.display = 'none';
            }
        });
    }

    updateCustomerDisplay() {
        const customerDisplay = document.getElementById('current-customer-display');
        const customerName = document.getElementById('customer-name-display');
        
        if (this.currentCustomer) {
            if (customerName) {
                customerName.textContent = this.currentCustomer.company_name;
            }
            if (customerDisplay) {
                customerDisplay.style.display = 'flex';
            }
        } else {
            if (customerDisplay) {
                customerDisplay.style.display = 'none';
            }
        }
    }

    updateModuleDisplay() {
        const moduleDisplay = document.getElementById('current-module-display');
        const moduleName = document.getElementById('current-module-name');
        
        if (this.currentModule) {
            if (moduleName) {
                moduleName.textContent = this.currentModule.config.name;
            }
            if (moduleDisplay) {
                moduleDisplay.style.display = 'flex';
            }
        } else {
            if (moduleDisplay) {
                moduleDisplay.style.display = 'none';
            }
        }
    }

    updateStatus(message, type = 'info') {
        const statusIndicator = document.getElementById('status-indicator');
        if (statusIndicator) {
            const icon = type === 'error' ? '🔴' : '🟢';
            statusIndicator.textContent = `${icon} ${message}`;
        }
    }

    showLoading(message = '加载中...') {
        const overlay = document.getElementById('loading-overlay');
        const text = document.querySelector('.loading-text');
        if (overlay) {
            overlay.style.display = 'flex';
        }
        if (text) {
            text.textContent = message;
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
}

// 全局实例
let shellApp;

// 初始化外壳应用
document.addEventListener('DOMContentLoaded', () => {
    shellApp = new ShellApplication();
});
