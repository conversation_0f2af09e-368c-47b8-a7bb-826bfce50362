#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取条件落实情况表(.docx格式)并识别标黄部分
"""

import docx
from pathlib import Path
import logging
from docx.shared import RGBColor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConditionChecklistDocxReader:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.file_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
        
    def read_and_analyze(self):
        """读取并分析条件落实情况表"""
        logger.info("📖 开始读取条件落实情况表(.docx)...")
        
        if not self.file_path.exists():
            logger.error(f"❌ 文件不存在: {self.file_path}")
            print(f"请确认文件已转换并保存为: {self.file_path}")
            return
        
        try:
            # 加载文档
            doc = docx.Document(self.file_path)
            
            print("\n" + "="*80)
            print("📋 条件落实情况表内容分析")
            print("="*80)
            print(f"📁 文件路径: {self.file_path}")
            print(f"📊 段落数量: {len(doc.paragraphs)}")
            print(f"📊 表格数量: {len(doc.tables)}")
            
            # 分析段落中的高亮内容
            highlighted_paragraphs = self._analyze_paragraphs(doc.paragraphs)
            
            # 分析表格中的高亮内容
            highlighted_tables = self._analyze_tables(doc.tables)
            
            # 汇总所有高亮内容
            all_highlighted = highlighted_paragraphs + highlighted_tables
            
            print(f"\n🎨 发现标黄内容总数: {len(all_highlighted)}个")
            
            if all_highlighted:
                self._display_highlighted_content(all_highlighted)
                self._analyze_database_mapping(all_highlighted)
            else:
                print("⚠️ 未发现标黄内容，可能需要检查文档格式")
                self._show_all_content_preview(doc)
            
            print("\n" + "="*80)
            print("✅ 分析完成")
            print("="*80)
            
        except Exception as e:
            logger.error(f"❌ 读取文件失败: {e}")
    
    def _analyze_paragraphs(self, paragraphs):
        """分析段落中的高亮内容"""
        highlighted_content = []
        
        for i, paragraph in enumerate(paragraphs):
            if paragraph.text.strip():
                # 检查段落中的每个run
                for run in paragraph.runs:
                    if run.text.strip() and self._is_highlighted(run):
                        highlighted_content.append({
                            'type': 'paragraph',
                            'location': f'段落{i+1}',
                            'text': run.text.strip(),
                            'context': paragraph.text.strip()[:100]
                        })
        
        return highlighted_content
    
    def _analyze_tables(self, tables):
        """分析表格中的高亮内容"""
        highlighted_content = []
        
        for table_idx, table in enumerate(tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    # 检查单元格中的每个段落和run
                    for para in cell.paragraphs:
                        for run in para.runs:
                            if run.text.strip() and self._is_highlighted(run):
                                highlighted_content.append({
                                    'type': 'table',
                                    'location': f'表格{table_idx+1}-行{row_idx+1}-列{cell_idx+1}',
                                    'text': run.text.strip(),
                                    'context': cell.text.strip()[:100]
                                })
        
        return highlighted_content
    
    def _is_highlighted(self, run):
        """检查run是否被高亮（标黄）"""
        if run.font.highlight_color:
            # 检查高亮颜色
            highlight = run.font.highlight_color
            # Word中的黄色高亮通常是YELLOW
            if hasattr(highlight, 'rgb') and highlight.rgb:
                return True
            # 或者检查是否有高亮设置
            return True
        
        # 检查背景色
        if hasattr(run.font, 'color') and run.font.color:
            if hasattr(run.font.color, 'rgb') and run.font.color.rgb:
                rgb = run.font.color.rgb
                # 检查是否为黄色系
                if self._is_yellow_like_color(rgb):
                    return True
        
        return False
    
    def _is_yellow_like_color(self, rgb_color):
        """判断颜色是否类似黄色"""
        if rgb_color:
            try:
                # 获取RGB值
                r = (rgb_color >> 16) & 0xFF
                g = (rgb_color >> 8) & 0xFF  
                b = rgb_color & 0xFF
                
                # 黄色特征：红色和绿色值较高，蓝色值较低
                return r > 200 and g > 200 and b < 150
            except:
                pass
        return False
    
    def _display_highlighted_content(self, highlighted_content):
        """显示高亮内容"""
        print("\n🎨 标黄内容详情:")
        print("-" * 60)
        
        for i, item in enumerate(highlighted_content, 1):
            print(f"{i:2d}. 位置: {item['location']}")
            print(f"    内容: {item['text']}")
            if item['context'] != item['text']:
                print(f"    上下文: {item['context']}")
            print()
    
    def _analyze_database_mapping(self, highlighted_content):
        """分析数据库字段映射"""
        print("\n📊 数据库字段映射分析:")
        print("-" * 60)
        
        # 数据库现有字段
        db_fields = {
            'company_name': '企业名称',
            'unified_social_credit_code': '统一社会信用代码',
            'legal_representative': '法定代表人',
            'registration_date': '注册时间',
            'registered_capital': '注册资本',
            'business_scope': '经营范围',
            'business_description': '主营业务描述',
            'contact_phone': '联系电话',
            'finance_manager_name': '财务主管姓名',
            'finance_manager_phone': '财务主管电话',
            'total_assets': '资产总额',
            'total_liabilities': '负债总额',
            'registered_address': '注册地址'
        }
        
        can_auto_fill = []
        need_additional_data = []
        
        for item in highlighted_content:
            text = item['text']
            location = item['location']
            
            # 检查是否能从数据库字段匹配
            matched = False
            for db_field, description in db_fields.items():
                if any(keyword in text for keyword in description.split('/')):
                    can_auto_fill.append({
                        'text': text,
                        'location': location,
                        'db_field': db_field,
                        'description': description
                    })
                    matched = True
                    break
            
            if not matched:
                need_additional_data.append({
                    'text': text,
                    'location': location
                })
        
        print(f"✅ 可自动填充字段 ({len(can_auto_fill)}个):")
        for item in can_auto_fill:
            print(f"   - {item['text']} → {item['description']} ({item['db_field']})")
        
        print(f"\n❌ 需要额外数据字段 ({len(need_additional_data)}个):")
        for item in need_additional_data:
            print(f"   - {item['text']} (位置: {item['location']})")
    
    def _show_all_content_preview(self, doc):
        """显示所有内容预览（当未找到高亮时）"""
        print("\n📝 文档内容预览:")
        print("-" * 60)
        
        print("段落内容:")
        for i, para in enumerate(doc.paragraphs[:10]):
            if para.text.strip():
                text = para.text.strip()[:100]
                print(f"   {i+1}. {text}{'...' if len(para.text.strip()) > 100 else ''}")
        
        if doc.tables:
            print(f"\n表格内容 (共{len(doc.tables)}个表格):")
            for table_idx, table in enumerate(doc.tables[:2]):  # 只显示前2个表格
                print(f"   表格{table_idx+1}:")
                for row_idx, row in enumerate(table.rows[:5]):  # 只显示前5行
                    row_text = " | ".join([cell.text.strip()[:30] for cell in row.cells])
                    print(f"      行{row_idx+1}: {row_text}")

def main():
    reader = ConditionChecklistDocxReader()
    reader.read_and_analyze()

if __name__ == "__main__":
    main()
