#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据库实现 - 提取申报书信息到数据库，生成完整落实情况表
"""

import os
import shutil
import sqlite3
import json
from docx import Document
from docx.shared import RGBColor, Pt
from docx.enum.table import WD_TABLE_ALIGNMENT
from datetime import datetime
from pathlib import Path

class CompleteDatabaseImplementation:
    """完整数据库实现"""
    
    def __init__(self):
        self.green_rgb = RGBColor(0, 128, 0)  # 绿色标记
        self.project_root = Path(__file__).parent
        
        # 文件路径
        self.quota_application = self.project_root / "templates" / "contract_disbursement" / "额度申报书.docx"
        self.business_application = self.project_root / "templates" / "contract_disbursement" / "业务申报书.docx"
        self.template_file = self.project_root / "templates" / "contract_disbursement" / "落实情况表.docx"
        self.output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        # 公司信息
        self.company_name = "成都中科卓尔智能科技集团有限公司"
        
    def create_database_tables(self):
        """创建数据库表结构"""
        print("🗄️ 创建数据库表结构...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建条件表格主表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS condition_tables (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    company_name TEXT NOT NULL,
                    table_type TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    rows_count INTEGER,
                    columns_count INTEGER,
                    headers TEXT,
                    source_file TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建条件表格数据行表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS condition_table_rows (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_id INTEGER,
                    row_index INTEGER,
                    row_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (table_id) REFERENCES condition_tables (id)
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ 数据库表结构创建成功")
            
        except Exception as e:
            print(f"❌ 创建数据库表失败: {e}")
    
    def analyze_business_application(self):
        """重新分析业务申报书"""
        print("📋 重新分析业务申报书...")
        
        if not self.business_application.exists():
            print(f"❌ 业务申报书不存在: {self.business_application}")
            return []
        
        try:
            doc = Document(self.business_application)
            target_tables = []
            
            print(f"📊 业务申报书包含 {len(doc.tables)} 个表格")
            
            for i, table in enumerate(doc.tables):
                table_text = self.get_table_text(table)
                print(f"🔍 分析表格 {i+1}...")
                
                # 查找贷款条件表格
                if "贷款条件" in table_text or "担保措施" in table_text or "序号" in table_text:
                    print(f"✅ 表格{i+1} 识别为贷款条件表格")
                    structure = self.extract_table_structure(table, "贷款条件表格")
                    target_tables.append(structure)
                else:
                    print(f"⚪ 表格{i+1} 不是目标表格")
            
            return target_tables
            
        except Exception as e:
            print(f"❌ 分析业务申报书失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def get_table_text(self, table):
        """获取表格的所有文本内容"""
        text_parts = []
        try:
            for row in table.rows:
                for cell in row.cells:
                    text_parts.append(cell.text.strip())
        except:
            pass
        return " ".join(text_parts)
    
    def extract_table_structure(self, table, table_name):
        """提取表格结构"""
        structure = {
            "table_name": table_name,
            "rows": len(table.rows),
            "columns": len(table.columns),
            "headers": [],
            "data": []
        }
        
        try:
            # 提取表头
            if table.rows:
                headers = []
                for cell in table.rows[0].cells:
                    headers.append(cell.text.strip())
                structure["headers"] = headers
            
            # 提取数据行
            for row in table.rows[1:]:  # 跳过表头
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())
                structure["data"].append(row_data)
                
        except Exception as e:
            print(f"❌ 提取表格结构失败: {e}")
        
        return structure
    
    def get_quota_application_tables(self):
        """获取额度申报书中的目标表格"""
        print("📋 获取额度申报书目标表格...")
        
        if not self.quota_application.exists():
            print(f"❌ 额度申报书不存在: {self.quota_application}")
            return []
        
        try:
            doc = Document(self.quota_application)
            target_tables = []
            
            for i, table in enumerate(doc.tables):
                table_text = self.get_table_text(table)
                
                # 识别前提条件表格 (表格5)
                if (len(table.rows) == 2 and len(table.columns) == 4 and 
                    "前提条件" in table_text and "产品" in table_text):
                    print(f"✅ 表格{i+1} 识别为前提条件表格")
                    structure = self.extract_table_structure(table, "前提条件表格")
                    structure["table_type"] = "precondition"
                    target_tables.append(structure)
                
                # 识别持续条件表格 (表格6)
                elif (len(table.rows) == 13 and len(table.columns) == 4 and 
                      "持续条件" in table_text and "评级水平" in table_text):
                    print(f"✅ 表格{i+1} 识别为持续条件表格")
                    structure = self.extract_table_structure(table, "持续条件表格")
                    structure["table_type"] = "continuous"
                    target_tables.append(structure)
            
            return target_tables
            
        except Exception as e:
            print(f"❌ 获取额度申报书表格失败: {e}")
            return []
    
    def save_tables_to_database(self, tables, source_file):
        """将表格保存到数据库"""
        print(f"💾 保存表格到数据库...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 清除该公司的旧数据
            cursor.execute("DELETE FROM condition_table_rows WHERE table_id IN (SELECT id FROM condition_tables WHERE company_name = ?)", (self.company_name,))
            cursor.execute("DELETE FROM condition_tables WHERE company_name = ?", (self.company_name,))
            
            for table in tables:
                # 插入表格主记录
                cursor.execute("""
                    INSERT INTO condition_tables 
                    (company_name, table_type, table_name, rows_count, columns_count, headers, source_file)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.company_name,
                    table.get("table_type", "unknown"),
                    table["table_name"],
                    table["rows"],
                    table["columns"],
                    json.dumps(table["headers"], ensure_ascii=False),
                    source_file
                ))
                
                table_id = cursor.lastrowid
                
                # 插入表格数据行
                for row_index, row_data in enumerate(table["data"]):
                    cursor.execute("""
                        INSERT INTO condition_table_rows 
                        (table_id, row_index, row_data)
                        VALUES (?, ?, ?)
                    """, (
                        table_id,
                        row_index,
                        json.dumps(row_data, ensure_ascii=False)
                    ))
                
                print(f"✅ 保存表格: {table['table_name']} ({table['rows']}行 x {table['columns']}列)")
            
            conn.commit()
            conn.close()
            print(f"✅ 成功保存 {len(tables)} 个表格到数据库")
            
        except Exception as e:
            print(f"❌ 保存到数据库失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_company_info(self):
        """获取公司基础信息"""
        return {
            'name': self.company_name,
            'contact_person': '张总',
            'phone': '028-********',
            'address': '成都市高新区',
            'contract_number': '建八卓尔（2025）001号',
            'date': datetime.now().strftime('%Y年%m月%d日'),
            'bank_branch': '建行成都第八支行',
            'current_year': str(datetime.now().year),
            'current_month': str(datetime.now().month)
        }

    def load_tables_from_database(self):
        """从数据库加载表格数据"""
        print("📖 从数据库加载表格数据...")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询表格主记录
            cursor.execute("""
                SELECT id, table_type, table_name, rows_count, columns_count, headers
                FROM condition_tables
                WHERE company_name = ?
                ORDER BY table_type
            """, (self.company_name,))

            tables = {}
            for row in cursor.fetchall():
                table_id, table_type, table_name, rows_count, columns_count, headers_json = row

                # 查询表格数据行
                cursor.execute("""
                    SELECT row_index, row_data
                    FROM condition_table_rows
                    WHERE table_id = ?
                    ORDER BY row_index
                """, (table_id,))

                rows_data = []
                for row_data in cursor.fetchall():
                    row_index, row_data_json = row_data
                    rows_data.append(json.loads(row_data_json))

                tables[table_type] = {
                    'table_name': table_name,
                    'headers': json.loads(headers_json),
                    'data': rows_data,
                    'rows': rows_count,
                    'columns': columns_count
                }

                print(f"✅ 加载表格: {table_name} ({table_type})")

            conn.close()
            return tables

        except Exception as e:
            print(f"❌ 从数据库加载表格失败: {e}")
            return {}

    def create_table_in_document(self, doc, table_data):
        """在文档中创建表格"""
        if not table_data:
            return None

        try:
            headers = table_data['headers']
            data = table_data['data']

            # 创建表格
            table = doc.add_table(rows=len(data)+1, cols=len(headers))
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 设置表头
            header_row = table.rows[0]
            for col_idx, header in enumerate(headers):
                cell = header_row.cells[col_idx]
                cell.text = header
                # 表头设置为黑色加粗
                for para in cell.paragraphs:
                    for run in para.runs:
                        run.font.bold = True
                        run.font.color.rgb = RGBColor(0, 0, 0)

            # 设置数据行
            for row_idx, row_data in enumerate(data, 1):
                table_row = table.rows[row_idx]
                for col_idx, cell_data in enumerate(row_data):
                    if col_idx < len(table_row.cells):
                        cell = table_row.cells[col_idx]
                        cell.text = str(cell_data)
                        # 数据行设置为绿色
                        for para in cell.paragraphs:
                            for run in para.runs:
                                run.font.color.rgb = self.green_rgb

            return table

        except Exception as e:
            print(f"❌ 创建表格失败: {e}")
            return None

    def replace_placeholders_in_document(self, doc, company_info):
        """替换文档中的占位符"""
        print("🔄 替换文档占位符...")

        # 定义占位符映射
        placeholders = {
            '【企业名称】': company_info['name'],
            '【客户名称】': company_info['name'],
            '【合同编号】': company_info['contract_number'],
            '【当前年份】': company_info['current_year'],
            '【当前月份】': company_info['current_month'],
            '【当前日期】': company_info['date'],
            '【联系人】': company_info['contact_person'],
            '【联系电话】': company_info['phone'],
            '【地址】': company_info['address'],
            '【经办行】': company_info['bank_branch'],
            '【填报日期】': company_info['date']
        }

        # 替换段落中的占位符
        for para in doc.paragraphs:
            original_text = para.text
            for placeholder, value in placeholders.items():
                if placeholder in para.text:
                    print(f"  替换段落: {placeholder} → {value}")
                    para.text = para.text.replace(placeholder, value)
                    # 设置替换内容为绿色
                    if para.text != original_text:
                        for run in para.runs:
                            run.font.color.rgb = self.green_rgb

        # 替换表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    original_text = cell.text
                    for placeholder, value in placeholders.items():
                        if placeholder in cell.text:
                            print(f"  替换表格: {placeholder} → {value}")
                            cell.text = cell.text.replace(placeholder, value)
                            # 设置替换内容为绿色
                            if cell.text != original_text:
                                for para in cell.paragraphs:
                                    for run in para.runs:
                                        run.font.color.rgb = self.green_rgb

    def insert_tables_at_titles(self, doc, tables_data):
        """在指定标题下插入表格"""
        print("📝 在指定标题下插入表格...")

        # 标题映射
        title_mappings = {
            '一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况': 'precondition',
            '二、单户综合融资总量方案申报书中列明的持续条件及落实情况': 'continuous',
            '三、单笔业务申报书中列明的贷款条件及落实情况': 'business'
        }

        # 找到标题段落并在其后插入表格
        paragraphs = list(doc.paragraphs)

        for para in paragraphs:
            for title, table_type in title_mappings.items():
                if title in para.text:
                    print(f"✅ 找到标题: {title}")

                    if table_type in tables_data:
                        print(f"   插入 {table_type} 表格...")

                        # 在段落后添加表格
                        table = self.create_table_in_document(doc, tables_data[table_type])

                        if table:
                            print(f"   ✅ 成功插入 {tables_data[table_type]['table_name']}")
                        else:
                            print(f"   ❌ 插入表格失败")
                    else:
                        print(f"   ⚠️ 未找到对应的 {table_type} 表格数据")

    def generate_complete_document(self):
        """生成完整的落实情况表"""
        print("🚀 生成完整的落实情况表...")
        print("=" * 80)

        try:
            # 1. 创建数据库表结构
            self.create_database_tables()

            # 2. 分析并提取申报书表格
            print("\n📋 第一步：提取申报书表格数据")
            quota_tables = self.get_quota_application_tables()
            business_tables = self.analyze_business_application()

            all_tables = quota_tables + business_tables

            if not all_tables:
                print("❌ 没有提取到任何表格数据")
                return None

            # 3. 保存表格到数据库
            print(f"\n💾 第二步：保存 {len(all_tables)} 个表格到数据库")
            self.save_tables_to_database(quota_tables, "额度申报书.docx")
            self.save_tables_to_database(business_tables, "业务申报书.docx")

            # 4. 从数据库加载表格数据
            print(f"\n📖 第三步：从数据库加载表格数据")
            tables_data = self.load_tables_from_database()

            if not tables_data:
                print("❌ 从数据库加载表格数据失败")
                return None

            # 5. 获取公司基础信息
            company_info = self.get_company_info()
            print(f"✅ 获取公司信息: {company_info['name']}")

            # 6. 复制模板并生成文档
            print(f"\n📄 第四步：生成完整落实情况表")
            output_file = self.output_dir / f"落实情况表_完整版_中科卓尔_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(self.template_file, output_file)
            print(f"✅ 复制模板文件到: {output_file}")

            # 7. 打开文档并处理
            doc = Document(output_file)

            # 8. 替换基础信息占位符
            self.replace_placeholders_in_document(doc, company_info)

            # 9. 在标题下插入表格
            self.insert_tables_at_titles(doc, tables_data)

            # 10. 保存最终文档
            doc.save(output_file)
            print(f"✅ 成功生成完整文档: {output_file}")

            return output_file

        except Exception as e:
            print(f"❌ 生成文档失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    print("🎯 完整数据库实现 - 落实情况表生成器")
    print("📋 提取申报书信息到数据库，生成完整测试文件")
    print("=" * 80)

    generator = CompleteDatabaseImplementation()
    result = generator.generate_complete_document()

    if result:
        print(f"\n🎉 完整实现成功！")
        print(f"📄 测试文件位置: {result}")
        print(f"📝 包含完整内容:")
        print(f"  ✅ 中科卓尔基础信息自动填充（绿色显示）")
        print(f"  ✅ 一、前提条件表格（从额度申报书提取）")
        print(f"  ✅ 二、持续条件表格（从额度申报书提取）")
        print(f"  ✅ 三、贷款条件表格（从业务申报书提取）")
        print(f"  ✅ 所有数据已存储到数据库供后续使用")
        print(f"\n🔍 请检查文件是否符合您的所有要求！")
    else:
        print(f"\n❌ 生成失败，请检查错误信息")

if __name__ == "__main__":
    main()
