# 落实情况表标准化处理流程

## 📋 概述

本文档记录了落实情况表的标准化处理流程，适用于所有客户。

## 🎯 标准化数据提取位置

### 1. 用信前提条件
- **来源文件**: 额度申报书.docx
- **标准位置**: 第5个表格，第2行，第2列
- **输出格式**: 文本格式
- **标记**: 红色显示

### 2. 持续条件
- **来源文件**: 额度申报书.docx  
- **标准位置**: 第6个表格（完整表格）
- **输出格式**: 13行x4列真实Word表格
- **标记**: 表头黑色，数据红色

### 3. 贷款条件
- **来源文件**: 业务申报书.docx
- **标准位置**: 第7个表格（完整表格）
- **输出格式**: 5行x2列真实Word表格
- **标记**: 全部内容红色

## 🔧 使用方法

### 对于新客户

1. **准备文件**
   ```
   templates/contract_disbursement/额度申报书.docx
   templates/contract_disbursement/业务申报书.docx
   templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx
   ```

2. **运行标准化处理器**
   ```python
   from standardized_implementation_processor import process_for_company
   
   # 替换为客户公司名称关键词
   output_file = process_for_company("客户公司关键词")
   ```

3. **获得输出**
   ```
   test_output/落实情况表_公司名称_YYYYMMDD_HHMMSS.docx
   ```

## 📊 标准化替换位置

### 目标文档位置
- **模板文件**: disbursement_condition_checklist_blueprint.docx
- **替换位置**: 主表格第9行第1列
- **替换方式**: 清空单元格，按顺序插入三个部分

### 替换顺序
1. 用信前提条件（文本）
2. 持续条件（表格）
3. 贷款条件（表格）

## ⚠️ 注意事项

### 文件要求
- 源文件必须是标准Word格式(.docx)
- 表格位置必须符合标准映射
- 数据库中必须有对应企业信息

### 质量保证
- 自动验证表格结构
- 确保内容完整性
- 保持原始格式

## 🔄 后续维护

### 定期检查
1. 验证申报书格式是否变化
2. 更新表格位置映射
3. 测试完整流程

### 扩展功能
1. 支持批量处理
2. 自定义输出格式
3. 集成到业务系统

---

**最后更新**: 2025-08-01
**版本**: 1.0 - 标准化版本
