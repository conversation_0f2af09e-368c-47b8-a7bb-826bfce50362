# 协定存款协议字段分析报告

## 📋 字段分类总结

### ✅ 数据库中已有的企业信息（可直接获取）

| 字段名称 | 数据库字段 | 示例值 | 状态 |
|---------|-----------|--------|------|
| 甲方企业名称 | `companies.company_name` | 成都中科卓尔智能科技集团有限公司 | ✅ 已有 |
| 统一社会信用代码 | `companies.unified_social_credit_code` | 91510100MA6CGUGA1W | ✅ 已有 |
| 法定代表人 | `companies.legal_representative` | 杨伟 | ✅ 已有 |
| 注册地址 | `companies.registered_address` | 中国（四川）自由贸易试验区... | ✅ 已有 |
| 联系电话 | `companies.contact_phone` | 028-******** | ✅ 已有 |
| 企业账户号 | `companies.company_account` | 51001915101006102345 | ⚠️ 部分有 |

### ❌ 数据库中缺失的字段（需要配置或生成）

#### 🏦 账户信息类
| 字段名称 | 模板占位符 | 数据来源建议 | 优先级 |
|---------|-----------|-------------|--------|
| 协定存款账户号 | `在乙方开立协定存款账户的账号：待补充` | 新建账户表或生成规则 | 🔴 高 |
| 活期存款账户号 | `活期存款账户（账号：待补充_）` | 账户管理系统 | 🔴 高 |

#### 💰 金融信息类
| 字段名称 | 模板占位符 | 数据来源建议 | 优先级 |
|---------|-----------|-------------|--------|
| 基本存款额度 | `（大写）            万` | 企业规模评估算法 | 🔴 高 |
| 利率调整幅度 | `加（选填"加"或"减"）25 bps` | 企业等级配置表 | 🟡 中 |

#### 📅 业务信息类
| 字段名称 | 模板占位符 | 数据来源建议 | 优先级 |
|---------|-----------|-------------|--------|
| 合同签署日期 | `年    月    日` | 系统当前日期 | 🟢 低 |
| 通知期限 | `通知后【】个工作日内` | 银行政策配置 | 🟢 低 |

## 🔧 建议的数据库扩展方案

### 1. 创建账户信息表
```sql
CREATE TABLE company_accounts (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    account_type VARCHAR(50) NOT NULL, -- '基本存款账户', '协定存款账户'
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(200),
    opening_date DATE,
    account_status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 创建企业金融配置表
```sql
CREATE TABLE company_financial_config (
    id UUID PRIMARY KEY,
    company_id UUID NOT NULL REFERENCES companies(id),
    deposit_limit_amount DECIMAL(15,2), -- 协定存款额度
    deposit_limit_amount_chinese VARCHAR(100), -- 额度大写
    interest_rate_adjustment VARCHAR(20), -- 利率调整幅度
    credit_rating VARCHAR(10), -- 企业信用等级
    risk_level VARCHAR(20), -- 风险等级
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 创建银行政策配置表
```sql
CREATE TABLE bank_policy_config (
    id SERIAL PRIMARY KEY,
    policy_name VARCHAR(100) NOT NULL,
    policy_value VARCHAR(200) NOT NULL,
    policy_description TEXT,
    effective_from DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT true
);

-- 插入默认政策
INSERT INTO bank_policy_config (policy_name, policy_value, policy_description, effective_from, is_active) 
VALUES 
('利率调整通知期限', '5个工作日', '利率调整提前通知期限', CURRENT_DATE, true),
('协定存款最低额度', '100万元', '协定存款业务最低额度要求', CURRENT_DATE, true);
```

## 🎯 实施优先级建议

### 🔴 高优先级（立即实施）
1. **完善企业账户信息** - 为现有企业补充账户号信息
2. **建立存款额度评估规则** - 基于企业规模自动计算建议额度
3. **创建账户管理表** - 支持多账户类型管理

### 🟡 中优先级（近期实施）
1. **企业信用等级评估** - 建立等级与利率调整的映射
2. **金融配置管理** - 为每个企业设置个性化金融参数

### 🟢 低优先级（后续优化）
1. **银行政策配置化** - 将固定参数改为可配置
2. **历史记录追踪** - 记录配置变更历史

## 📊 当前数据完整性分析

基于现有4家企业的数据分析：

| 企业名称 | 基本信息完整度 | 账户信息 | 金融配置 | 可生成协议 |
|---------|---------------|----------|----------|-----------|
| 成都中科卓尔智能科技集团 | 95% | ✅ 有账户号 | ❌ 缺失 | ⚠️ 需配置 |
| 神光光学集团 | 90% | ❌ 无账户号 | ❌ 缺失 | ❌ 需补充 |
| 成都卫讯科技 | 85% | ❌ 无账户号 | ❌ 缺失 | ❌ 需补充 |
| 四川至臻精密光学 | 80% | ❌ 无账户号 | ❌ 缺失 | ❌ 需补充 |

## 🚀 下一步行动建议

1. **立即可做**：为中科卓尔配置金融参数，实现首个完整协议生成
2. **短期目标**：补充其他3家企业的账户信息
3. **中期目标**：建立自动化的额度评估和等级评定系统
4. **长期目标**：实现协定存款协议的完全自动化生成
