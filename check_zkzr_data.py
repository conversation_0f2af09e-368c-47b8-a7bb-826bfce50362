#!/usr/bin/env python3
"""
查看中科卓尔的数据库信息
"""

import sqlite3

def check_zkzr_data():
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        # 获取表结构
        cursor.execute("PRAGMA table_info(companies)")
        columns = cursor.fetchall()
        print("=== 数据库字段结构 ===")
        for col in columns:
            print(f"{col[1]}: {col[2]}")
        
        print("\n=== 中科卓尔数据 ===")
        cursor.execute("SELECT * FROM companies WHERE company_name LIKE '%中科卓尔%'")
        result = cursor.fetchone()
        
        if result:
            column_names = [description[0] for description in cursor.description]
            for i, value in enumerate(result):
                print(f"{column_names[i]}: {value}")
        else:
            print("未找到中科卓尔数据")
        
        conn.close()
        
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    check_zkzr_data()
