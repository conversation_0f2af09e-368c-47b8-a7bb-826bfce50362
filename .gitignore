# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
*.tmp
*.bak
*.backup

# Test files
test_*.py
*_test.py
debug_*.py
verify_*.py

# Generated documents
*.docx
*.pdf
!templates/**/*.docx
!templates/**/*.pdf

# Node.js (if needed in future)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
