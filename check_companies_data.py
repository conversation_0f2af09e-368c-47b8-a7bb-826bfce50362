#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查companies表的具体数据
"""

import sqlite3
from pathlib import Path

def check_companies_data():
    """检查companies表数据"""
    db_path = Path(__file__).parent / "database" / "enterprise_service.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有companies数据
        cursor.execute("SELECT * FROM companies;")
        companies = cursor.fetchall()
        
        # 获取列名
        cursor.execute("PRAGMA table_info(companies);")
        columns = [col[1] for col in cursor.fetchall()]
        
        print("📊 companies表数据详情:")
        print("=" * 80)
        
        for i, company in enumerate(companies, 1):
            print(f"\n🏢 企业 {i}:")
            print("-" * 40)
            for j, value in enumerate(company):
                if value is not None and str(value).strip():
                    print(f"  {columns[j]}: {value}")
        
        # 查找中科卓尔
        cursor.execute("SELECT * FROM companies WHERE company_name LIKE '%卓尔%' OR company_name LIKE '%中科%';")
        zhuoer_data = cursor.fetchall()
        
        if zhuoer_data:
            print(f"\n🎯 中科卓尔数据:")
            print("=" * 40)
            for company in zhuoer_data:
                for j, value in enumerate(company):
                    if value is not None and str(value).strip():
                        print(f"  {columns[j]}: {value}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_companies_data()
