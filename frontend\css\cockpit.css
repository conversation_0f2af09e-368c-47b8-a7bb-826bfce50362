/* 驾驶舱专用样式 */

/* 基础布局 */
.cockpit-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.cockpit-header {
    text-align: center;
    padding: 2rem 0;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.cockpit-header h1 {
    font-size: 2.5rem;
    margin: 0;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cockpit-header .subtitle {
    font-size: 1.1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

/* 主要布局 */
.cockpit-main {
    display: grid;
    grid-template-columns: 1fr 280px;
    gap: 1.5rem;
    padding: 2rem;
    min-height: calc(100vh - 140px);
}

/* 客户信息侧边栏 */
.customer-sidebar {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: #333;
}

.change-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.change-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* 当前模块区域样式 */
.current-module-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.module-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
}

.module-name {
    font-weight: 600;
    color: #007bff;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.module-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

.customer-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    text-align: center;
}

.customer-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.customer-code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
}

.customer-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    margin-bottom: 1rem;
    padding: 0.8rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.detail-label {
    display: block;
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.detail-value {
    display: block;
    color: #333;
    font-size: 0.9rem;
    line-height: 1.4;
}

.personnel-compact h4,
.tags-compact h4 {
    margin: 0 0 0.8rem 0;
    font-size: 1rem;
    color: #333;
}

.personnel-compact-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.personnel-item-compact {
    background: #e3f2fd;
    padding: 0.6rem;
    border-radius: 6px;
    font-size: 0.85rem;
}

.tags-compact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
}

.tag-compact {
    background: #f0f0f0;
    color: #666;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    font-size: 0.75rem;
    border: 1px solid #ddd;
}

/* 业务舞台 */
.business-stage {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 模块选择舞台 */
.module-selection-stage {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.module-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.module-card:hover {
    border-color: #667eea;
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.module-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
}

.module-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.module-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    text-align: center;
}

.module-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.feature-tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.module-select-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.module-select-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 客户选择舞台 */
.selection-stage {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.selection-content {
    max-width: 500px;
}

.selection-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.selection-content h2 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.selection-content p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.customer-selector-enhanced {
    margin-top: 2rem;
}

.form-select-enhanced {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 1.1rem;
    background: white;
    transition: all 0.3s ease;
}

.form-select-enhanced:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 工作流程舞台 */
.workflow-stage {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.stage-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
}

.progress-enhanced {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar-enhanced {
    width: 200px;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill-enhanced {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.6s ease;
    width: 50%; /* 默认50%，因为有2个预选项 */
}

.progress-text-enhanced {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

/* 工作流程列表 */
.workflow-list-enhanced {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.workflow-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.workflow-item:hover {
    background: #f0f4ff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.workflow-item.completed {
    background: #e8f5e8;
    border-color: #4caf50;
}

.workflow-item.in-progress {
    background: #fff3e0;
    border-color: #ff9800;
}

.task-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.task-checkbox {
    position: relative;
}

.task-check {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
}

.task-content h3 {
    margin: 0 0 0.3rem 0;
    color: #333;
    font-size: 1.1rem;
}

.task-description {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-action {
    background: #667eea;
    color: white;
}

.primary-action:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.ai-action {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.ai-action:hover {
    background: linear-gradient(135deg, #ff5252, #e55100);
    transform: translateY(-1px);
}

.download-action {
    background: #4caf50;
    color: white;
}

.download-action:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1rem;
}

/* 状态反馈 */
.status-feedback {
    margin-top: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.feedback-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.feedback-icon {
    font-size: 1.2rem;
}

.feedback-text {
    color: #333;
    font-weight: 500;
}

/* 操作日志面板 */
.logs-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid #f0f0f0;
}

.panel-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.clear-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #ff5252;
}

.logs-container-enhanced {
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #667eea;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry.success {
    border-left-color: #4caf50;
    background: #e8f5e8;
}

.log-entry.error {
    border-left-color: #f44336;
    background: #ffebee;
}

.log-entry.info {
    border-left-color: #2196f3;
    background: #e3f2fd;
}

/* OA工作台模态窗口 */
.oa-workspace-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.oa-workspace-content {
    background: white;
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* OA工作台详细样式 */
.oa-workspace-container {
    width: 95vw;
    height: 90vh;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 16px;
    overflow: hidden;
}

.workspace-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.workspace-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.customer-info {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.workspace-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.document-panel {
    flex: 1;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.ai-panel {
    width: 400px;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.panel-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    background: white;
}

.panel-header h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-size: 1.1rem;
}

.replacement-info {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #666;
}

.highlight-legend {
    color: #f57c00;
}

.replacement-count {
    color: #4CAF50;
}

.document-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    background: white;
    font-family: 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
}

.document-content .auto-replaced {
    background-color: yellow;
    padding: 2px 4px;
    border-radius: 3px;
}

.document-content .doc-paragraph {
    margin: 0.8rem 0;
}

.document-content .doc-heading {
    font-weight: bold;
    margin: 1.5rem 0 0.8rem 0;
    color: #333;
}

.document-content .doc-title {
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;
    margin: 1rem 0;
}

.prompt-section,
.ai-input-section {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h3 {
    margin: 0;
    font-size: 1rem;
    color: #333;
}

.copy-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: background 0.3s ease;
}

.copy-btn:hover {
    background: #45a049;
}

.instruction {
    margin: 0;
    font-size: 0.85rem;
    color: #666;
}

.prompt-content textarea,
.ai-input-content textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    resize: vertical;
}

.prompt-content textarea {
    height: 120px;
    background: #f5f5f5;
    color: #333;
}

.ai-input-content textarea {
    height: 200px;
    background: white;
}

.action-section {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.action-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn.primary {
    background: #667eea;
    color: white;
}

.action-btn.primary:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover:not(:disabled) {
    background: #5a6268;
}

.action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.workspace-footer {
    padding: 1rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.status-info {
    display: flex;
    gap: 2rem;
    font-size: 0.85rem;
}

.status-item {
    color: #666;
}

/* 加载和错误状态样式 */
.oa-workspace-loading,
.oa-workspace-error {
    padding: 3rem;
    text-align: center;
    height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-detail {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.error-actions {
    margin-top: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.retry-btn,
.close-error-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
}

.retry-btn {
    background: #667eea;
    color: white;
}

.close-error-btn {
    background: #6c757d;
    color: white;
}

/* 统一驾驶舱专用布局 */
.cockpit-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem;
}

.cockpit-container .cockpit-main {
    display: block;
    max-width: 1200px;
    margin: 0 auto;
}

/* 选择舞台样式 */
.selection-stage {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.selection-content .selection-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    display: block;
}

.selection-content h2 {
    font-size: 2.2rem;
    color: #333;
    margin-bottom: 1rem;
    font-weight: 600;
}

.selection-content p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.customer-selector-enhanced {
    max-width: 400px;
    margin: 0 auto;
}

.form-select-enhanced {
    width: 100%;
    padding: 1.2rem 1.5rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1.1rem;
    background: white;
    color: #333;
    transition: all 0.3s ease;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 1rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
}

.form-select-enhanced:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select-enhanced:hover {
    border-color: #667eea;
}

/* 业务选择舞台 */
.business-stage {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.stage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid #f0f0f0;
}

.stage-header h2 {
    font-size: 1.8rem;
    color: #333;
    margin: 0;
    font-weight: 600;
}

/* 业务模块网格样式 */
.business-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.business-module-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    min-height: 320px;
    position: relative;
    overflow: hidden;
}

.business-module-card:hover:not(.disabled):not(.add-module) {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

.business-module-card.disabled {
    opacity: 0.6;
    background: rgba(240, 240, 240, 0.95);
}

.business-module-card.add-module {
    border-style: dashed;
    border-color: #ccc;
    background: rgba(250, 250, 250, 0.95);
}

.module-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.module-header .module-icon {
    font-size: 2.5rem;
    color: #667eea;
}

.business-module-card.disabled .module-icon {
    color: #999;
}

.module-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
    font-weight: 600;
}

.module-description {
    flex: 1;
    margin-bottom: 1.5rem;
}

.module-description p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.module-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.module-features li {
    color: #777;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.module-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.business-module-card.disabled .module-features li:before {
    color: #999;
}

.module-actions {
    margin-top: auto;
}

.disabled-action {
    background: #bdc3c7 !important;
    cursor: not-allowed !important;
}

.disabled-action:hover {
    transform: none !important;
    background: #bdc3c7 !important;
}

/* 当前客户信息显示 */
.current-customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    color: #666;
    flex-wrap: wrap;
}

.customer-name-display {
    font-weight: 600;
    color: #333;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    white-space: nowrap;
}

.change-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.change-btn:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

/* 日志面板样式 */
.logs-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: fixed;
    top: 2rem;
    right: 2rem;
    width: 300px;
    max-height: 400px;
    z-index: 100;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
    font-weight: 600;
}

.clear-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #c0392b;
}

.logs-container-enhanced {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #667eea;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry.success {
    border-left-color: #27ae60;
    background: #d5f4e6;
}

.log-entry.error {
    border-left-color: #e74c3c;
    background: #fdf2f2;
}

.log-entry.warning {
    border-left-color: #f39c12;
    background: #fef9e7;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .cockpit-container {
        padding: 1rem;
    }

    .logs-panel {
        position: static;
        width: 100%;
        margin-top: 2rem;
    }

    .business-modules-grid {
        grid-template-columns: 1fr;
    }

    .stage-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .cockpit-header h1 {
        font-size: 2rem;
    }

    .cockpit-container {
        padding: 1rem;
    }

    .selection-stage,
    .business-stage {
        padding: 2rem 1.5rem;
    }

    .selection-content h2 {
        font-size: 1.8rem;
    }

    .business-module-card {
        padding: 1.5rem;
        min-height: auto;
    }

    .current-customer-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .logs-panel {
        position: static;
        width: 100%;
        margin-top: 1rem;
    }
}
