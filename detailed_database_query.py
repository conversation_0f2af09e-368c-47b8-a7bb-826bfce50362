#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细数据库查询脚本
查看具体的公司数据和配置信息
"""

import sqlite3
import json

def detailed_query():
    db_path = 'database/enterprise_service.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("详细数据库内容分析")
        print("=" * 80)
        
        # 1. 查看所有公司的基本信息
        print("\n1. 公司基本信息:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                company_name,
                unified_social_credit_code,
                legal_representative,
                registered_capital,
                business_status,
                company_short_name
            FROM companies
        """)
        
        companies = cursor.fetchall()
        for i, company in enumerate(companies, 1):
            print(f"公司{i}:")
            print(f"  企业名称: {company[0]}")
            print(f"  统一社会信用代码: {company[1]}")
            print(f"  法定代表人: {company[2]}")
            print(f"  注册资本: {company[3]} 万元")
            print(f"  经营状态: {company[4]}")
            print(f"  公司简称: {company[5]}")
            print()
        
        # 2. 查看公司的详细信息
        print("\n2. 公司详细信息:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                company_name,
                registered_address,
                business_description,
                contact_phone,
                contact_email,
                industry_category,
                finance_manager_name,
                finance_manager_phone
            FROM companies
        """)
        
        details = cursor.fetchall()
        for i, detail in enumerate(details, 1):
            print(f"公司{i} - {detail[0]}:")
            print(f"  注册地址: {detail[1]}")
            print(f"  业务描述: {detail[2][:100]}..." if detail[2] and len(detail[2]) > 100 else f"  业务描述: {detail[2]}")
            print(f"  联系电话: {detail[3]}")
            print(f"  联系邮箱: {detail[4]}")
            print(f"  行业类别: {detail[5]}")
            print(f"  财务经理: {detail[6]}")
            print(f"  财务经理电话: {detail[7]}")
            print()
        
        # 3. 查看信贷相关信息
        print("\n3. 信贷业务信息:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                company_name,
                credit_line_number,
                business_number,
                loan_amount,
                guarantee_amount,
                pledge_value
            FROM companies
        """)
        
        credit_info = cursor.fetchall()
        for i, credit in enumerate(credit_info, 1):
            print(f"公司{i} - {credit[0]}:")
            print(f"  授信额度编号: {credit[1]}")
            print(f"  业务编号: {credit[2]}")
            print(f"  贷款金额: {credit[3]}")
            print(f"  担保金额: {credit[4]}")
            print(f"  质押价值: {credit[5]}")
            print()
        
        # 4. 查看协定存款信息
        print("\n4. 协定存款信息:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                company_name,
                agreement_number,
                deposit_amount,
                interest_rate_adjustment,
                status,
                created_at
            FROM deposit_agreements
        """)
        
        deposits = cursor.fetchall()
        for i, deposit in enumerate(deposits, 1):
            print(f"协定存款{i}:")
            print(f"  公司名称: {deposit[0]}")
            print(f"  协议编号: {deposit[1]}")
            print(f"  存款金额: {deposit[2]} 万元")
            print(f"  利率调整: {deposit[3]} bp")
            print(f"  状态: {deposit[4]}")
            print(f"  创建时间: {deposit[5]}")
            print()
        
        # 5. 查看配置信息
        print("\n5. 系统配置信息:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                config_key,
                config_value,
                description
            FROM deposit_config
        """)
        
        configs = cursor.fetchall()
        for config in configs:
            print(f"  {config[2]}: {config[1]} (键: {config[0]})")
        
        # 6. 查看文档分类
        print("\n6. 文档分类:")
        print("-" * 50)
        cursor.execute("""
            SELECT 
                category_name,
                category_description,
                sort_order
            FROM document_categories
            ORDER BY sort_order
        """)
        
        categories = cursor.fetchall()
        for category in categories:
            print(f"  {category[2]}. {category[0]} - {category[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"查询数据库时出错: {e}")

if __name__ == "__main__":
    detailed_query()
