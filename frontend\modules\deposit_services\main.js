/**
 * 协定存款业务模块
 * 提供协定存款协议生成和管理功能
 */

class DepositServicesModule {
    constructor() {
        this.currentCompany = null;
        this.agreements = [];
        this.statistics = {};
    }

    /**
     * 模块初始化
     */
    async init(companyData) {
        console.log('初始化协定存款模块', companyData);
        this.currentCompany = companyData;
        
        // 渲染模块界面
        this.render();
        
        // 加载数据
        await this.loadData();
    }

    /**
     * 渲染模块界面
     */
    render() {
        const container = document.getElementById('module-content');
        if (!container) {
            console.error('找不到模块容器');
            return;
        }

        container.innerHTML = `
            <div class="deposit-module">
                <!-- 模块标题 -->
                <div class="module-header">
                    <h2>💰 协定存款业务</h2>
                    <p class="module-subtitle">为 ${this.currentCompany.company_name} 生成协定存款协议</p>
                </div>

                <!-- 协议生成区 -->
                <div class="agreement-generator">
                    <div class="generator-card">
                        <h3>📋 协议参数设置</h3>

                        <div class="form-group">
                            <label for="deposit-amount">存款金额（万元）：</label>
                            <input type="number" id="deposit-amount" class="form-input" value="1000" min="1" step="1">
                            <span class="input-hint">请输入协定存款金额</span>
                        </div>

                        <div class="form-actions">
                            <button id="generate-agreement-btn" class="btn btn-primary">
                                <span class="btn-icon">📄</span>
                                <span class="btn-text">生成协定存款协议</span>
                            </button>
                        </div>
                    </div>

                    <!-- 生成结果区 -->
                    <div id="generation-result" class="generation-result" style="display: none;">
                        <div class="result-card">
                            <div class="result-header">
                                <span class="result-icon">✅</span>
                                <h4>协议生成完成</h4>
                            </div>
                            <div class="result-content">
                                <p class="result-info">协定存款协议已成功生成</p>
                                <div class="download-section">
                                    <button id="download-agreement-btn" class="btn btn-success">
                                        <span class="btn-icon">📥</span>
                                        <span class="btn-text">下载协议文档</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载状态 -->
                    <div id="generation-loading" class="generation-loading" style="display: none;">
                        <div class="loading-card">
                            <div class="loading-spinner"></div>
                            <p class="loading-text">正在生成协议，请稍候...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 绑定事件处理
     */
    bindEvents() {
        // 生成协议按钮
        const generateBtn = document.getElementById('generate-agreement-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateAgreement());
        }

        // 下载协议按钮
        const downloadBtn = document.getElementById('download-agreement-btn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadAgreement());
        }
    }

    /**
     * 生成协议
     */
    async generateAgreement() {
        const depositAmount = document.getElementById('deposit-amount').value;

        if (!depositAmount || depositAmount <= 0) {
            alert('请输入有效的存款金额');
            return;
        }

        // 显示加载状态
        this.showLoading();

        try {
            const response = await fetch('http://127.0.0.1:5000/api/deposit/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: this.currentCompany.id,
                    deposit_amount: parseFloat(depositAmount)
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.showResult(result.data);
            } else {
                throw new Error(result.message || '生成协议失败');
            }

        } catch (error) {
            console.error('生成协议失败:', error);
            alert('生成协议失败: ' + error.message);
            this.hideLoading();
        }
    }

    /**
     * 下载协议
     */
    async downloadAgreement() {
        if (!this.generatedFile) {
            alert('没有可下载的文件');
            return;
        }

        try {
            const response = await fetch(`http://127.0.0.1:5000/api/deposit/download/${this.generatedFile.file_id}`);

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = this.generatedFile.filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                throw new Error('下载失败');
            }

        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败: ' + error.message);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        document.getElementById('generation-loading').style.display = 'block';
        document.getElementById('generation-result').style.display = 'none';
        document.getElementById('generate-agreement-btn').disabled = true;
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        document.getElementById('generation-loading').style.display = 'none';
        document.getElementById('generate-agreement-btn').disabled = false;
    }

    /**
     * 显示生成结果
     */
    showResult(data) {
        this.generatedFile = data;
        document.getElementById('generation-loading').style.display = 'none';
        document.getElementById('generation-result').style.display = 'block';
        document.getElementById('generate-agreement-btn').disabled = false;

        // 更新结果信息
        const resultInfo = document.querySelector('.result-info');
        if (resultInfo) {
            resultInfo.textContent = `协议文件：${data.filename}`;
        }
    }

    /**
     * 加载数据（简化版）
     */
    async loadData() {
        console.log('协定存款模块数据加载完成');
    }
}

// 创建全局实例
window.depositModule = new DepositServicesModule();
