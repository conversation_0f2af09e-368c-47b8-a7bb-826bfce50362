-- =====================================================
-- 扩展companies表结构
-- 添加缺失的企业详细信息字段
-- 创建时间: 2025-07-30
-- =====================================================

-- 为companies表添加详细信息字段
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS registered_address TEXT,
ADD COLUMN IF NOT EXISTS communication_address TEXT,
ADD COLUMN IF NOT EXISTS business_description TEXT,
ADD COLUMN IF NOT EXISTS legal_representative VARCHAR(100),
ADD COLUMN IF NOT EXISTS registration_date DATE,
ADD COLUMN IF NOT EXISTS registered_capital DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS business_scope TEXT,
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(100),
ADD COLUMN IF NOT EXISTS website VARCHAR(200),
ADD COLUMN IF NOT EXISTS industry_category VARCHAR(100),
ADD COLUMN IF NOT EXISTS company_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS business_status VARCHAR(20) DEFAULT 'active',
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 同样扩展companies_history表
ALTER TABLE companies_history 
ADD COLUMN IF NOT EXISTS registered_address TEXT,
ADD COLUMN IF NOT EXISTS communication_address TEXT,
ADD COLUMN IF NOT EXISTS business_description TEXT,
ADD COLUMN IF NOT EXISTS legal_representative VARCHAR(100),
ADD COLUMN IF NOT EXISTS registration_date DATE,
ADD COLUMN IF NOT EXISTS registered_capital DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS business_scope TEXT,
ADD COLUMN IF NOT EXISTS contact_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS contact_email VARCHAR(100),
ADD COLUMN IF NOT EXISTS website VARCHAR(200),
ADD COLUMN IF NOT EXISTS industry_category VARCHAR(100),
ADD COLUMN IF NOT EXISTS company_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS business_status VARCHAR(20);

-- 为现有的成都卫讯科技有限公司更新详细信息
UPDATE companies 
SET 
    registered_address = '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
    communication_address = '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
    business_description = '通信设备制造、软件开发、技术服务',
    legal_representative = '万明刚',
    registration_date = '2020-03-15',
    registered_capital = 1000000.00,
    business_scope = '通信设备制造；软件开发；技术服务；计算机系统服务；数据处理和存储服务',
    contact_phone = '028-85123456',
    contact_email = '<EMAIL>',
    industry_category = '制造业',
    company_type = '有限责任公司',
    business_status = 'active',
    updated_at = CURRENT_TIMESTAMP
WHERE id = '34af7659-d69a-4c05-a697-6ae6eb00aad3';

-- 为现有的成都中科卓尔智能科技集团有限公司更新详细信息
UPDATE companies 
SET 
    registered_address = '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
    communication_address = '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
    business_description = '智能科技研发、软件开发、技术咨询服务',
    legal_representative = '杨伟',
    registration_date = '2019-08-20',
    registered_capital = 5000000.00,
    business_scope = '智能科技研发；软件开发；技术咨询；人工智能技术服务；大数据服务',
    contact_phone = '028-86789012',
    contact_email = '<EMAIL>',
    industry_category = '科技服务业',
    company_type = '有限责任公司',
    business_status = 'active',
    updated_at = CURRENT_TIMESTAMP
WHERE id = 'a1b2c3d4-e5f6-7890-1234-567890abcdef';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为companies表创建更新时间触发器
DROP TRIGGER IF EXISTS update_companies_updated_at ON companies;
CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 验证数据更新
SELECT 
    company_name,
    registered_address,
    communication_address,
    business_description,
    legal_representative
FROM companies 
WHERE id IN (
    '34af7659-d69a-4c05-a697-6ae6eb00aad3',
    'a1b2c3d4-e5f6-7890-1234-567890abcdef'
);

COMMIT;
