"""
高级文档生成服务
支持一键生成和人机协作模式
"""

import os
import uuid
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_COLOR_INDEX
from io import BytesIO

logger = logging.getLogger(__name__)

class DocumentProcessingError(Exception):
    """文档处理异常"""
    pass

class AdvancedDocumentService:
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        self.templates_dir = Path(__file__).parent.parent.parent / 'templates' / 'yingqi_zhilian'
        logger.info(f"AdvancedDocumentService初始化，模板目录: {self.templates_dir}")
        logger.info(f"模板目录是否存在: {self.templates_dir.exists()}")
        
    def generate_service_agreement(self, company_id: str) -> BytesIO:
        """
        一键生成服务协议
        使用经过验证的无损替换技术
        """
        try:
            logger.info(f"开始生成服务协议，客户ID: {company_id}")

            # 获取公司信息
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到公司信息: {company_id}")

            logger.info(f"获取客户数据成功: {company_data.get('company_name', 'N/A')}")

            # 使用原始模板（包含具体企业信息的版本）
            template_path = self.templates_dir / 'yingqi_zhilian_agreement_blueprint.docx'
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            logger.info(f"使用模板文件: {template_path}")
            logger.info(f"模板大小: {template_path.stat().st_size} 字节")

            # 打开模板文档
            doc = Document(template_path)
            logger.info(f"模板加载成功，段落数: {len(doc.paragraphs)}, 表格数: {len(doc.tables)}")

            # 准备替换数据（使用经过验证的映射）
            replacements = self._prepare_verified_replacements(company_data)
            logger.info(f"准备了 {len(replacements)} 个替换项")

            # 执行经过验证的无损替换
            replacement_count = self._perform_verified_lossless_replacement(doc, replacements)
            logger.info(f"完成 {replacement_count} 次替换")

            # 保存到内存
            doc_buffer = BytesIO()
            doc.save(doc_buffer)
            doc_buffer.seek(0)

            logger.info(f"服务协议生成成功: {company_data['company_name']}")
            return doc_buffer

        except Exception as e:
            logger.error(f"生成服务协议失败: {e}")
            raise
    
    def prepare_oa_template(self, company_id: str) -> Dict[str, Any]:
        """
        准备OA正文模板和任务包
        返回半成品内容和Gemini任务包
        """
        try:
            # 获取公司信息
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到公司信息: {company_id}")
            
            # 加载模板
            template_path = self.templates_dir / 'yingqi_zhilian_oa_text_blueprint.docx'
            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")
            
            # 读取模板内容
            doc = Document(template_path)
            template_text = self._extract_text_from_document(doc)
            
            # 准备基础替换数据
            basic_replacements = self._prepare_basic_oa_replacements(company_data)
            
            # 执行基础替换
            semi_finished_content = self._replace_text_simple(template_text, basic_replacements)
            
            # 生成Gemini任务包
            task_package = self._generate_gemini_task_package(company_data)
            
            result = {
                'company_id': company_id,
                'company_name': company_data['company_name'],
                'semi_finished_content': semi_finished_content,
                'task_package': task_package,
                'template_path': str(template_path)
            }
            
            logger.info(f"OA模板准备成功: {company_data['company_name']}")
            return result
            
        except Exception as e:
            logger.error(f"准备OA模板失败: {e}")
            raise
    
    def complete_oa_document(self, company_id: str, semi_finished_content: str, 
                           gemini_response: str) -> BytesIO:
        """
        完成OA文档生成
        合并半成品内容和Gemini回复
        """
        try:
            # 获取公司信息
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到公司信息: {company_id}")
            
            # 创建新文档
            doc = Document()
            
            # 添加标题
            title = doc.add_heading(f'关于为{company_data["company_name"]}开通银企直联服务的申请', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加半成品内容
            self._add_content_to_document(doc, semi_finished_content)
            
            # 添加业务价值分析部分
            doc.add_heading('业务价值分析', level=1)
            self._add_content_to_document(doc, gemini_response)
            
            # 添加结尾
            doc.add_paragraph()
            footer_para = doc.add_paragraph('特此申请，请予批准。')
            footer_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # 添加日期和签名
            date_para = doc.add_paragraph(f'申请日期：{datetime.now().strftime("%Y年%m月%d日")}')
            date_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # 保存到内存
            doc_buffer = BytesIO()
            doc.save(doc_buffer)
            doc_buffer.seek(0)
            
            logger.info(f"OA文档生成成功: {company_data['company_name']}")
            return doc_buffer
            
        except Exception as e:
            logger.error(f"完成OA文档失败: {e}")
            raise
    
    def _get_company_data(self, company_id: str) -> Optional[Dict[str, Any]]:
        """获取公司数据"""
        logger.info(f"获取公司数据，ID: {company_id}")

        # 如果是模拟数据ID，直接返回模拟数据
        if company_id.startswith('mock'):
            logger.info(f"检测到模拟数据ID，返回模拟数据: {company_id}")
            company_name = '成都卫讯科技有限公司' if company_id == 'mock1' else '测试企业2'
            return {
                'id': company_id,
                'company_name': company_name,
                'unified_social_credit_code': '915101003320526751',
                'registered_address': '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                'communication_address': '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                'business_description': '通信设备制造、软件开发',
                'personnel': [{'person_name': '万明刚', 'role': '法定代表人'}],
                'tags': [
                    {'tag_name': '国家级高新技术企业', 'tag_category': '企业资质'},
                    {'tag_name': '省级专精特新企业', 'tag_category': '企业资质'},
                    {'tag_name': '制造业', 'tag_category': '行业分类'},
                    {'tag_name': '通信设备制造', 'tag_category': '行业分类'},
                    {'tag_name': '行业信贷政策-优先支持', 'tag_category': '政策支持'}
                ]
            }

        # 尝试通过数据库管理器获取数据
        if self.db_manager:
            try:
                company_data = self.db_manager.get_company_detail(company_id)
                if company_data:
                    logger.info(f"通过数据库获取数据成功: {company_data.get('company_name', 'N/A')}")
                    return company_data
            except Exception as e:
                logger.warning(f"数据库获取失败: {e}")

        # 如果数据库获取失败，尝试通过API获取数据
        try:
            company_data = self._get_company_info(company_id)
            if company_data:
                logger.info(f"通过API获取数据成功: {company_data.get('company_name', 'N/A')}")
                return company_data
        except Exception as e:
            logger.error(f"无法通过API获取公司数据: {e}")
            # 不使用默认数据，直接抛出异常
            raise ValueError(f"未找到公司信息: {company_id}")
        
        # 使用真实数据库
        try:
            query = """
            SELECT * FROM companies WHERE id = %s
            """
            company = self.db_manager.execute_single(query, (company_id,))
            if not company:
                return None
            
            # 获取人员信息
            personnel_query = """
            SELECT p.person_name, r.role_name as role
            FROM company_person_roles cpr
            JOIN persons p ON cpr.person_id = p.id
            JOIN roles r ON cpr.role_id = r.id
            WHERE cpr.company_id = %s AND cpr.is_active = true
            """
            personnel = self.db_manager.execute_query(personnel_query, (company_id,))
            
            # 获取标签信息
            tags_query = """
            SELECT t.tag_name, t.tag_category
            FROM company_tags ct
            JOIN tags t ON ct.tag_id = t.id
            WHERE ct.company_id = %s
            """
            tags = self.db_manager.execute_query(tags_query, (company_id,))
            
            result = dict(company)
            result['personnel'] = [dict(p) for p in personnel]
            result['tags'] = [dict(t) for t in tags]
            
            return result
            
        except Exception as e:
            logger.error(f"获取公司数据失败: {e}")
            return None
    
    def _prepare_agreement_replacements(self, company_data: Dict[str, Any]) -> Dict[str, str]:
        """
        准备服务协议的替换数据
        只读不改原则：只替换客户相关占位符，银行信息保持不变
        """
        # 获取法定代表人
        legal_person = next((p['person_name'] for p in company_data.get('personnel', [])
                           if p['role'] == '法定代表人'), '未知')

        # 获取授权经办人
        authorized_person = next((p['person_name'] for p in company_data.get('personnel', [])
                                if p['role'] == '授权经办人'), legal_person)

        # 获取财务负责人
        finance_person = next((p['person_name'] for p in company_data.get('personnel', [])
                             if p['role'] == '财务负责人'), legal_person)

        # 获取企业标签信息
        tags = company_data.get('tags', [])
        high_tech = any('高新技术' in tag.get('tag_name', '') for tag in tags)

        current_date = datetime.now().strftime('%Y年%m月%d日')
        current_year = datetime.now().strftime('%Y')

        # 只替换客户相关的占位符，银行信息（签约机构、负责人等）保持不变
        replacements = {
            # 企业基本信息
            '【公司全称】': company_data.get('company_name', ''),
            '【公司名称】': company_data.get('company_name', ''),
            '【企业名称】': company_data.get('company_name', ''),
            '【统一社会信用代码】': company_data.get('unified_social_credit_code', ''),
            '【信用代码】': company_data.get('unified_social_credit_code', ''),

            # 人员信息
            '【法定代表人】': legal_person,
            '【法人代表】': legal_person,
            '【法人】': legal_person,
            '【授权经办人】': authorized_person,
            '【财务负责人】': finance_person,

            # 日期信息
            '【签署日期】': current_date,
            '【申请日期】': current_date,
            '【当前日期】': current_date,
            '【年份】': current_year,

            # 地址信息
            '【注册地址】': company_data.get('registered_address', '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号'),
            '【通讯地址】': company_data.get('communication_address', '成都市武侯区天府五街200号'),

            # 企业性质
            '【企业性质】': '高新技术企业' if high_tech else '一般企业'
        }

        logger.info(f"准备了 {len(replacements)} 个客户相关占位符的替换数据")
        return replacements

    def _prepare_verified_replacements(self, company_data: Dict[str, Any]) -> Dict[str, str]:
        """
        准备经过验证的替换数据
        使用与test_doc_generator.py完全相同的逻辑
        """
        # 获取法定代表人
        legal_person = next((p['person_name'] for p in company_data.get('personnel', [])
                           if p['role'] == '法定代表人'), '万明刚')

        # 构建完整的注册地址
        registered_address = company_data.get('registered_address')
        if not registered_address:
            registered_address = "中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号D区B5栋18层1805-1808号"

        # 使用与测试脚本完全相同的替换映射
        replacements = {
            "成都中科卓尔智能科技集团有限公司": company_data.get('company_name', '成都卫讯科技有限公司'),
            "中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区8楼": registered_address,
            "杨伟": legal_person
        }

        logger.info("经过验证的替换数据:")
        for old_value, new_value in replacements.items():
            logger.info(f"  {old_value[:30]}... → {new_value[:30]}...")

        return replacements

    def _perform_verified_lossless_replacement(self, doc: Document, replacements: Dict[str, str]) -> int:
        """
        执行经过验证的无损替换
        与test_doc_generator.py中的逻辑完全一致
        """
        replacement_count = 0

        # 替换段落中的文本
        for paragraph in doc.paragraphs:
            count = self._replace_in_paragraph_verified(paragraph, replacements)
            replacement_count += count

        # 替换表格中的文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        count = self._replace_in_paragraph_verified(paragraph, replacements)
                        replacement_count += count

        return replacement_count

    def _replace_in_paragraph_verified(self, paragraph, replacements: Dict[str, str]) -> int:
        """
        在段落中执行经过验证的无损替换
        与test_doc_generator.py中的逻辑完全一致
        """
        if not paragraph.runs:
            return 0

        replacement_count = 0

        # 获取段落完整文本
        full_text = ''.join(run.text for run in paragraph.runs)

        # 检查是否包含目标占位符
        for placeholder, replacement in replacements.items():
            if placeholder in full_text:
                success = self._replace_placeholder_in_runs_verified(paragraph, placeholder, replacement)
                if success:
                    replacement_count += 1
                    logger.info(f"  ✅ 替换: {placeholder[:30]}... → {replacement[:30]}...")
                    # 重新获取文本以便下次替换
                    full_text = ''.join(run.text for run in paragraph.runs)

        return replacement_count

    def _replace_placeholder_in_runs_verified(self, paragraph, placeholder: str, replacement: str) -> bool:
        """
        在runs中替换占位符，保持格式完整性
        与test_doc_generator.py中的逻辑完全一致
        """
        runs = paragraph.runs
        if not runs:
            return False

        # 构建文本映射
        run_texts = [run.text for run in runs]
        full_text = ''.join(run_texts)

        # 查找占位符位置
        placeholder_start = full_text.find(placeholder)
        if placeholder_start == -1:
            return False

        placeholder_end = placeholder_start + len(placeholder)

        # 找到占位符跨越的runs
        current_pos = 0
        affected_runs = []
        start_run_info = None
        end_run_info = None

        for run_idx, run_text in enumerate(run_texts):
            run_start = current_pos
            run_end = current_pos + len(run_text)

            # 检查这个run是否与占位符有交集
            if run_end > placeholder_start and run_start < placeholder_end:
                affected_runs.append(run_idx)

                # 记录开始run
                if start_run_info is None:
                    start_run_info = {
                        'run_idx': run_idx,
                        'char_pos': placeholder_start - run_start
                    }

                # 记录结束run
                if run_end >= placeholder_end:
                    end_run_info = {
                        'run_idx': run_idx,
                        'char_pos': placeholder_end - run_start
                    }
                    break

            current_pos = run_end

        if not start_run_info or not end_run_info:
            return False

        # 执行替换
        return self._execute_run_replacement_verified(runs, start_run_info, end_run_info, replacement)

    def _execute_run_replacement_verified(self, runs, start_run_info, end_run_info, replacement: str) -> bool:
        """
        执行run级别的替换
        与test_doc_generator.py中的逻辑完全一致
        """
        try:
            start_run_idx = start_run_info['run_idx']
            end_run_idx = end_run_info['run_idx']

            if start_run_idx == end_run_idx:
                # 占位符在同一个run中
                run = runs[start_run_idx]
                start_pos = start_run_info['char_pos']
                end_pos = end_run_info['char_pos']

                # 保存原始格式
                original_format = self._copy_run_format_verified(run)

                # 执行替换
                new_text = (run.text[:start_pos] + replacement + run.text[end_pos:])
                run.text = new_text

                # 恢复格式并添加高亮
                self._apply_run_format_verified(run, original_format)
                self._add_highlight_to_replacement(run, start_pos, len(replacement))

            else:
                # 占位符跨越多个runs
                self._handle_multi_run_replacement_verified(runs, start_run_info, end_run_info, replacement)

            return True

        except Exception as e:
            logger.error(f"替换执行失败: {e}")
            return False

    def _handle_multi_run_replacement_verified(self, runs, start_run_info, end_run_info, replacement: str):
        """
        处理跨越多个runs的占位符替换
        与test_doc_generator.py中的逻辑完全一致
        """
        start_run_idx = start_run_info['run_idx']
        end_run_idx = end_run_info['run_idx']

        # 保存第一个run的格式
        first_run = runs[start_run_idx]
        replacement_format = self._copy_run_format_verified(first_run)

        # 修改第一个run：保留前缀 + 替换文本
        start_pos = start_run_info['char_pos']
        first_run.text = first_run.text[:start_pos] + replacement
        self._apply_run_format_verified(first_run, replacement_format)

        # 为替换文本添加高亮
        self._add_highlight_to_replacement(first_run, start_pos, len(replacement))

        # 修改最后一个run：只保留后缀
        end_pos = end_run_info['char_pos']
        last_run = runs[end_run_idx]
        last_run.text = last_run.text[end_pos:]

        # 清空中间的runs
        for run_idx in range(start_run_idx + 1, end_run_idx):
            runs[run_idx].text = ""

    def _copy_run_format_verified(self, run):
        """
        复制run的格式信息
        与test_doc_generator.py中的逻辑完全一致
        """
        try:
            return {
                'font_name': run.font.name,
                'font_size': run.font.size,
                'bold': run.font.bold,
                'italic': run.font.italic,
                'underline': run.font.underline
            }
        except:
            return {}

    def _apply_run_format_verified(self, run, format_info):
        """
        应用格式信息到run
        与test_doc_generator.py中的逻辑完全一致
        """
        try:
            if format_info.get('font_name'):
                run.font.name = format_info['font_name']
            if format_info.get('font_size'):
                run.font.size = format_info['font_size']
            if format_info.get('bold') is not None:
                run.font.bold = format_info['bold']
            if format_info.get('italic') is not None:
                run.font.italic = format_info['italic']
            if format_info.get('underline') is not None:
                run.font.underline = format_info['underline']
        except Exception as e:
            logger.warning(f"格式应用失败: {e}")
    
    def _prepare_basic_oa_replacements(self, company_data: Dict[str, Any]) -> Dict[str, str]:
        """准备OA正文的基础替换数据"""
        legal_person = next((p['person_name'] for p in company_data.get('personnel', []) 
                           if p['role'] == '法定代表人'), '未知')
        
        return {
            '【公司全称】': company_data.get('company_name', ''),
            '【统一社会信用代码】': company_data.get('unified_social_credit_code', ''),
            '【法定代表人】': legal_person,
            '【申请日期】': datetime.now().strftime('%Y年%m月%d日')
        }
    
    def _generate_gemini_task_package(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成Gemini任务包"""
        # 整理企业背景信息
        background_info = []
        background_info.append(f"企业名称：{company_data.get('company_name', '')}")
        background_info.append(f"统一社会信用代码：{company_data.get('unified_social_credit_code', '')}")
        
        # 添加人员信息
        personnel = company_data.get('personnel', [])
        if personnel:
            background_info.append("关键人员：")
            for person in personnel:
                background_info.append(f"  • {person['person_name']} - {person['role']}")
        
        # 添加企业标签
        tags = company_data.get('tags', [])
        if tags:
            background_info.append("企业特征：")
            for tag in tags:
                background_info.append(f"  • {tag['tag_name']} ({tag['tag_category']})")
        
        # 生成提示词
        prompt = f"""你是一位专业的银行客户经理，请基于以下客户的背景信息，为我行内部OA系统撰写一段关于"为该客户开通银企直联服务的业务价值说明"。

客户背景信息：
{chr(10).join(background_info)}

请从以下角度分析业务价值：
1. 客户资质和信用状况
2. 业务发展潜力
3. 对银行的战略价值
4. 风险评估
5. 预期收益

要求：
- 语言专业、客观
- 突出客户优势
- 体现银企直联服务的必要性
- 字数控制在300-500字
- 使用银行业务术语"""

        return {
            'background_materials': background_info,
            'suggested_prompt': prompt,
            'instructions': [
                '1. 复制上述提示词到Gemini',
                '2. 获取Gemini的回复',
                '3. 将回复内容粘贴到下方文本框',
                '4. 点击"完成并生成文档"按钮'
            ]
        }
    
    def _replace_text_in_document(self, doc: Document, replacements: Dict[str, str]):
        """
        在Word文档中替换文本，完美保留格式
        使用非破坏性的精细化替换算法
        """
        logger.info(f"开始文档替换，共 {len(replacements)} 个占位符")

        # 替换段落中的文本
        for i, paragraph in enumerate(doc.paragraphs):
            self._replace_text_in_paragraph_advanced(paragraph, replacements)

        # 替换表格中的文本
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        self._replace_text_in_paragraph_advanced(paragraph, replacements)

        logger.info("文档替换完成")

    def _replace_text_in_paragraph_advanced(self, paragraph, replacements: Dict[str, str]):
        """
        高级段落文本替换，完美保留格式
        """
        if not paragraph.runs:
            return

        # 获取段落的完整文本
        full_text = ''.join(run.text for run in paragraph.runs)

        # 检查是否包含任何占位符
        has_placeholder = False
        for placeholder in replacements.keys():
            if placeholder in full_text:
                has_placeholder = True
                break

        if not has_placeholder:
            return

        # 执行替换
        for placeholder, replacement in replacements.items():
            if placeholder in full_text:
                self._replace_placeholder_in_runs(paragraph, placeholder, replacement)
                # 重新获取文本以便下次替换
                full_text = ''.join(run.text for run in paragraph.runs)

    def _replace_placeholder_in_runs(self, paragraph, placeholder: str, replacement: str):
        """
        在runs中替换占位符，保持格式完整性
        """
        runs = paragraph.runs
        if not runs:
            return

        # 构建run映射表
        run_texts = [run.text for run in runs]
        full_text = ''.join(run_texts)

        # 查找占位符位置
        placeholder_start = full_text.find(placeholder)
        if placeholder_start == -1:
            return

        placeholder_end = placeholder_start + len(placeholder)

        # 找到占位符跨越的runs
        current_pos = 0
        start_run_info = None
        end_run_info = None
        affected_runs = []

        for run_idx, run_text in enumerate(run_texts):
            run_start = current_pos
            run_end = current_pos + len(run_text)

            # 检查这个run是否与占位符有交集
            if run_end > placeholder_start and run_start < placeholder_end:
                affected_runs.append(run_idx)

                # 记录开始run
                if start_run_info is None:
                    start_run_info = {
                        'run_idx': run_idx,
                        'char_pos': placeholder_start - run_start
                    }

                # 记录结束run
                if run_end >= placeholder_end:
                    end_run_info = {
                        'run_idx': run_idx,
                        'char_pos': placeholder_end - run_start
                    }
                    break

            current_pos = run_end

        if not start_run_info or not end_run_info:
            return

        # 执行替换
        self._perform_run_replacement(
            paragraph, affected_runs, start_run_info, end_run_info,
            placeholder, replacement
        )

    def _perform_run_replacement(self, paragraph, affected_runs, start_run_info,
                                end_run_info, placeholder, replacement):
        """
        执行run级别的替换，保持格式完整性
        """
        runs = paragraph.runs
        start_run_idx = start_run_info['run_idx']
        end_run_idx = end_run_info['run_idx']

        if start_run_idx == end_run_idx:
            # 占位符在同一个run中 - 最简单的情况
            run = runs[start_run_idx]
            start_pos = start_run_info['char_pos']
            end_pos = end_run_info['char_pos']

            # 保存原始格式
            original_font = self._copy_run_format(run)

            # 执行替换
            new_text = (run.text[:start_pos] + replacement + run.text[end_pos:])
            run.text = new_text

            # 确保格式保持不变
            self._apply_run_format(run, original_font)

        else:
            # 占位符跨越多个runs - 复杂情况
            self._handle_multi_run_replacement(
                paragraph, affected_runs, start_run_info, end_run_info, replacement
            )

    def _handle_multi_run_replacement(self, paragraph, affected_runs,
                                    start_run_info, end_run_info, replacement):
        """
        处理跨越多个runs的占位符替换
        """
        runs = paragraph.runs
        start_run_idx = start_run_info['run_idx']
        end_run_idx = end_run_info['run_idx']

        # 保存第一个run的格式用于替换文本
        first_run = runs[start_run_idx]
        replacement_format = self._copy_run_format(first_run)

        # 修改第一个run：保留前缀 + 替换文本
        start_pos = start_run_info['char_pos']
        first_run.text = first_run.text[:start_pos] + replacement

        # 修改最后一个run：只保留后缀
        end_pos = end_run_info['char_pos']
        last_run = runs[end_run_idx]
        last_run.text = last_run.text[end_pos:]

        # 清空中间的runs（但保留run对象以维持格式结构）
        for run_idx in range(start_run_idx + 1, end_run_idx):
            runs[run_idx].text = ""

        # 确保替换文本使用正确的格式
        self._apply_run_format(first_run, replacement_format)

    def _copy_run_format(self, run):
        """
        复制run的格式信息
        """
        try:
            return {
                'font_name': run.font.name,
                'font_size': run.font.size,
                'bold': run.font.bold,
                'italic': run.font.italic,
                'underline': run.font.underline,
                'color': run.font.color.rgb if run.font.color.rgb else None
            }
        except:
            # 如果获取格式失败，返回默认格式
            return {
                'font_name': None,
                'font_size': None,
                'bold': None,
                'italic': None,
                'underline': None,
                'color': None
            }

    def _apply_run_format(self, run, format_info):
        """
        应用格式信息到run
        """
        try:
            if format_info['font_name']:
                run.font.name = format_info['font_name']
            if format_info['font_size']:
                run.font.size = format_info['font_size']
            if format_info['bold'] is not None:
                run.font.bold = format_info['bold']
            if format_info['italic'] is not None:
                run.font.italic = format_info['italic']
            if format_info['underline'] is not None:
                run.font.underline = format_info['underline']
            if format_info['color']:
                run.font.color.rgb = format_info['color']
        except Exception as e:
            # 格式应用失败时记录但不中断流程
            logger.warning(f"应用格式失败: {e}")
    
    def _extract_text_from_document(self, doc: Document) -> str:
        """从Word文档中提取文本"""
        text_parts = []
        for paragraph in doc.paragraphs:
            text_parts.append(paragraph.text)
        return '\n'.join(text_parts)
    
    def _replace_text_simple(self, text: str, replacements: Dict[str, str]) -> str:
        """简单文本替换"""
        for placeholder, replacement in replacements.items():
            text = text.replace(placeholder, replacement)
        return text
    
    def _add_content_to_document(self, doc: Document, content: str):
        """向文档添加内容"""
        paragraphs = content.split('\n')
        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text.strip())

    def _add_highlight_to_replacement(self, run, start_pos: int, replacement_length: int):
        """
        为替换的文本添加高亮显示
        注意：由于python-docx的限制，这里采用简化的高亮策略
        """
        try:
            # 为整个run设置高亮（简化处理）
            run.font.highlight_color = WD_COLOR_INDEX.YELLOW
            logger.debug(f"为替换文本设置黄色高亮，长度: {replacement_length}")
        except Exception as e:
            logger.warning(f"设置高亮失败: {e}")
            # 如果高亮失败，尝试设置背景色或其他视觉标识
            try:
                # 可以尝试其他视觉标识，比如加粗
                run.font.bold = True
                logger.debug("高亮失败，改为设置加粗")
            except Exception as e2:
                logger.warning(f"设置加粗也失败: {e2}")

    def _add_highlight_to_new_run(self, paragraph, replacement_text: str, original_format: dict):
        """
        创建新的高亮run来显示替换文本
        这是一个更精确的高亮方法，适用于跨run替换的情况
        """
        try:
            # 创建新的run
            new_run = paragraph.add_run(replacement_text)

            # 应用原始格式
            if original_format:
                self._apply_run_format_verified(new_run, original_format)

            # 设置高亮
            new_run.font.highlight_color = WD_COLOR_INDEX.YELLOW
            logger.debug(f"创建高亮run: {replacement_text}")

            return new_run
        except Exception as e:
            logger.warning(f"创建高亮run失败: {e}")
            # 降级处理：创建普通run
            new_run = paragraph.add_run(replacement_text)
            if original_format:
                self._apply_run_format_verified(new_run, original_format)
            return new_run

    def prepare_oa_document(self, company_id: str) -> dict:
        """
        生成OA文档的半成品版本，用于人机协作工作台
        返回包含文档内容和元数据的字典
        """
        try:
            logger.info(f"开始为公司 {company_id} 准备OA半成品文档")

            # 获取公司信息
            company_info = self._get_company_info(company_id)
            if not company_info:
                raise ValueError(f"未找到公司信息: {company_id}")

            # 加载OA模板
            template_path = self._get_oa_template_path()
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"OA模板文件不存在: {template_path}")

            # 打开模板文档
            doc = Document(template_path)

            # 执行自动替换并高亮
            replacements_made = self._perform_oa_auto_replacements(doc, company_info)

            # 提取文档内容为HTML格式（用于前端显示）
            html_content = self._extract_document_as_html(doc)

            # 保存半成品文档到临时位置
            temp_doc_path = self._save_temp_document(doc, company_id, "oa_draft")

            logger.info(f"OA半成品文档准备完成，替换了 {len(replacements_made)} 处内容")

            return {
                'html_content': html_content,
                'temp_doc_path': temp_doc_path,
                'replacements_made': replacements_made,
                'company_info': company_info,
                'template_sections': self._identify_oa_sections(doc)
            }

        except Exception as e:
            logger.error(f"准备OA半成品文档失败: {e}")
            raise

    def _get_oa_template_path(self) -> str:
        """获取OA模板文件路径"""
        # 模板文件路径
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "templates", "yingqi_zhilian")
        template_path = os.path.join(template_dir, "yingqi_zhilian_oa_text_blueprint.docx")
        return template_path

    def _get_company_info(self, company_id: str) -> dict:
        """获取公司信息"""
        try:
            if self.db_manager:
                # 使用数据库管理器获取公司信息
                company_info = self.db_manager.get_company_detail(company_id)
                if company_info:
                    return company_info
                else:
                    raise ValueError(f"未找到公司信息: {company_id}")
            else:
                # 如果没有数据库管理器，使用API调用
                import requests
                response = requests.get(f"http://localhost:5000/api/company/{company_id}")
                if response.status_code == 200:
                    return response.json()["data"]
                else:
                    raise ValueError(f"无法获取公司信息: {company_id}")
        except Exception as e:
            logger.error(f"获取公司信息失败: {e}")
            raise

    def _perform_oa_auto_replacements(self, doc: Document, company_info: dict) -> list:
        """
        执行OA文档的自动替换并高亮
        """
        replacements_made = []

        # 定义替换映射
        replacement_map = {
            '【公司全称】': company_info.get('company_name', ''),
            '【公司名称】': company_info.get('company_name', ''),
            '【客户名称】': company_info.get('company_name', ''),
            '【法定代表人】': company_info.get('legal_representative', ''),
            '【注册地址】': company_info.get('registered_address', ''),
            '【联系电话】': company_info.get('contact_phone', ''),
            '【统一社会信用代码】': company_info.get('unified_social_credit_code', ''),
            '【注册资本】': f"{company_info.get('registered_capital', '')}万元" if company_info.get('registered_capital') else '',
            '【行业分类】': company_info.get('industry_category', ''),
            '【经营范围】': company_info.get('business_scope', '')
        }

        # 执行替换
        for placeholder, replacement in replacement_map.items():
            if replacement:  # 只替换有值的字段
                single_replacement = {placeholder: replacement}
                count = self._perform_verified_lossless_replacement(
                    doc, single_replacement
                )
                if count > 0:
                    replacements_made.append({
                        'placeholder': placeholder,
                        'replacement': replacement,
                        'count': count
                    })
                    logger.debug(f"替换 {placeholder} -> {replacement} ({count} 处)")

        return replacements_made

    def _extract_document_as_html(self, doc: Document) -> str:
        """
        将Word文档内容提取为HTML格式，用于前端显示
        """
        try:
            html_parts = []
            html_parts.append('<div class="document-content">')

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # 检查段落样式
                    style_class = self._get_paragraph_style_class(paragraph)
                    html_parts.append(f'<p class="{style_class}">')

                    # 处理段落中的runs
                    for run in paragraph.runs:
                        if run.text:
                            run_html = self._convert_run_to_html(run)
                            html_parts.append(run_html)

                    html_parts.append('</p>')

            # 处理表格
            for table in doc.tables:
                html_parts.append('<table class="doc-table">')
                for row in table.rows:
                    html_parts.append('<tr>')
                    for cell in row.cells:
                        html_parts.append('<td>')
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                html_parts.append(f'<p>{paragraph.text}</p>')
                        html_parts.append('</td>')
                    html_parts.append('</tr>')
                html_parts.append('</table>')

            html_parts.append('</div>')
            return ''.join(html_parts)

        except Exception as e:
            logger.warning(f"提取文档HTML失败: {e}")
            return '<div class="document-content"><p>文档内容提取失败</p></div>'

    def _get_paragraph_style_class(self, paragraph) -> str:
        """获取段落的CSS样式类"""
        try:
            if paragraph.style.name.startswith('Heading'):
                return 'doc-heading'
            elif 'Title' in paragraph.style.name:
                return 'doc-title'
            else:
                return 'doc-paragraph'
        except:
            return 'doc-paragraph'

    def _convert_run_to_html(self, run) -> str:
        """将run转换为HTML"""
        try:
            text = run.text
            if not text:
                return ''

            # 检查是否有高亮
            is_highlighted = False
            try:
                if run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                    is_highlighted = True
            except:
                pass

            # 检查格式
            is_bold = run.font.bold
            is_italic = run.font.italic

            # 构建HTML
            if is_highlighted:
                text = f'<mark class="auto-replaced">{text}</mark>'

            if is_bold:
                text = f'<strong>{text}</strong>'

            if is_italic:
                text = f'<em>{text}</em>'

            return text

        except Exception as e:
            logger.warning(f"转换run为HTML失败: {e}")
            return run.text if run.text else ''

    def _save_temp_document(self, doc: Document, company_id: str, doc_type: str) -> str:
        """保存临时文档"""
        try:
            # 创建临时目录
            temp_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "temp")
            os.makedirs(temp_dir, exist_ok=True)

            # 生成临时文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"{company_id}_{doc_type}_{timestamp}.docx"
            temp_path = os.path.join(temp_dir, temp_filename)

            # 保存文档
            doc.save(temp_path)

            logger.debug(f"临时文档已保存: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"保存临时文档失败: {e}")
            raise

    def _identify_oa_sections(self, doc: Document) -> list:
        """
        识别OA文档中的主要章节，用于生成Gemini提示词
        """
        sections = []

        try:
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text and ('一、' in text or '二、' in text or '三、' in text or '四、' in text):
                    sections.append({
                        'title': text,
                        'needs_ai_content': '客户综合情况' in text or '业务价值' in text or '费用减免' in text
                    })
        except Exception as e:
            logger.warning(f"识别文档章节失败: {e}")

        return sections

    def finalize_oa_document(self, company_id: str, temp_doc_path: str, ai_content: str) -> BytesIO:
        """
        合成最终的OA文档
        """
        try:
            logger.info(f"开始合成最终OA文档: {company_id}")

            # 检查临时文档是否存在
            if not os.path.exists(temp_doc_path):
                raise FileNotFoundError(f"临时文档不存在: {temp_doc_path}")

            # 打开半成品文档
            doc = Document(temp_doc_path)

            # 查找需要插入AI内容的位置
            self._insert_ai_content_to_oa(doc, ai_content)

            # 保存到内存
            doc_buffer = BytesIO()
            doc.save(doc_buffer)
            doc_buffer.seek(0)

            # 清理临时文件
            try:
                os.remove(temp_doc_path)
                logger.debug(f"已清理临时文件: {temp_doc_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

            logger.info(f"OA文档合成完成: {company_id}")
            return doc_buffer

        except Exception as e:
            logger.error(f"合成OA文档失败: {e}")
            raise

    def _insert_ai_content_to_oa(self, doc: Document, ai_content: str):
        """
        将AI生成的内容插入到OA文档的适当位置
        """
        try:
            # 查找插入点（通常是"一、客户综合情况"和"二、业务价值及费用减免说明"之后）
            insertion_points = []

            for i, paragraph in enumerate(doc.paragraphs):
                text = paragraph.text.strip()
                if ('一、客户综合情况' in text or
                    '二、业务价值' in text or
                    '客户综合情况' in text or
                    '业务价值' in text):
                    insertion_points.append(i + 1)  # 在标题后插入

            if insertion_points:
                # 在第一个插入点后添加AI内容
                insert_index = insertion_points[0]

                # 分割AI内容为段落
                ai_paragraphs = ai_content.split('\n\n')

                # 从后往前插入，避免索引变化
                for j, ai_para in enumerate(reversed(ai_paragraphs)):
                    if ai_para.strip():
                        # 在指定位置插入新段落
                        new_paragraph = doc.paragraphs[insert_index].insert_paragraph_before(ai_para.strip())
                        # 设置段落格式
                        new_paragraph.style = doc.styles['Normal']

                        # 为AI生成的内容添加轻微的视觉标识（可选）
                        for run in new_paragraph.runs:
                            run.font.color.rgb = None  # 保持默认颜色

                logger.debug(f"AI内容已插入到文档中，共 {len(ai_paragraphs)} 个段落")
            else:
                # 如果找不到特定插入点，在文档末尾添加
                doc.add_paragraph("\n" + ai_content)
                logger.warning("未找到特定插入点，AI内容已添加到文档末尾")

        except Exception as e:
            logger.error(f"插入AI内容失败: {e}")
            # 降级处理：在文档末尾添加
            try:
                doc.add_paragraph("\n" + ai_content)
                logger.info("降级处理：AI内容已添加到文档末尾")
            except Exception as e2:
                logger.error(f"降级处理也失败: {e2}")
                raise
