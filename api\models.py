"""
数据模型和验证器
定义企业信息相关的数据结构和验证规则
"""

import re
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class OperationType(Enum):
    """操作类型枚举"""
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"

class IDType(Enum):
    """证件类型枚举"""
    ID_CARD = "身份证"
    PASSPORT = "护照"
    OTHER = "其他"

@dataclass
class CompanyCreateRequest:
    """公司创建请求模型"""
    unified_social_credit_code: str
    company_name: str
    operated_by_user_id: str
    operation_reason: str = "新增公司信息"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class CompanyUpdateRequest:
    """公司更新请求模型"""
    unified_social_credit_code: Optional[str] = None
    company_name: Optional[str] = None
    operated_by_user_id: str = None
    operation_reason: str = "更新公司信息"
    
    def to_dict(self) -> Dict[str, Any]:
        return {k: v for k, v in asdict(self).items() if v is not None}

@dataclass
class PersonCreateRequest:
    """人员创建请求模型"""
    id_number: str
    id_type: str
    person_name: str
    operated_by_user_id: str
    operation_reason: str = "新增人员信息"
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_credit_code(code: str) -> bool:
        """验证统一社会信用代码格式"""
        if not code or len(code) != 18:
            return False
        
        # 统一社会信用代码格式：18位，第1位为数字或大写字母，其余为数字或大写字母
        pattern = r'^[0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}$'
        return bool(re.match(pattern, code))
    
    @staticmethod
    def validate_id_number(id_number: str, id_type: str) -> bool:
        """验证证件号码格式"""
        if not id_number:
            return False
        
        if id_type == IDType.ID_CARD.value:
            # 身份证号码验证（15位或18位）
            if len(id_number) == 15:
                return id_number.isdigit()
            elif len(id_number) == 18:
                return re.match(r'^\d{17}[\dXx]$', id_number) is not None
            return False
        elif id_type == IDType.PASSPORT.value:
            # 护照号码验证（简单格式检查）
            return len(id_number) >= 6 and len(id_number) <= 20
        
        return True  # 其他类型暂不严格验证
    
    @staticmethod
    def validate_company_name(name: str) -> bool:
        """验证公司名称"""
        if not name or len(name.strip()) == 0:
            return False
        if len(name) > 500:
            return False
        return True
    
    @staticmethod
    def validate_person_name(name: str) -> bool:
        """验证人员姓名"""
        if not name or len(name.strip()) == 0:
            return False
        if len(name) > 100:
            return False
        return True
    
    @staticmethod
    def validate_uuid(uuid_str: str) -> bool:
        """验证UUID格式"""
        try:
            uuid.UUID(uuid_str)
            return True
        except ValueError:
            return False

class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, field: str, message: str):
        self.field = field
        self.message = message
        super().__init__(f"{field}: {message}")

class CompanyValidator:
    """公司数据验证器"""
    
    @staticmethod
    def validate_create_request(data: Dict[str, Any]) -> CompanyCreateRequest:
        """验证公司创建请求"""
        errors = []
        
        # 验证统一社会信用代码
        credit_code = data.get('unified_social_credit_code', '').strip()
        if not DataValidator.validate_credit_code(credit_code):
            errors.append(ValidationError('unified_social_credit_code', '统一社会信用代码格式不正确'))
        
        # 验证公司名称
        company_name = data.get('company_name', '').strip()
        if not DataValidator.validate_company_name(company_name):
            errors.append(ValidationError('company_name', '公司名称不能为空且长度不能超过500字符'))
        
        # 验证操作人ID
        operated_by = data.get('operated_by_user_id', '').strip()
        if not DataValidator.validate_uuid(operated_by):
            errors.append(ValidationError('operated_by_user_id', '操作人ID格式不正确'))
        
        if errors:
            raise ValidationError('validation_failed', f"验证失败: {'; '.join([str(e) for e in errors])}")
        
        return CompanyCreateRequest(
            unified_social_credit_code=credit_code,
            company_name=company_name,
            operated_by_user_id=operated_by,
            operation_reason=data.get('operation_reason', '新增公司信息')
        )
    
    @staticmethod
    def validate_update_request(data: Dict[str, Any]) -> CompanyUpdateRequest:
        """验证公司更新请求"""
        errors = []
        
        # 验证统一社会信用代码（如果提供）
        credit_code = data.get('unified_social_credit_code')
        if credit_code is not None:
            credit_code = credit_code.strip()
            if not DataValidator.validate_credit_code(credit_code):
                errors.append(ValidationError('unified_social_credit_code', '统一社会信用代码格式不正确'))
        
        # 验证公司名称（如果提供）
        company_name = data.get('company_name')
        if company_name is not None:
            company_name = company_name.strip()
            if not DataValidator.validate_company_name(company_name):
                errors.append(ValidationError('company_name', '公司名称不能为空且长度不能超过500字符'))
        
        # 验证操作人ID
        operated_by = data.get('operated_by_user_id', '').strip()
        if not DataValidator.validate_uuid(operated_by):
            errors.append(ValidationError('operated_by_user_id', '操作人ID格式不正确'))
        
        if errors:
            raise ValidationError('validation_failed', f"验证失败: {'; '.join([str(e) for e in errors])}")
        
        return CompanyUpdateRequest(
            unified_social_credit_code=credit_code,
            company_name=company_name,
            operated_by_user_id=operated_by,
            operation_reason=data.get('operation_reason', '更新公司信息')
        )
