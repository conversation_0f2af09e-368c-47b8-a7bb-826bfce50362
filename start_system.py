#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
银企直联系统启动脚本
自动启动API服务和Web服务器
"""

import subprocess
import time
import os
import sys
import threading
import webbrowser
from pathlib import Path

def start_api_server():
    """启动API服务器"""
    print("🚀 启动API服务器...")
    try:
        # 切换到项目根目录
        os.chdir(Path(__file__).parent)
        
        # 启动API服务
        process = subprocess.Popen([
            sys.executable, "-m", "api.app"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("✅ API服务器已启动 (端口: 5000)")
        return process
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        return None

def start_web_server():
    """启动Web服务器"""
    print("🌐 启动Web服务器...")
    try:
        # 切换到frontend目录
        frontend_dir = Path(__file__).parent / "frontend"
        os.chdir(frontend_dir)
        
        # 启动Web服务器
        process = subprocess.Popen([
            sys.executable, "-m", "http.server", "8080"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print("✅ Web服务器已启动 (端口: 8080)")
        return process
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        return None

def check_api_health():
    """检查API服务健康状态"""
    import requests
    max_retries = 10
    for i in range(max_retries):
        try:
            response = requests.get("http://localhost:5000/health", timeout=2)
            if response.status_code == 200:
                print("✅ API服务健康检查通过")
                return True
        except:
            pass
        
        print(f"⏳ 等待API服务启动... ({i+1}/{max_retries})")
        time.sleep(2)
    
    print("❌ API服务健康检查失败")
    return False

def main():
    print("=" * 60)
    print("🏦 银企直联系统启动器")
    print("=" * 60)
    
    # 启动API服务器
    api_process = start_api_server()
    if not api_process:
        print("❌ 系统启动失败：API服务器启动失败")
        return
    
    # 等待API服务启动
    time.sleep(3)
    
    # 检查API健康状态
    if not check_api_health():
        print("❌ 系统启动失败：API服务不健康")
        api_process.terminate()
        return
    
    # 启动Web服务器
    web_process = start_web_server()
    if not web_process:
        print("❌ 系统启动失败：Web服务器启动失败")
        api_process.terminate()
        return
    
    # 等待Web服务器启动
    time.sleep(2)
    
    print("\n" + "=" * 60)
    print("🎉 系统启动成功！")
    print("=" * 60)
    print("📊 API服务器: http://localhost:5000")
    print("🌐 Web服务器: http://localhost:8080")
    print("🏦 银企直联驾驶舱: http://localhost:8080/index_cockpit.html")
    print("🤝 客户接洽模块: http://localhost:8080/index_customer_engagement.html")
    print("=" * 60)
    
    # 自动打开浏览器
    try:
        print("🌐 正在打开浏览器...")
        webbrowser.open("http://localhost:8080/index_cockpit.html")
    except:
        print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
    
    print("\n💡 提示：")
    print("   - 按 Ctrl+C 停止所有服务")
    print("   - 如果遇到问题，请检查端口5000和8080是否被占用")
    
    try:
        # 保持服务运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        api_process.terminate()
        web_process.terminate()
        print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()
