#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准插入"还款来源"字段到放款条件落实情况表
专门处理还款来源字段的精确定位、提取和插入
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import Pt
from pathlib import Path
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseRepaymentSourceInserter:
    """精准还款来源插入器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
        self.business_doc_path = self.project_root / "templates" / "contract_disbursement" / "业务申报书.docx"
        self.output_path = self.project_root / "test_output" / "test_还款来源插入结果.docx"
        
        # 确保输出目录存在
        self.output_path.parent.mkdir(exist_ok=True)
        
        # 颜色定义
        self.yellow_color = WD_COLOR_INDEX.YELLOW
        
        # 验证结果
        self.verification_results = {
            'found_placeholder': False,
            'found_title': False,
            'extracted_paragraphs': 0,
            'content_preview': '',
            'inserted_paragraphs': 0,
            'format_preserved': False
        }
    
    def insert_repayment_source(self):
        """执行还款来源字段插入"""
        logger.info("🎯 开始精准插入还款来源字段")
        logger.info("="*60)
        
        # 1. 验证文件存在
        if not self._verify_files():
            return None
        
        # 2. 从业务申报书提取还款来源内容
        repayment_content = self._extract_repayment_source_content()
        if not repayment_content:
            logger.error("❌ 未能提取到还款来源内容")
            return None
        
        # 3. 加载模板文档
        doc = docx.Document(self.template_path)
        
        # 4. 查找并替换{{还款来源}}占位符
        success = self._find_and_replace_placeholder(doc, repayment_content)
        if not success:
            logger.error("❌ 未能找到或替换{{还款来源}}占位符")
            return None
        
        # 5. 保存文档
        doc.save(self.output_path)
        logger.info(f"✅ 文档保存完成: {self.output_path}")
        
        # 6. 输出验证结果
        self._output_verification_results()
        
        return self.output_path
    
    def _verify_files(self):
        """验证所需文件是否存在"""
        logger.info("📁 验证文件存在性...")
        
        files_to_check = [
            ("模板文件", self.template_path),
            ("业务申报书", self.business_doc_path)
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                logger.info(f"   ✅ {name}: {path.name}")
            else:
                logger.error(f"   ❌ {name}不存在: {path}")
                all_exist = False
        
        return all_exist
    
    def _extract_repayment_source_content(self):
        """从业务申报书提取还款来源内容"""
        logger.info("📄 从业务申报书提取还款来源内容...")
        
        try:
            doc = docx.Document(self.business_doc_path)
            
            # 查找"四、还款来源"标题
            title_found = False
            content_paragraphs = []
            
            for i, paragraph in enumerate(doc.paragraphs):
                paragraph_text = paragraph.text.strip()
                
                # 检查是否找到还款来源标题
                if not title_found:
                    if self._is_repayment_source_title(paragraph_text):
                        logger.info(f"   ✅ 找到还款来源标题: {paragraph_text}")
                        title_found = True
                        self.verification_results['found_title'] = True
                        continue
                
                # 如果已找到标题，开始收集内容
                if title_found:
                    # 检查是否遇到下一个一级标题
                    if self._is_next_major_title(paragraph_text):
                        logger.info(f"   📍 遇到下一个标题，停止提取: {paragraph_text}")
                        break
                    
                    # 收集非空段落
                    if paragraph_text and len(paragraph_text) > 5:
                        content_paragraphs.append(paragraph)
                        logger.info(f"   📝 提取段落: {paragraph_text[:30]}...")
            
            if not title_found:
                logger.error("   ❌ 未找到'四、还款来源'标题")
                return None
            
            if not content_paragraphs:
                logger.error("   ❌ 未找到还款来源内容段落")
                return None
            
            self.verification_results['extracted_paragraphs'] = len(content_paragraphs)
            self.verification_results['content_preview'] = content_paragraphs[0].text[:20] if content_paragraphs else ''
            
            logger.info(f"   ✅ 成功提取 {len(content_paragraphs)} 个段落")
            return content_paragraphs
            
        except Exception as e:
            logger.error(f"   ❌ 提取失败: {e}")
            return None
    
    def _is_repayment_source_title(self, text):
        """判断是否为还款来源标题"""
        # 匹配各种可能的还款来源标题格式
        patterns = [
            r'^四、\s*还款来源',
            r'^4、\s*还款来源',
            r'^四\s*、\s*还款来源',
            r'^\d+\.\s*还款来源',
            r'^还款来源$',
            r'^还款来源\s*：',
            r'^还款来源\s*审查'
        ]
        
        for pattern in patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return True
        
        return False
    
    def _is_next_major_title(self, text):
        """判断是否为下一个一级标题"""
        # 匹配一级标题格式
        patterns = [
            r'^[一二三四五六七八九十]+、',
            r'^[1-9]\d*、',
            r'^[1-9]\d*\.',
            r'^第[一二三四五六七八九十]+[章节部分]',
            r'^附件',
            r'^结论',
            r'^总结'
        ]
        
        for pattern in patterns:
            if re.match(pattern, text):
                # 排除还款来源本身
                if '还款来源' not in text:
                    return True
        
        return False
    
    def _find_and_replace_placeholder(self, doc, content_paragraphs):
        """查找并替换还款来源插入位置"""
        logger.info("🔍 查找还款来源插入位置...")

        # 尝试多种可能的插入位置标识
        target_phrases = [
            "{{还款来源}}",
            "还款来源审查",
            "审批批复中列明的放款条件及落实情况",
            "授信申报书及业务申报书中列明的放款条件及落实情况"
        ]

        found_placeholder = False

        # 遍历表格中的段落（优先在表格中查找）
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for paragraph in cell.paragraphs:
                        cell_text = cell.text.strip()

                        # 检查是否匹配目标短语
                        for phrase in target_phrases:
                            if phrase in cell_text:
                                logger.info(f"   ✅ 找到插入位置: 表格{table_idx}-行{row_idx}-列{cell_idx}")
                                logger.info(f"      原内容: {cell_text}")
                                found_placeholder = True
                                self.verification_results['found_placeholder'] = True

                                # 在此位置插入内容
                                success = self._insert_content_in_cell(cell, content_paragraphs)
                                if success:
                                    return True

        # 如果在表格中没找到，尝试在段落中查找
        for paragraph in doc.paragraphs:
            paragraph_text = paragraph.text.strip()
            for phrase in target_phrases:
                if phrase in paragraph_text:
                    logger.info(f"   ✅ 在段落中找到插入位置: {paragraph_text}")
                    found_placeholder = True
                    self.verification_results['found_placeholder'] = True

                    # 替换段落内容
                    success = self._replace_placeholder_in_paragraph(paragraph, phrase, content_paragraphs)
                    if success:
                        return True

        if not found_placeholder:
            logger.error("   ❌ 未找到合适的插入位置")
            logger.info("   💡 尝试在第一个表格的最后添加还款来源内容...")
            return self._append_to_first_table(doc, content_paragraphs)

        return False

    def _insert_content_in_cell(self, cell, content_paragraphs):
        """在表格单元格中插入内容"""
        logger.info("   🔄 在表格单元格中插入内容...")

        try:
            # 清空单元格现有内容
            cell.text = ""

            # 添加标题
            title_paragraph = cell.paragraphs[0]
            title_run = title_paragraph.add_run("还款来源审查")
            title_run.font.name = '宋体'
            title_run.font.size = Pt(12)
            title_run.font.bold = True

            # 添加内容段落
            for i, source_paragraph in enumerate(content_paragraphs):
                # 添加新段落
                new_paragraph = cell.add_paragraph()
                run = new_paragraph.add_run(source_paragraph.text)

                # 设置格式
                self._apply_format_to_run(run)

                logger.info(f"      ✅ 插入段落 {i+1}: {source_paragraph.text[:30]}...")

            self.verification_results['inserted_paragraphs'] = len(content_paragraphs)
            self.verification_results['format_preserved'] = True

            logger.info(f"   ✅ 成功在单元格中插入 {len(content_paragraphs)} 个段落")
            return True

        except Exception as e:
            logger.error(f"   ❌ 单元格插入失败: {e}")
            return False

    def _append_to_first_table(self, doc, content_paragraphs):
        """在第一个表格末尾添加还款来源内容"""
        logger.info("   🔄 在第一个表格末尾添加内容...")

        try:
            if not doc.tables:
                logger.error("   ❌ 文档中没有表格")
                return False

            table = doc.tables[0]

            # 添加新行
            new_row = table.add_row()

            # 在第一个单元格添加标题
            title_cell = new_row.cells[0]
            title_cell.text = "还款来源审查"

            # 在第二个单元格添加内容
            if len(new_row.cells) > 1:
                content_cell = new_row.cells[1]

                # 添加内容
                for i, source_paragraph in enumerate(content_paragraphs):
                    if i > 0:
                        content_cell.add_paragraph()

                    paragraph = content_cell.paragraphs[-1] if content_cell.paragraphs else content_cell.add_paragraph()
                    run = paragraph.add_run(source_paragraph.text)

                    # 设置格式
                    self._apply_format_to_run(run)

                    logger.info(f"      ✅ 插入段落 {i+1}: {source_paragraph.text[:30]}...")

            self.verification_results['found_placeholder'] = True
            self.verification_results['inserted_paragraphs'] = len(content_paragraphs)
            self.verification_results['format_preserved'] = True

            logger.info(f"   ✅ 成功在表格末尾添加 {len(content_paragraphs)} 个段落")
            return True

        except Exception as e:
            logger.error(f"   ❌ 表格添加失败: {e}")
            return False

    def _replace_placeholder_in_paragraph(self, paragraph, placeholder, content_paragraphs):
        """在段落中替换占位符"""
        logger.info("   🔄 替换占位符...")
        
        try:
            # 清空段落内容
            paragraph.clear()
            
            # 插入提取的内容
            for i, source_paragraph in enumerate(content_paragraphs):
                # 创建新的段落内容
                if i > 0:
                    # 添加段落分隔
                    paragraph.add_run("\n")
                
                # 添加段落文本
                run = paragraph.add_run(source_paragraph.text)
                
                # 设置格式
                self._apply_format_to_run(run)
                
                logger.info(f"      ✅ 插入段落 {i+1}: {source_paragraph.text[:30]}...")
            
            self.verification_results['inserted_paragraphs'] = len(content_paragraphs)
            self.verification_results['format_preserved'] = True
            
            logger.info(f"   ✅ 成功插入 {len(content_paragraphs)} 个段落")
            return True
            
        except Exception as e:
            logger.error(f"   ❌ 替换失败: {e}")
            return False
    
    def _apply_format_to_run(self, run):
        """应用格式到文本运行"""
        try:
            # 设置字体
            run.font.name = '宋体'
            run.font.size = Pt(12)
            
            # 设置黄色高亮背景
            run.font.highlight_color = self.yellow_color
            
            logger.debug("      ✅ 格式应用成功: 宋体12pt，黄色高亮")
            
        except Exception as e:
            logger.warning(f"      ⚠️ 格式应用失败: {e}")
    
    def _output_verification_results(self):
        """输出验证结果"""
        logger.info("\n" + "="*60)
        logger.info("🧪 结果验证输出")
        logger.info("="*60)
        
        results = self.verification_results
        
        logger.info(f"✅ 是否找到 {{{{还款来源}}}} 占位符: {results['found_placeholder']}")
        logger.info(f"✅ 是否成功定位'还款来源'标题: {results['found_title']}")
        logger.info(f"📝 提取了几段内容: {results['extracted_paragraphs']} 段")
        logger.info(f"📄 内容预览: {results['content_preview']}")
        logger.info(f"📋 插入段落数量: {results['inserted_paragraphs']}")
        logger.info(f"🎨 插入后是否保留原格式: {results['format_preserved']}")
        
        if results['format_preserved']:
            logger.info("   📌 格式详情: 字体=宋体, 字号=12pt, 背景=黄色高亮")
        
        logger.info("="*60)


def main():
    """主函数"""
    print("🎯 精准插入还款来源字段测试")
    print("="*60)
    
    inserter = PreciseRepaymentSourceInserter()
    
    try:
        output_path = inserter.insert_repayment_source()
        
        if output_path:
            print(f"\n✅ 任务完成!")
            print(f"📁 输出文件: {output_path}")
            print(f"📊 文件大小: {output_path.stat().st_size} 字节")
            print(f"\n🔍 请人工验证输出文档:")
            print(f"   1. 检查{{还款来源}}占位符是否被正确替换")
            print(f"   2. 检查插入内容的格式是否正确")
            print(f"   3. 检查段落结构是否保持完整")
            print(f"   4. 检查黄色高亮是否正确应用")
        else:
            print("❌ 任务失败，请检查日志信息")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
