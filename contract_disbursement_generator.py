#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同支用文件生成器
基于数据库数据生成中科卓尔的合同支用相关文件
严格保持模板格式不变
"""

import sqlite3
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import docx
from docx.shared import RGBColor
from docx.enum.dml import MSO_COLOR_TYPE
import openpyxl
from openpyxl.styles import PatternFill
import uuid

class ContractDisbursementGenerator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_dir = self.project_root / "templates" / "contract_disbursement"
        self.output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        # 中科卓尔公司ID
        self.zkzr_company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
        
    def get_company_data(self):
        """获取中科卓尔的完整数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取公司基本信息
            cursor.execute("""
                SELECT * FROM companies 
                WHERE id = ?
            """, (self.zkzr_company_id,))
            
            company_row = cursor.fetchone()
            if not company_row:
                raise Exception("未找到中科卓尔公司数据")
            
            # 获取列名
            cursor.execute("PRAGMA table_info(companies)")
            columns = [row[1] for row in cursor.fetchall()]
            
            company_data = dict(zip(columns, company_row))
            
            # 获取合同信息
            cursor.execute("""
                SELECT * FROM contracts 
                WHERE company_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (self.zkzr_company_id,))
            
            contract_row = cursor.fetchone()
            if contract_row:
                cursor.execute("PRAGMA table_info(contracts)")
                contract_columns = [row[1] for row in cursor.fetchall()]
                company_data['contract'] = dict(zip(contract_columns, contract_row))
            
            conn.close()
            return company_data
            
        except Exception as e:
            print(f"获取公司数据失败: {e}")
            return None
    
    def generate_all_files(self, disbursement_amount=1000):
        """生成所有合同支用相关文件"""
        print("🏦 开始生成中科卓尔合同支用文件...")
        
        company_data = self.get_company_data()
        if not company_data:
            print("❌ 无法获取公司数据")
            return
        
        print(f"📊 公司数据: {company_data['company_name']}")
        
        # 生成各类文件
        files_generated = []
        
        try:
            # 1. 提款申请书
            file1 = self.generate_drawdown_application(company_data, disbursement_amount)
            if file1:
                files_generated.append(file1)
            
            # 2. 信贷业务申请书
            file2 = self.generate_credit_application(company_data)
            if file2:
                files_generated.append(file2)
            
            # 3. 落实情况表
            file3 = self.generate_condition_checklist(company_data, disbursement_amount)
            if file3:
                files_generated.append(file3)
            
            # 4. 合同制作清单
            file4 = self.generate_contract_checklist(company_data, disbursement_amount)
            if file4:
                files_generated.append(file4)
            
            # 5. 规模台账
            file5 = self.generate_scale_ledger(company_data, disbursement_amount)
            if file5:
                files_generated.append(file5)
            
            print(f"\n🎉 文件生成完成！共生成 {len(files_generated)} 个文件:")
            for file_path in files_generated:
                print(f"  📄 {file_path}")
                
        except Exception as e:
            print(f"❌ 文件生成失败: {e}")
            import traceback
            traceback.print_exc()
    
    def generate_drawdown_application(self, company_data, disbursement_amount):
        """生成提款申请书"""
        try:
            template_path = self.template_dir / "提款申请书.docx"
            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None
            
            # 复制模板
            output_path = self.output_dir / f"提款申请书_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(template_path, output_path)
            
            # 打开文档进行替换
            doc = docx.Document(output_path)
            
            # 替换数据
            replacements = {
                '建八卓尔（2025）001号': company_data.get('contract', {}).get('contract_number', '建八卓尔（2025）001号'),
                '壹仟万元整': self.amount_to_chinese(disbursement_amount),
                '用于公司向日常经营周转需求': company_data.get('standard_loan_purpose', '用于公司向日常经营周转需求'),
                '51050148850800008651': company_data.get('primary_account', '51050148850800008651')
            }
            
            # 执行替换
            self.replace_in_document(doc, replacements)
            
            doc.save(output_path)
            print(f"✅ 生成提款申请书: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成提款申请书失败: {e}")
            return None
    
    def generate_credit_application(self, company_data):
        """生成信贷业务申请书"""
        try:
            template_path = self.template_dir / "信贷业务申请书.xlsx"
            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None
            
            output_path = self.output_dir / f"信贷业务申请书_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            shutil.copy2(template_path, output_path)
            
            # 打开Excel进行替换
            workbook = openpyxl.load_workbook(output_path)
            
            # 在所有工作表中进行替换
            replacements = {
                '神光光学集团有限公司': company_data.get('company_name', ''),
                '贾秉炜': company_data.get('legal_representative', ''),
                '18607.4012': str(company_data.get('registered_capital', '')),
                '钟蓬川': company_data.get('finance_manager', ''),
                '13980435363': company_data.get('finance_manager_phone', ''),
                '75930': str(company_data.get('total_assets', '')),
                '53752': str(company_data.get('total_liabilities', ''))
            }
            
            self.replace_in_excel(workbook, replacements)
            
            workbook.save(output_path)
            print(f"✅ 生成信贷业务申请书: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成信贷业务申请书失败: {e}")
            return None
    
    def generate_condition_checklist(self, company_data, disbursement_amount):
        """生成落实情况表"""
        try:
            template_path = self.template_dir / "落实情况表.docx"
            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None
            
            output_path = self.output_dir / f"落实情况表_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(template_path, output_path)
            
            doc = docx.Document(output_path)
            
            # 当前日期
            current_date = datetime.now()
            
            replacements = {
                '成都中科卓尔智能科技集团有限公司': company_data.get('company_name', ''),
                '2025': str(current_date.year),
                '3': str(current_date.month),
                '40,000,000.00': str(disbursement_amount * 10000),  # 转换为元
                '4000.00': str(disbursement_amount),
                '建八卓尔（2025）001号': company_data.get('contract', {}).get('contract_number', '建八卓尔（2025）001号')
            }
            
            self.replace_in_document(doc, replacements)
            
            doc.save(output_path)
            print(f"✅ 生成落实情况表: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成落实情况表失败: {e}")
            return None
    
    def generate_contract_checklist(self, company_data, disbursement_amount):
        """生成合同制作清单"""
        try:
            template_path = self.template_dir / "合同制作清单.xlsm"
            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None
            
            output_path = self.output_dir / f"合同制作清单_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsm"
            shutil.copy2(template_path, output_path)
            
            print(f"✅ 生成合同制作清单: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成合同制作清单失败: {e}")
            return None
    
    def generate_scale_ledger(self, company_data, disbursement_amount):
        """生成规模台账"""
        try:
            template_path = self.template_dir / "规模台账.xlsm"
            if not template_path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None
            
            output_path = self.output_dir / f"规模台账_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsm"
            shutil.copy2(template_path, output_path)
            
            print(f"✅ 生成规模台账: {output_path.name}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成规模台账失败: {e}")
            return None
    
    def replace_in_document(self, doc, replacements):
        """在Word文档中进行替换，保持格式"""
        # 替换段落中的文本
        for para in doc.paragraphs:
            for old_text, new_text in replacements.items():
                if old_text in para.text:
                    self.replace_text_in_paragraph(para, old_text, str(new_text))
        
        # 替换表格中的文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        for old_text, new_text in replacements.items():
                            if old_text in para.text:
                                self.replace_text_in_paragraph(para, old_text, str(new_text))
    
    def replace_text_in_paragraph(self, paragraph, old_text, new_text):
        """在段落中替换文本，保持格式"""
        if old_text in paragraph.text:
            # 简单替换，保持原有格式
            for run in paragraph.runs:
                if old_text in run.text:
                    run.text = run.text.replace(old_text, new_text)
    
    def replace_in_excel(self, workbook, replacements):
        """在Excel中进行替换"""
        for sheet_name in workbook.sheetnames:
            sheet = workbook[sheet_name]
            for row in sheet.iter_rows():
                for cell in row:
                    if cell.value:
                        cell_value = str(cell.value)
                        for old_text, new_text in replacements.items():
                            if old_text in cell_value:
                                cell.value = cell_value.replace(old_text, new_text)
    
    def amount_to_chinese(self, amount):
        """将数字金额转换为中文大写"""
        # 简化版本，实际应用中可以使用更完整的转换
        chinese_numbers = {
            1000: '壹仟万元整',
            1500: '壹仟伍佰万元整',
            2000: '贰仟万元整',
            2500: '贰仟伍佰万元整',
            3000: '叁仟万元整'
        }
        return chinese_numbers.get(amount, f'{amount}万元整')

def main():
    """主函数"""
    generator = ContractDisbursementGenerator()
    
    # 生成中科卓尔的文件，支用金额1000万元
    generator.generate_all_files(disbursement_amount=1000)

if __name__ == "__main__":
    main()
