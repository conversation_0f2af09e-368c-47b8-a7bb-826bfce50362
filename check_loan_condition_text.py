#!/usr/bin/env python3
"""
检查贷款条件的具体提示文字
"""

import docx
from pathlib import Path

def check_loan_condition_text():
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    doc = docx.Document(template_file)
    
    target_cell = doc.tables[0].rows[8].cells[0]
    cell_text = target_cell.text
    
    print('=== 检查贷款条件提示文字 ===\n')
    print('完整单元格内容:')
    print(cell_text)
    print('\n' + '='*80 + '\n')
    
    # 查找包含"业务申报书"的部分
    lines = cell_text.split('\n')
    for i, line in enumerate(lines):
        if '业务申报书' in line:
            print(f'行{i+1}: {line}')
    
    # 查找包含"贷款条件"的部分
    print('\n包含"贷款条件"的行:')
    for i, line in enumerate(lines):
        if '贷款条件' in line:
            print(f'行{i+1}: {line}')

if __name__ == "__main__":
    check_loan_condition_text()
