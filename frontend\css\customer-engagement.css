/* 客户接洽与资料准备工作台样式 */

/* 基础布局 */
.workspace-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.workspace-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #4CAF50;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.back-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.module-title h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.5rem;
}

.module-subtitle {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.workspace-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 主工作区 */
.workspace-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* 通用区块样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.3rem;
}

.section-description {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* 客户信息区 */
.customer-info-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.customer-lock-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #28a745;
    font-size: 0.9rem;
}

.customer-info-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
}

.customer-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.info-value {
    color: #2c3e50;
    font-weight: 500;
}

/* 工具分发区 */
.tools-distribution-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.tool-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #4CAF50;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.tool-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tool-icon {
    font-size: 2rem;
}

.tool-header h3 {
    margin: 0;
    color: #2c3e50;
}

.tool-description p {
    margin: 0 0 1rem 0;
    color: #6c757d;
    line-height: 1.5;
}

.tool-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tool-btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.tool-btn:active {
    transform: translateY(0);
}

/* 文档管理区 */
.documents-management-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 文件上传区 */
.upload-area {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.upload-header h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.file-input-wrapper {
    margin-bottom: 1.5rem;
}

.file-input {
    display: none;
}

.file-input-label {
    display: block;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.file-input-label:hover {
    border-color: #4CAF50;
    background: #f0f8f0;
}

.upload-icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 0.5rem;
}

.upload-text {
    display: block;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.upload-hint {
    display: block;
    font-size: 0.9rem;
    color: #6c757d;
}

.upload-options {
    display: grid;
    grid-template-columns: 1fr 2fr auto;
    gap: 1rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #495057;
}

.form-select,
.form-input {
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 0.9rem;
}

.upload-btn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.upload-btn:hover:not(:disabled) {
    background: #0056b3;
}

.upload-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 文档列表区 */
.documents-list-area {
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.list-header h3 {
    margin: 0;
    color: #2c3e50;
}

.refresh-btn {
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #5a6268;
}

.documents-table-wrapper {
    overflow-x: auto;
}

.documents-table {
    width: 100%;
    border-collapse: collapse;
}

.documents-table th,
.documents-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.documents-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.no-data-text {
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

/* 状态提示 */
.status-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.status-toast.show {
    transform: translateX(0);
}

.status-toast.success {
    background: #28a745;
}

.status-toast.error {
    background: #dc3545;
}

.status-toast.info {
    background: #17a2b8;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    margin-top: 1rem;
    font-weight: 500;
}

/* 文档操作按钮 */
.doc-action-btn {
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.doc-action-btn:hover {
    background: #5a6268;
}

.doc-action-btn.delete {
    background: #dc3545;
}

.doc-action-btn.delete:hover {
    background: #c82333;
}

/* 文件大小显示 */
.file-size {
    color: #6c757d;
    font-size: 0.9rem;
}

/* 文档分类标签 */
.category-tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .workspace-main {
        padding: 1rem;
    }

    .upload-options {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .customer-basic-info {
        grid-template-columns: 1fr;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }
}
