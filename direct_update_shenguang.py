#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接通过数据库更新神光光学集团有限公司信息
"""

import sqlite3
import json
from datetime import datetime

def update_shenguang_direct():
    """直接通过数据库更新神光光学信息"""
    
    # 数据库文件路径
    db_path = "database/enterprise_service.db"
    
    # 神光光学集团有限公司ID
    company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
    
    # 更新信息
    legal_representative = "贾秉炜"
    registered_address = "四川省成都市成华区建设南街9号2层"
    
    print("🔄 直接通过数据库更新神光光学集团有限公司信息...")
    print("=" * 60)
    print(f"🏢 公司ID: {company_id}")
    print(f"👤 法定代表人: {legal_representative}")
    print(f"📍 注册地址: {registered_address}")
    print()
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 首先检查当前数据
        print("📋 检查当前数据...")
        cursor.execute("""
            SELECT company_name, unified_social_credit_code, registered_address,
                   legal_representative, business_description
            FROM companies
            WHERE id = ?
        """, (company_id,))
        
        current_data = cursor.fetchone()
        if current_data:
            print("✅ 找到公司记录:")
            print(f"   公司名称: {current_data[0]}")
            print(f"   信用代码: {current_data[1]}")
            print(f"   注册地址: {current_data[2] or '待补充'}")
            print(f"   法定代表人: {current_data[3] or '待补充'}")
            print(f"   业务描述: {current_data[4] or '待补充'}")
        else:
            print("❌ 未找到公司记录")
            return False
        
        # 更新数据
        print("\n🔄 执行更新...")
        update_sql = """
            UPDATE companies
            SET
                legal_representative = ?,
                registered_address = ?,
                updated_at = ?
            WHERE id = ?
        """
        
        current_time = datetime.now().isoformat()
        cursor.execute(update_sql, (
            legal_representative,
            registered_address, 
            current_time,
            company_id
        ))
        
        # 检查是否有行被更新
        if cursor.rowcount > 0:
            print(f"✅ 成功更新 {cursor.rowcount} 条记录")
            
            # 提交事务
            conn.commit()
            
            # 验证更新结果
            print("\n🔍 验证更新结果...")
            cursor.execute("""
                SELECT company_name, unified_social_credit_code, registered_address,
                       legal_representative, business_description, updated_at
                FROM companies
                WHERE id = ?
            """, (company_id,))
            
            updated_data = cursor.fetchone()
            if updated_data:
                print("✅ 更新后的数据:")
                print(f"   公司名称: {updated_data[0]}")
                print(f"   信用代码: {updated_data[1]}")
                print(f"   注册地址: {updated_data[2]}")
                print(f"   法定代表人: {updated_data[3]}")
                print(f"   业务描述: {updated_data[4]}")
                print(f"   更新时间: {updated_data[5]}")
                
                # 检查关键字段是否正确更新
                if updated_data[3] == legal_representative:
                    print("   ✅ 法定代表人更新正确")
                else:
                    print("   ❌ 法定代表人更新失败")
                    
                if updated_data[2] == registered_address:
                    print("   ✅ 注册地址更新正确")
                else:
                    print("   ❌ 注册地址更新失败")
                    
                return True
            else:
                print("❌ 验证失败：无法获取更新后的数据")
                return False
        else:
            print("❌ 没有记录被更新")
            return False
            
    except sqlite3.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False
    finally:
        if conn:
            conn.close()

def verify_through_api():
    """通过API验证更新结果"""
    import requests
    
    print("\n📡 通过API验证更新结果...")
    
    try:
        company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
        response = requests.get(f"http://localhost:5000/api/company/{company_id}", timeout=10)
        
        if response.status_code == 200:
            data = response.json()["data"]
            
            print("✅ API验证成功:")
            print(f"   🏢 公司名称: {data.get('company_name')}")
            print(f"   🆔 信用代码: {data.get('unified_social_credit_code')}")
            print(f"   👤 法定代表人: {data.get('legal_representative')}")
            print(f"   📍 注册地址: {data.get('registered_address')}")
            print(f"   📋 业务描述: {data.get('business_description')}")
            
            # 检查关键字段
            if data.get('legal_representative') == "贾秉炜":
                print("   ✅ API显示法定代表人正确")
            else:
                print("   ❌ API显示法定代表人不正确")
                
            if "四川省成都市成华区建设南街9号2层" in str(data.get('registered_address', '')):
                print("   ✅ API显示注册地址正确")
            else:
                print("   ❌ API显示注册地址不正确")
                
        else:
            print(f"❌ API验证失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ API验证异常: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 神光光学集团有限公司信息直接更新")
    print("📋 任务: 通过数据库直接更新法人代表和注册地址")
    print("=" * 60)
    
    # 执行数据库更新
    success = update_shenguang_direct()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 数据库更新成功!")
        
        # 通过API验证
        verify_through_api()
        
        print("\n" + "=" * 60)
        print("✅ 神光光学集团有限公司信息已完善")
        print("🚀 现在可以为该公司提供完整的企业服务")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 数据库更新失败!")
        print("请检查数据库连接和表结构")
        print("=" * 60)
