/**
 * 银企直联业务流程管理器
 * 负责管理业务办理清单的状态和交互
 */

class WorkflowManager {
    constructor() {
        this.currentCompany = null;
        this.workflowItems = [
            'serviceAgreement',
            'oaDocument', 
            'authorizationForm',
            'applicationForm',
            'documentsCollected',
            'systemSubmitted'
        ];
        this.initializeElements();
        this.bindEvents();
        this.updateProgress();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            // 工作流程相关
            workflowSection: document.getElementById('businessWorkflowSection'),
            progressCount: document.getElementById('progressCount'),
            progressFill: document.getElementById('progressFill'),
            resetWorkflowBtn: document.getElementById('resetWorkflowBtn'),
            exportWorkflowBtn: document.getElementById('exportWorkflowBtn'),
            
            // 复选框
            checkboxes: {},
            actionButtons: {}
        };

        // 获取所有复选框和按钮
        this.workflowItems.forEach(itemId => {
            this.elements.checkboxes[itemId] = document.getElementById(itemId);
            const actionBtn = document.querySelector(`[data-template]`);
            if (actionBtn) {
                this.elements.actionButtons[itemId] = actionBtn;
            }
        });

        // 获取所有操作按钮
        this.elements.allActionButtons = document.querySelectorAll('.action-btn[data-template]');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 复选框变更事件
        this.workflowItems.forEach(itemId => {
            const checkbox = this.elements.checkboxes[itemId];
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    this.onCheckboxChange(itemId);
                });
            }
        });

        // 操作按钮事件
        this.elements.allActionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.onActionButtonClick(e.target);
            });
        });

        // 重置流程按钮
        this.elements.resetWorkflowBtn.addEventListener('click', () => {
            this.resetWorkflow();
        });

        // 导出进度报告按钮
        this.elements.exportWorkflowBtn.addEventListener('click', () => {
            this.exportProgressReport();
        });
    }

    /**
     * 设置当前公司
     */
    setCurrentCompany(company) {
        this.currentCompany = company;
        this.updateActionButtonsState();
    }

    /**
     * 复选框状态变更处理
     */
    onCheckboxChange(itemId) {
        const checkbox = this.elements.checkboxes[itemId];
        const isChecked = checkbox.checked;
        
        console.log(`工作流程项 ${itemId} 状态变更: ${isChecked ? '已完成' : '未完成'}`);
        
        // 更新进度
        this.updateProgress();
        
        // 记录日志
        if (window.uiManager) {
            const itemName = this.getItemDisplayName(itemId);
            const status = isChecked ? '已完成' : '取消完成';
            window.uiManager.addLog(`工作流程: ${itemName} - ${status}`, isChecked ? 'success' : 'info');
        }
    }

    /**
     * 操作按钮点击处理
     */
    async onActionButtonClick(button) {
        if (!this.currentCompany) {
            if (window.uiManager) {
                window.uiManager.showError('请先选择一个客户企业');
            }
            return;
        }

        const template = button.dataset.template;
        const isWordTemplate = template.endsWith('.docx');
        const isPdfTemplate = template.endsWith('.pdf');

        try {
            button.disabled = true;
            button.textContent = '处理中...';

            if (window.uiManager) {
                window.uiManager.addLog(`正在处理文档: ${template}...`);
            }

            // 根据模板类型选择不同的处理方式
            if (template.includes('agreement')) {
                // 服务协议：一键生成模式
                await this.generateServiceAgreement();
            } else if (template.includes('oa_text')) {
                // OA正文：人机协作模式
                await this.openOAWorkspace();
            } else if (isWordTemplate) {
                // 其他Word模板：传统模式
                await window.documentGenerator.downloadWordDocument(this.currentCompany.id, template);
            } else if (isPdfTemplate) {
                // PDF模板：传统模式
                await window.documentGenerator.downloadPdfDocument(template);
            }

            // 自动勾选对应的复选框
            this.autoCheckRelatedItem(template);

        } catch (error) {
            if (window.uiManager) {
                window.uiManager.addLog(`文档处理失败: ${error.message}`, 'error');
                window.uiManager.showError(`文档处理失败: ${error.message}`);
            }
        } finally {
            button.disabled = false;
            this.updateButtonText(button, template);
        }
    }

    /**
     * 自动勾选相关项目
     */
    autoCheckRelatedItem(template) {
        let itemId = null;
        
        if (template.includes('agreement')) {
            itemId = 'serviceAgreement';
        } else if (template.includes('oa_text')) {
            itemId = 'oaDocument';
        } else if (template.includes('authorization')) {
            itemId = 'authorizationForm';
        } else if (template.includes('application')) {
            itemId = 'applicationForm';
        }

        if (itemId && this.elements.checkboxes[itemId]) {
            this.elements.checkboxes[itemId].checked = true;
            this.onCheckboxChange(itemId);
        }
    }

    /**
     * 更新按钮文本
     */
    updateButtonText(button, template) {
        if (template.endsWith('.docx')) {
            if (button.classList.contains('btn-gemini')) {
                button.textContent = '生成Word文档 + Gemini助手';
            } else {
                button.textContent = '生成Word文档';
            }
        } else if (template.endsWith('.pdf')) {
            button.textContent = '下载PDF表单';
        }
    }

    /**
     * 一键生成服务协议
     */
    async generateServiceAgreement() {
        try {
            if (window.uiManager) {
                window.uiManager.addLog('正在一键生成服务协议...', 'info');
            }

            const response = await fetch('http://localhost:5000/api/documents/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCompany.id,
                    document_type: 'agreement'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 下载文档
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `服务协议-${this.currentCompany.company_name}.docx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            if (window.uiManager) {
                window.uiManager.addLog('服务协议一键生成成功！', 'success');
                window.uiManager.showSuccess('服务协议已生成并下载！');
            }

        } catch (error) {
            throw new Error(`服务协议生成失败: ${error.message}`);
        }
    }

    /**
     * 打开OA工作台
     */
    async openOAWorkspace() {
        try {
            if (window.uiManager) {
                window.uiManager.addLog('正在启动OA正文人机协作工作台...', 'info');
            }

            // 检查OA工作台是否可用
            if (!window.oaWorkspace) {
                throw new Error('OA工作台未初始化');
            }

            // 打开工作台
            await window.oaWorkspace.openWorkspace(this.currentCompany);

            if (window.uiManager) {
                window.uiManager.addLog('OA工作台已启动，请按照指南与Gemini协作', 'success');
            }

        } catch (error) {
            throw new Error(`OA工作台启动失败: ${error.message}`);
        }
    }

    /**
     * 更新操作按钮状态
     */
    updateActionButtonsState() {
        const hasCompany = this.currentCompany !== null;
        
        this.elements.allActionButtons.forEach(button => {
            button.disabled = !hasCompany;
        });
    }

    /**
     * 更新进度显示
     */
    updateProgress() {
        const totalItems = this.workflowItems.length;
        let completedItems = 0;

        this.workflowItems.forEach(itemId => {
            const checkbox = this.elements.checkboxes[itemId];
            if (checkbox && checkbox.checked) {
                completedItems++;
            }
        });

        const progressPercentage = (completedItems / totalItems) * 100;

        // 更新进度文本
        this.elements.progressCount.textContent = `${completedItems}/${totalItems}`;
        
        // 更新进度条
        this.elements.progressFill.style.width = `${progressPercentage}%`;

        // 更新进度条颜色
        if (progressPercentage === 100) {
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #4CAF50, #8BC34A)';
        } else if (progressPercentage >= 50) {
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #FF9800, #FFC107)';
        } else {
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #2196F3, #03A9F4)';
        }

        return { completed: completedItems, total: totalItems, percentage: progressPercentage };
    }

    /**
     * 重置工作流程
     */
    resetWorkflow() {
        // 重置所有复选框（除了预先勾选的）
        this.workflowItems.forEach(itemId => {
            const checkbox = this.elements.checkboxes[itemId];
            if (checkbox) {
                // 保持预先勾选的项目
                if (itemId === 'authorizationForm' || itemId === 'applicationForm') {
                    checkbox.checked = true;
                } else {
                    checkbox.checked = false;
                }
            }
        });

        this.updateProgress();

        if (window.uiManager) {
            window.uiManager.addLog('工作流程已重置', 'info');
        }
    }

    /**
     * 导出进度报告
     */
    exportProgressReport() {
        if (!this.currentCompany) {
            if (window.uiManager) {
                window.uiManager.showError('请先选择一个客户企业');
            }
            return;
        }

        const progress = this.updateProgress();
        const currentDate = new Date().toLocaleDateString('zh-CN');
        
        const report = {
            company: this.currentCompany.company_name,
            creditCode: this.currentCompany.unified_social_credit_code,
            reportDate: currentDate,
            progress: progress,
            items: {}
        };

        // 收集各项状态
        this.workflowItems.forEach(itemId => {
            const checkbox = this.elements.checkboxes[itemId];
            report.items[itemId] = {
                name: this.getItemDisplayName(itemId),
                completed: checkbox ? checkbox.checked : false
            };
        });

        // 生成报告文本
        const reportText = this.generateReportText(report);
        
        // 下载报告
        const blob = new Blob([reportText], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `银企直联业务进度报告-${this.currentCompany.company_name}-${currentDate}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        if (window.uiManager) {
            window.uiManager.addLog('进度报告导出成功', 'success');
        }
    }

    /**
     * 生成报告文本
     */
    generateReportText(report) {
        return `
银企直联业务办理进度报告
========================================

企业信息:
  企业名称: ${report.company}
  信用代码: ${report.creditCode}
  报告日期: ${report.reportDate}

办理进度:
  完成进度: ${report.progress.completed}/${report.progress.total} (${report.progress.percentage.toFixed(1)}%)

详细清单:
  📄 文档生成阶段:
    ${report.items.serviceAgreement.completed ? '✓' : '○'} ${report.items.serviceAgreement.name}
    ${report.items.oaDocument.completed ? '✓' : '○'} ${report.items.oaDocument.name}
    ${report.items.authorizationForm.completed ? '✓' : '○'} ${report.items.authorizationForm.name}
    ${report.items.applicationForm.completed ? '✓' : '○'} ${report.items.applicationForm.name}

  🔄 业务处理阶段:
    ${report.items.documentsCollected.completed ? '✓' : '○'} ${report.items.documentsCollected.name}
    ${report.items.systemSubmitted.completed ? '✓' : '○'} ${report.items.systemSubmitted.name}

========================================
报告生成时间: ${new Date().toLocaleString('zh-CN')}
        `.trim();
    }

    /**
     * 获取项目显示名称
     */
    getItemDisplayName(itemId) {
        const names = {
            serviceAgreement: '生成《服务协议》',
            oaDocument: '生成《OA正文》',
            authorizationForm: '下载《对公客户授权及承诺书》',
            applicationForm: '下载《对公综合服务申请书》',
            documentsCollected: '已收集客户用印文件返回',
            systemSubmitted: '已提交后台系统审批'
        };
        return names[itemId] || itemId;
    }
}

// 创建全局工作流程管理器实例
const workflowManager = new WorkflowManager();

// 导出供其他模块使用
window.WorkflowManager = WorkflowManager;
window.workflowManager = workflowManager;
