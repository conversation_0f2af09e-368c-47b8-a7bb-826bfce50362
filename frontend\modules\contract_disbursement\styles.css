/**
 * 合同支用业务模块样式
 */

.contract-disbursement-module {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 模块标题 */
.module-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.module-header h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.module-subtitle {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

/* 文档生成区 */
.document-generator {
    margin-bottom: 30px;
}

.document-generator h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.document-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    text-align: center;
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.excel-doc {
    border-left: 4px solid #28a745;
}

.excel-doc:hover {
    border-color: #28a745;
    background: #f8fff9;
}

.word-doc {
    border-left: 4px solid #007bff;
}

.word-doc:hover {
    border-color: #007bff;
    background: #f8fbff;
}

.doc-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.document-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.document-card p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.generate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.generate-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.generate-btn:active {
    transform: translateY(0);
}

/* 生成结果区 */
.generation-result {
    margin-top: 20px;
}

.result-card {
    background: #f8f9fa;
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 25px;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.result-icon {
    font-size: 24px;
}

.result-header h4 {
    margin: 0;
    color: #28a745;
    font-size: 18px;
}

.result-content {
    margin-top: 15px;
}

.result-info {
    margin-bottom: 20px;
    color: #555;
    font-size: 16px;
}

.download-section {
    text-align: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

/* 加载状态 */
.generation-loading {
    margin-top: 20px;
}

.loading-card {
    background: #f8f9fa;
    border: 2px solid #6c757d;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #666;
    font-size: 16px;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .document-grid {
        grid-template-columns: 1fr;
    }
    
    .contract-disbursement-module {
        padding: 15px;
    }
    
    .module-header {
        padding: 15px;
    }
    
    .module-header h2 {
        font-size: 24px;
    }
}
