#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库初始化脚本
"""

import sqlite3
import uuid
from datetime import datetime

def init_sqlite_database():
    """初始化SQLite数据库"""
    
    db_path = "database/enterprise_service.db"
    
    print("🔄 初始化SQLite数据库...")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建companies表
        print("📋 创建companies表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS companies (
                id TEXT PRIMARY KEY,
                unified_social_credit_code TEXT UNIQUE NOT NULL,
                company_name TEXT NOT NULL,
                registered_address TEXT,
                communication_address TEXT,
                business_description TEXT,
                legal_representative TEXT,
                registration_date DATE,
                registered_capital REAL,
                business_scope TEXT,
                contact_phone TEXT,
                contact_email TEXT,
                website TEXT,
                industry_category TEXT,
                company_type TEXT,
                business_status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 2. 创建companies_history表
        print("📋 创建companies_history表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS companies_history (
                history_id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                version_number INTEGER NOT NULL,
                unified_social_credit_code TEXT,
                company_name TEXT,
                registered_address TEXT,
                communication_address TEXT,
                business_description TEXT,
                legal_representative TEXT,
                operation_type TEXT NOT NULL,
                operated_by_user_id TEXT NOT NULL,
                operation_reason TEXT,
                effective_from TIMESTAMP NOT NULL,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
        """)
        
        # 3. 创建persons表
        print("📋 创建persons表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS persons (
                id TEXT PRIMARY KEY,
                id_number TEXT UNIQUE NOT NULL,
                id_type TEXT NOT NULL,
                person_name TEXT NOT NULL,
                mobile_phone TEXT,
                other_contact TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 4. 创建company_person_relationships表
        print("📋 创建company_person_relationships表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS company_person_relationships (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                person_id TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                start_date DATE,
                end_date DATE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (person_id) REFERENCES persons(id)
            )
        """)
        
        # 5. 创建company_tags表
        print("📋 创建company_tags表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS company_tags (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                tag_name TEXT NOT NULL,
                tag_category TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
        """)
        
        print("✅ 表结构创建完成")
        
        # 插入初始数据
        print("\n📋 插入初始数据...")
        
        # 成都卫讯科技有限公司
        weixun_id = "34af7659-d69a-4c05-a697-6ae6eb00aad3"
        cursor.execute("""
            INSERT OR REPLACE INTO companies (
                id, company_name, unified_social_credit_code, 
                registered_address, legal_representative, business_description,
                contact_phone, contact_email, industry_category, company_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            weixun_id,
            "成都卫讯科技有限公司",
            "915101003320526751",
            "中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号",
            "万明刚",
            "通信设备制造、软件开发、技术服务",
            "028-85123456",
            "<EMAIL>",
            "制造业",
            "有限责任公司"
        ))
        
        # 成都中科卓尔智能科技集团有限公司
        zkzr_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
        cursor.execute("""
            INSERT OR REPLACE INTO companies (
                id, company_name, unified_social_credit_code, 
                registered_address, legal_representative, business_description,
                contact_phone, contact_email, industry_category, company_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            zkzr_id,
            "成都中科卓尔智能科技集团有限公司",
            "91510100MA6CGUGA1W",
            "中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼",
            "杨伟",
            "智能科技研发、软件开发、技术咨询服务",
            "028-86789012",
            "<EMAIL>",
            "科技服务业",
            "有限责任公司"
        ))
        
        # 神光光学集团有限公司
        shenguang_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
        cursor.execute("""
            INSERT OR REPLACE INTO companies (
                id, company_name, unified_social_credit_code, 
                registered_address, legal_representative, business_description,
                industry_category, company_type
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            shenguang_id,
            "神光光学集团有限公司",
            "91510100MA62TGHL5X",
            "四川省成都市成华区建设南街9号2层",
            "贾秉炜",
            "主营业务为高纯度合成熔石英的研发和生产，是可控核聚变、半导体光掩膜版、精密光学仪器等领域此前'卡脖子'的关键上游原材料。国家级专精特新小巨人企业。",
            "制造业",
            "有限责任公司"
        ))
        
        print("✅ 初始数据插入完成")
        
        # 提交事务
        conn.commit()
        
        # 验证初始化结果
        print("\n🔍 验证初始化结果...")
        
        cursor.execute("SELECT COUNT(*) FROM companies")
        company_count = cursor.fetchone()[0]
        print(f"✅ 公司记录数: {company_count}")
        
        cursor.execute("SELECT id, company_name, legal_representative FROM companies")
        companies = cursor.fetchall()
        print("📋 公司列表:")
        for company in companies:
            print(f"   - {company[1]} (法人: {company[2]}) [ID: {company[0]}]")
        
        conn.close()
        
        print("\n🎉 SQLite数据库初始化完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 企业服务SQLite数据库初始化")
    print("=" * 60)
    
    if init_sqlite_database():
        print("\n" + "=" * 60)
        print("🎉 数据库初始化完成!")
        print("🚀 现在可以使用企业服务系统了")
        print("✅ 神光光学集团有限公司信息已正确录入")
        print("   👤 法定代表人: 贾秉炜")
        print("   📍 注册地址: 四川省成都市成华区建设南街9号2层")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 数据库初始化失败!")
        print("=" * 60)
