# 文档处理功能使用指南

## 功能概述
企业信息核心库现已集成智能文档处理引擎，支持：
- **Word文档动态填充**: 根据企业数据自动填充模板占位符
- **PDF表单智能处理**: 自动勾选银企直联相关选项
- **批量文档生成**: 一键生成所有可用模板文档
- **格式完整保留**: 保持原始模板的所有格式和样式

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install python-docx PyPDF2 reportlab
```

### 2. 启动服务
```bash
python run_api.py
```

### 3. 访问前端
在浏览器中打开 `frontend/index.html`

## 📋 模板文件说明

### Word模板 (.docx)
- `yingqi_zhilian_agreement_blueprint.docx` - 银企直联服务协议模板
- `yingqi_zhilian_oa_text_blueprint.docx` - 银企直联OA文本模板

**支持的占位符**:
- `【公司全称】` / `【企业名称】` / `【公司名称】`
- `【统一社会信用代码】` / `【信用代码】`
- `【法定代表人】` / `【法人代表】` / `【法人姓名】`
- `【法人身份证号】` / `【法人证件号码】`
- `【企业资质】`
- `【申请日期】` / `【签署日期】` / `【当前日期】`
- `【企业ID】`

### PDF模板 (.pdf)
- `yingqi_zhilian_authorization_form_template.pdf` - 银企直联授权表单
- `yingqi_zhilian_application_form_template.pdf` - 银企直联申请表单

**处理功能**:
- 自动识别银企直联相关复选框
- 自动勾选相应选项
- 保持原始表单格式

## 🔧 API使用方法

### 获取模板列表
```bash
curl http://localhost:5000/api/templates
```

### 生成Word文档
```bash
curl -o "output.docx" \
  "http://localhost:5000/api/company/{company_id}/document/word/{template_name}"
```

### 生成PDF文档
```bash
curl -o "output.pdf" \
  "http://localhost:5000/api/document/pdf/{template_name}"
```

## 🎨 前端操作指南

### 1. 选择企业
- 在客户选择下拉框中选择目标企业
- 系统将自动加载企业详细信息

### 2. 查看可用模板
- 在"业务文档模板"区域查看可用模板列表
- Word模板显示为蓝色边框
- PDF模板显示为橙色边框

### 3. 生成单个文档
- 点击模板列表中的任意模板项
- 系统将自动生成并下载对应文档

### 4. 批量生成文档
- 点击"生成业务文档"按钮
- 系统将生成所有可用模板的文档
- 查看操作日志了解生成状态

## 🧪 测试功能

### 运行文档处理测试
```bash
python test_document_service.py
```

### 测试内容
- 模板列表获取测试
- Word文档生成测试
- PDF文档处理测试
- 文件下载和保存测试

### 测试输出
测试生成的文档将保存在 `test_output/` 目录中

## 🔍 故障排除

### 常见问题

#### 1. 依赖库未安装
**错误**: `python-docx库未安装，无法处理Word文档`
**解决**: `pip install python-docx`

#### 2. 模板文件不存在
**错误**: `模板文件不存在: template_name`
**解决**: 确认模板文件已放置在 `templates/yingqi_zhilian/` 目录中

#### 3. 公司数据获取失败
**错误**: `未找到公司信息: company_id`
**解决**: 确认公司ID正确且数据库中存在对应记录

#### 4. PDF处理失败
**错误**: `PyPDF2库未安装，无法处理PDF文档`
**解决**: `pip install PyPDF2`

### 日志查看
- 后端日志: 查看API服务控制台输出
- 前端日志: 查看浏览器开发者工具控制台
- 操作日志: 查看前端界面右侧操作日志面板

## 📈 性能优化

### 文档处理优化
- 大文档处理时间可能较长，请耐心等待
- 建议单次处理的文档数量不超过10个
- 复杂PDF表单处理可能需要更多时间

### 内存管理
- 文档处理完成后会自动释放内存
- 大量文档处理建议分批进行

## 🔮 扩展功能

### 添加新模板
1. 将模板文件放置在 `templates/yingqi_zhilian/` 目录
2. 确保Word模板包含正确的占位符
3. 确保PDF模板包含可识别的表单字段
4. 重启API服务或点击"刷新模板"按钮

### 自定义占位符
在 `api/document_service.py` 的 `prepare_replacement_data` 方法中添加新的占位符映射。

### 支持新文档格式
扩展 `DocumentService` 类以支持更多文档格式（如Excel、PowerPoint等）。

## 📞 技术支持

如遇到问题，请：
1. 查看API服务日志
2. 运行测试脚本验证功能
3. 检查模板文件格式和占位符
4. 确认数据库连接和数据完整性

---

**文档处理引擎** - 让业务文档生成变得智能和高效 📄✨
