#!/usr/bin/env python3
"""
最终精确替换 - 完全按照您截图的要求进行替换
"""

import docx
from pathlib import Path

def final_precise_replacement():
    # 源文件
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    target_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    
    print('=== 最终精确替换 ===\n')
    
    # 1. 提取您截图中的精确内容
    print('📖 提取您截图中的精确内容...')
    
    # 提取用信前提条件（额度申报书表格5，行2，列2）
    quota_doc = docx.Document(quota_file)
    precondition_text = quota_doc.tables[4].rows[1].cells[1].text.strip()
    print(f'✅ 用信前提条件: {precondition_text[:50]}...')
    
    # 提取持续条件完整表格（额度申报书表格6）
    continuous_table = quota_doc.tables[5]
    print(f'✅ 持续条件表格: {len(continuous_table.rows)}行')
    
    # 提取贷款条件（业务申报书表格7）
    business_doc = docx.Document(business_file)
    loan_table = business_doc.tables[6]
    loan_conditions = []
    for row in loan_table.rows:
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            if number and content:
                loan_conditions.append(f"{number}. {content}")
    print(f'✅ 贷款条件: {len(loan_conditions)}条')
    
    # 2. 创建新的落实情况表
    print('\n🔄 创建新的落实情况表...')
    create_new_implementation_table(precondition_text, continuous_table, loan_conditions)
    
    print('✅ 最终精确替换完成！')

def create_new_implementation_table(precondition_text, continuous_table, loan_conditions):
    """创建新的落实情况表"""
    
    # 创建新文档
    doc = docx.Document()
    
    # 添加标题
    title = doc.add_heading('条件落实情况表', 0)
    title.alignment = docx.enum.text.WD_ALIGN_PARAGRAPH.CENTER
    
    # 1. 用信前提条件部分
    doc.add_heading('一、用信前提条件及落实情况', level=1)
    
    # 创建用信前提条件表格
    precondition_table = doc.add_table(rows=2, cols=4)
    precondition_table.style = 'Table Grid'
    
    # 表头
    hdr_cells = precondition_table.rows[0].cells
    hdr_cells[0].text = '产品'
    hdr_cells[1].text = '本次设置的用信前提条件'
    hdr_cells[2].text = '前次单户综合融资总量方案设定条件'
    hdr_cells[3].text = '本次申报时点实际情况'
    
    # 内容行
    content_cells = precondition_table.rows[1].cells
    content_cells[0].text = '流动资金贷款\n（及可串用该额度的其他业务品种）'
    content_cells[1].text = precondition_text
    content_cells[2].text = '在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。'
    content_cells[3].text = '我行将在每次放款前审核落实'
    
    # 2. 持续条件部分
    doc.add_heading('二、单户综合融资总量用信持续条件', level=1)
    
    # 复制持续条件表格
    continuous_impl_table = doc.add_table(rows=len(continuous_table.rows), cols=4)
    continuous_impl_table.style = 'Table Grid'
    
    for row_idx, source_row in enumerate(continuous_table.rows):
        target_row = continuous_impl_table.rows[row_idx]
        for col_idx, source_cell in enumerate(source_row.cells):
            if col_idx < 4:  # 确保不超出列数
                target_row.cells[col_idx].text = source_cell.text.strip()
    
    # 3. 贷款条件部分
    doc.add_heading('三、单笔业务申报书中列明的贷款条件及落实情况', level=1)
    
    # 创建贷款条件表格
    loan_table = doc.add_table(rows=len(loan_conditions)+1, cols=2)
    loan_table.style = 'Table Grid'
    
    # 表头
    loan_hdr = loan_table.rows[0].cells
    loan_hdr[0].text = '序号'
    loan_hdr[1].text = '贷款条件内容'
    
    # 内容行
    for idx, condition in enumerate(loan_conditions):
        row = loan_table.rows[idx+1]
        parts = condition.split('.', 1)
        if len(parts) == 2:
            row.cells[0].text = parts[0].strip()
            row.cells[1].text = parts[1].strip()
        else:
            row.cells[0].text = str(idx+1)
            row.cells[1].text = condition
    
    # 保存文档到正确的test目录
    output_path = Path('test_output/落实情况表_最终版.docx')
    doc.save(output_path)
    print(f'💾 保存到: {output_path}')

    # 同时保存一个带时间戳的版本
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    timestamped_path = Path(f'test_output/落实情况表_测试版_{timestamp}.docx')
    doc.save(timestamped_path)
    print(f'💾 带时间戳版本: {timestamped_path}')

if __name__ == "__main__":
    final_precise_replacement()
