#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库扩展脚本
安全地添加新字段，避免重复字段错误
"""

import sqlite3
from pathlib import Path

def safe_add_column(cursor, table, column, column_type):
    """安全地添加列，如果已存在则跳过"""
    try:
        cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {column_type}")
        print(f"✅ 添加字段: {table}.{column}")
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print(f"⚠️ 字段已存在: {table}.{column}")
        else:
            print(f"❌ 添加字段失败: {table}.{column} - {e}")

def extend_database():
    """扩展数据库"""
    db_path = Path("database/enterprise_service.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 开始扩展数据库...")
        
        # 1. 创建新表
        print("\n📋 创建新表...")
        
        # 合同信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS contracts (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL REFERENCES companies(id),
                contract_number VARCHAR(100) NOT NULL,
                contract_type VARCHAR(50),
                contract_amount DECIMAL(15,2),
                contract_term INTEGER,
                contract_date DATE,
                maturity_date DATE,
                contract_status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 创建 contracts 表")
        
        # 放款信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS disbursements (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL REFERENCES companies(id),
                contract_id TEXT REFERENCES contracts(id),
                disbursement_amount DECIMAL(15,2),
                disbursement_date DATE,
                account_number VARCHAR(50),
                loan_purpose TEXT,
                disbursement_status VARCHAR(20) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 创建 disbursements 表")
        
        # 审批信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS approvals (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL REFERENCES companies(id),
                approval_number VARCHAR(100),
                approval_type VARCHAR(50),
                approved_amount DECIMAL(15,2),
                approval_date DATE,
                validity_start DATE,
                validity_end DATE,
                approval_status VARCHAR(20) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 创建 approvals 表")
        
        # 2. 安全地扩展companies表
        print("\n🔧 扩展 companies 表...")
        safe_add_column(cursor, "companies", "main_business", "TEXT")
        safe_add_column(cursor, "companies", "business_license_number", "VARCHAR(50)")
        safe_add_column(cursor, "companies", "primary_account", "VARCHAR(50)")
        safe_add_column(cursor, "companies", "account_manager", "VARCHAR(100)")
        safe_add_column(cursor, "companies", "account_manager_phone", "VARCHAR(20)")
        safe_add_column(cursor, "companies", "standard_loan_purpose", "TEXT")
        
        # 3. 更新现有公司数据
        print("\n📊 更新公司数据...")
        
        # 中科卓尔
        cursor.execute("""
            UPDATE companies 
            SET 
                primary_account = '51050148850800008651',
                standard_loan_purpose = '用于支付公司及其下属公司的员工工资、社保、缴纳税金、房租等日常经营周转，以及用于向控股（参股）公司提供公司经营的流动性支持，偿还其他银行流动资金贷款。',
                account_manager = '李睿杰',
                account_manager_phone = '***********'
            WHERE id = 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
        """)
        print("✅ 更新中科卓尔信息")
        
        # 神光光学
        cursor.execute("""
            UPDATE companies 
            SET primary_account = '51050111128900000943'
            WHERE id = '14371dda-2f8f-4d4c-82e6-c431dcf3b146'
        """)
        print("✅ 更新神光光学信息")
        
        # 卫讯科技
        cursor.execute("""
            UPDATE companies 
            SET primary_account = '5105014885080008705'
            WHERE id = '34af7659-d69a-4c05-a697-6ae6eb00aad3'
        """)
        print("✅ 更新卫讯科技信息")
        
        # 4. 插入示例合同数据
        print("\n📋 插入合同数据...")
        cursor.execute("""
            INSERT OR REPLACE INTO contracts (
                id, company_id, contract_number, contract_type, 
                contract_amount, contract_term, contract_date, 
                maturity_date, contract_status
            ) VALUES (
                'contract-zkzr-2025-001',
                'a1b2c3d4-e5f6-7890-1234-567890abcdef',
                '建八卓尔（2025）001号',
                '人民币流动资金贷款合同',
                2000.00,
                13,
                '2025-03-01',
                '2026-04-01',
                'active'
            )
        """)
        print("✅ 插入中科卓尔合同数据")
        
        # 5. 创建索引
        print("\n🔍 创建索引...")
        indexes = [
            ("idx_contracts_company", "contracts", "company_id"),
            ("idx_contracts_number", "contracts", "contract_number"),
            ("idx_disbursements_company", "disbursements", "company_id"),
            ("idx_disbursements_contract", "disbursements", "contract_id"),
            ("idx_approvals_company", "approvals", "company_id")
        ]
        
        for idx_name, table, column in indexes:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table}({column})")
                print(f"✅ 创建索引: {idx_name}")
            except Exception as e:
                print(f"⚠️ 索引创建失败: {idx_name} - {e}")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 数据库扩展完成！")
        
    except Exception as e:
        print(f"❌ 数据库扩展失败: {e}")
        if conn:
            conn.close()

if __name__ == "__main__":
    extend_database()
