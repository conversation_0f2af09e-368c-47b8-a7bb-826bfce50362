# 放款条件落实情况表 - 字段替换规范文档

## 📋 文档概述
基于对修改完成的 `2.docx` 文件的分析，提取所有标黄修改内容，生成结构化字段替换清单，用于自动化插入系统的开发。

## 🎯 分析范围
- **原始模板**: `disbursement_condition_checklist_blueprint.docx`
- **修改文件**: `2.docx` (包含所有标黄修改)
- **数据源文档**: `额度申报书.docx`、`业务申报书.docx`
- **分析方法**: 仅提取标黄部分内容，其他内容视为原样不动

## 📊 字段替换清单

### 🔍 总体统计
- **总字段数**: 6 个
- **数据库字段**: 1 个
- **系统生成字段**: 3 个  
- **文档提取字段**: 1 个
- **待确认字段**: 1 个

### 📝 详细字段清单

| 序号 | 字段名称 | 模板位置 | 来源文档 | 来源位置 | 类型 | 格式要求 | 替换方式 |
|------|---------|---------|---------|---------|------|---------|---------|
| 1 | 日期字段 | 表格0-行0-列1 | 系统生成 | 自动生成 | 系统字段 | 宋体12pt+注释 | 程序自动生成当前年月 |
| 2 | 公司名称 | 表格0-行1-列0 | 数据库 | companies.company_name | 数据库字段 | 宋体12pt | 直接文本替换 |
| 3 | 额度信息 | 表格0-行1-列1 | 未知 | 需要手动确认 | 未知 | 宋体12pt+黄标 | 需要确认来源 |
| 4 | 有效期信息 | 表格0-行3-列1 | 额度申报书.docx | 1.总量分项表 | 表格 | 保留表格格式 | 表格整体插入 |
| 5 | 担保措施 | 表格0-行5-列1 | 系统生成 | 自动生成 | 系统字段 | 宋体12pt+注释 | 从业务申报书提取 |
| 6 | 支用金额 | 表格0-行6-列0 | 系统生成 | 自动生成 | 系统字段 | 宋体12pt+注释 | 与业务申报书保持一致 |

## 🔧 详细字段规范

### 1. 【日期字段】
- **替换位置**: 表格0-行0-列1 (填报日期)
- **替换内容**: `2025 年 3 月 日 （每次生成到当月，日空着）`
- **实现方式**: 
  ```python
  current_date = datetime.now()
  date_text = f"{current_date.year}年{current_date.month}月   日"
  ```
- **格式要求**: 宋体12pt，保留"日空着"的格式
- **注意事项**: 每次生成时自动更新到当前年月，日期留空

### 2. 【公司名称】
- **替换位置**: 表格0-行1-列0 (借款人)
- **替换内容**: `成都中科卓尔智能科技集团有限公司（以下简称"中科卓尔"或"公司"）`
- **数据来源**: `companies.company_name`
- **实现方式**: 
  ```python
  company_name = get_company_name_from_db(company_id)
  full_text = f"{company_name}（以下简称"中科卓尔"或"公司"）"
  ```
- **格式要求**: 宋体12pt，黄色高亮
- **注意事项**: 需要添加简称说明

### 3. 【额度信息】
- **替换位置**: 表格0-行1-列1 (审批结论文号)
- **替换内容**: `（额度） （业务）`
- **数据来源**: ⚠️ 待确认
- **实现方式**: 需要进一步分析确定来源
- **格式要求**: 宋体12pt，黄色高亮
- **注意事项**: 可能需要从额度申报书或业务申报书中提取具体编号

### 4. 【有效期信息】
- **替换位置**: 表格0-行3-列1 (单户综合融资总量有效期)
- **替换内容**: `单 户综合 融资总量有效期： 2024-03-06 至 2025-03-06`
- **数据来源**: 额度申报书.docx - 1.总量分项表
- **实现方式**: 
  ```python
  # 从额度申报书的总量分项表中提取有效期信息
  start_date, end_date = extract_validity_period_from_quota_doc()
  ```
- **格式要求**: 保留表格格式，黄色高亮
- **注意事项**: 需要精确提取日期格式

### 5. 【担保措施】
- **替换位置**: 表格0-行5-列1 (担保方式)
- **替换内容**: `信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施`
- **数据来源**: 业务申报书.docx (根据注释"这句话在业务申报书内有，照着写")
- **实现方式**: 
  ```python
  # 从业务申报书中查找担保措施相关内容
  guarantee_info = extract_guarantee_measures_from_business_doc()
  ```
- **格式要求**: 宋体12pt，黄色高亮
- **注意事项**: 需要从业务申报书的担保措施部分提取

### 6. 【支用金额】
- **替换位置**: 表格0-行6-列0 (本次支用金额)
- **替换内容**: `本次支用金额： 万元 （和业务申报书内金额保持一致）`
- **数据来源**: 业务申报书.docx (根据注释"和业务申报书内金额保持一致")
- **实现方式**: 
  ```python
  # 从业务申报书中提取支用金额
  loan_amount = extract_loan_amount_from_business_doc()
  amount_text = f"本次支用金额：{loan_amount}万元"
  ```
- **格式要求**: 宋体12pt，黄色高亮
- **注意事项**: 金额必须与业务申报书保持一致

## 🎨 格式规范

### 字体格式要求
- **标准字体**: 宋体
- **标准字号**: 12pt
- **高亮标记**: 黄色背景 (WD_COLOR_INDEX.YELLOW)
- **行距**: 与原模板保持一致
- **段落间距**: 与原模板保持一致

### 表格格式要求
- **边框**: 保留原模板的表格边框
- **对齐方式**: 保持原有对齐设置
- **单元格合并**: 保持原有合并状态
- **列宽**: 保持原有列宽设置

## 🔄 实现建议

### 1. 按优先级实现
1. **高优先级**: 数据库字段 (公司名称)
2. **中优先级**: 系统生成字段 (日期、金额)
3. **低优先级**: 文档提取字段 (有效期、担保措施)

### 2. 实现步骤
1. **第一步**: 实现基础字段替换 (公司名称、日期)
2. **第二步**: 实现文档内容提取 (有效期、担保措施、支用金额)
3. **第三步**: 完善额度信息字段的来源确认
4. **第四步**: 整体测试和格式验证

### 3. 质量保证
- **格式验证**: 使用字体格式保持测试器验证
- **内容验证**: 对比原始申报书确保内容准确性
- **位置验证**: 确保所有字段插入到正确位置

## 📈 按来源分组的实现策略

### 数据库字段 (1个)
- **实现方式**: 直接SQL查询
- **技术难度**: 低
- **预计工时**: 0.5天

### 系统生成字段 (3个)
- **实现方式**: 程序逻辑生成
- **技术难度**: 中
- **预计工时**: 1天

### 文档提取字段 (1个)
- **实现方式**: 文档解析和内容匹配
- **技术难度**: 高
- **预计工时**: 2天

### 待确认字段 (1个)
- **实现方式**: 需要进一步分析
- **技术难度**: 待定
- **预计工时**: 0.5天

## 🎯 总结

本规范文档基于对 `2.docx` 中所有标黄内容的完整分析，提供了：

1. **完整的字段清单**: 6个字段的详细信息
2. **明确的数据来源**: 每个字段的具体来源和提取方法
3. **标准的格式要求**: 统一的字体和高亮标记规范
4. **可执行的实现建议**: 按优先级和技术难度的实现策略

此规范可直接用于自动化插入系统的开发工作，确保生成的文档与手动修改的 `2.docx` 完全一致。
