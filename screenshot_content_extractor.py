#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图内容提取器 - 根据用户提供的三张截图精准提取表格内容
用户截图分析：
1. 第一张图：前提条件表格 - 单户综合融资总量申报书中列明的用信前提条件及落实情况
2. 第二张图：业务申报书条件列表 - 编号1-6的具体条件
3. 第三张图：持续条件表格 - 单户综合融资总量申报书中列明的持续条件及落实情况
"""

class ScreenshotContentExtractor:
    """截图内容提取器"""
    
    def __init__(self):
        """初始化"""
        self.screenshot_data = self._load_screenshot_content()
    
    def _load_screenshot_content(self):
        """加载截图中的具体内容"""
        return {
            "前提条件表格": {
                "表格标题": "单户综合融资总量申报书中列明的用信前提条件及落实情况",
                "列标题": ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况'],
                "数据内容": [
                    [
                        "流动资金贷款（及可串用该额度的其他业务品种）",
                        "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
                        "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
                        "我行将在每次放款前审核落实"
                    ]
                ]
            },
            
            "业务申报书条件": {
                "条件列表": [
                    "1. 担保措施要求：本次申请的流动资金贷款须由公司实际控制人杨伟提供连带责任保证担保，并由公司以其持有的应收账款提供质押担保。",
                    "2. 贸易背景要求：本次申请的流动资金贷款须有真实的贸易背景，公司须提供相关合同、发票等证明材料。",
                    "3. 资金用途限制：本次申请的流动资金贷款仅可用于公司正常经营周转，不得用于固定资产投资、股权投资等。",
                    "4. 贷款期限：本次申请的流动资金贷款期限不超过12个月。",
                    "5. 还款来源：本次申请的流动资金贷款的还款来源为公司经营性现金流。",
                    "6. 其他条件：在贷款存续期内，公司须按照我行要求提供相关财务报表及经营情况报告。"
                ]
            },
            
            "持续条件表格": {
                "表格标题": "单户综合融资总量申报书中列明的持续条件及落实情况",
                "列标题": ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况'],
                "数据内容": [
                    ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
                    ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
                    ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
                    ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
                    ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "公司近期为引入A+轮融资对老股东进行了部分清退，系主动优化股权结构的战略安排。实际控制人杨伟依旧保持绝对控制权。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
                    ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "暂不考核", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
                    ["对长期投资的限制", "贷款存续期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "公司未对并表范围外公司投资。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
                    ["对发行优先权债务的限制", "贷款存续期内，不得优先发行优先权债务（将视影响程度削减甚至冻结未使用额度)", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式未强于我行。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
                    ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "客户已将基本户、代发薪账户转移至我行，并指定我行为A+轮融资唯一收款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
                    ["其他条件1", "/", "放款前落实", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
                    ["其他条件2", "/", "放款前落实", "A+轮融资到账前的临时性条款有效期最长不超过6个月（即至2026年1月31日）。"],
                    ["其他条件3", "单户综合融资总量有效期内，客户合并口径有息负债总金额不得超过7000万元", "经核查，截至2025年6月末，公司A+股权融资未到账，客户合并口径有息负债总额为5900万元，低于本条款约定的7500万元上限。", "分阶段调整：A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
                ]
            }
        }
    
    def get_precondition_table_data(self):
        """获取前提条件表格数据"""
        return self.screenshot_data["前提条件表格"]
    
    def get_business_conditions_data(self):
        """获取业务申报书条件数据"""
        return self.screenshot_data["业务申报书条件"]
    
    def get_continuous_conditions_data(self):
        """获取持续条件表格数据"""
        return self.screenshot_data["持续条件表格"]
    
    def extract_content_from_document(self, document_type, document_text):
        """从文档中提取对应的内容"""
        if document_type == "额度申报书":
            # 在额度申报书中查找持续条件相关内容
            return self._extract_continuous_conditions(document_text)
        elif document_type == "业务申报书":
            # 在业务申报书中查找编号条件列表
            return self._extract_business_conditions(document_text)
        else:
            return None
    
    def _extract_continuous_conditions(self, text):
        """从额度申报书中提取持续条件"""
        # 查找持续条件相关的关键字
        keywords = ["持续条件", "持续的评级", "资产负债率", "流动比率", "或有负债", "股权结构"]
        
        found_conditions = []
        for keyword in keywords:
            if keyword in text:
                # 提取相关段落
                import re
                pattern = f".*{keyword}.*"
                matches = re.findall(pattern, text, re.IGNORECASE)
                found_conditions.extend(matches)
        
        return found_conditions
    
    def _extract_business_conditions(self, text):
        """从业务申报书中提取编号条件"""
        # 查找编号条件模式 (1. 2. 3. 等)
        import re
        pattern = r'(\d+)\.\s*([^0-9]+?)(?=\d+\.|$)'
        matches = re.findall(pattern, text, re.DOTALL)
        
        conditions = []
        for match in matches:
            number, content = match
            content = content.strip()
            if content:
                conditions.append(f"{number}. {content}")
        
        return conditions
    
    def print_extraction_guide(self):
        """打印提取指南"""
        print("=" * 80)
        print("📋 截图内容提取指南")
        print("=" * 80)
        print("🔍 关键位置识别：")
        print("  📄 额度申报书 → 查找'持续条件'相关表格")
        print("  📄 业务申报书 → 查找编号列表的条件内容(1. 2. 3. ...)")
        print("  📄 落实情况表 → 需要将上述内容整合成真正的Word表格")
        print()
        print("📊 表格格式要求：")
        print("  ✅ 保持原有的4列结构（前提条件、持续条件）")
        print("  ✅ 保持原有的2列结构（业务条件）")
        print("  ✅ 保持表格边框和单元格格式")
        print("  ✅ 内容要完全匹配截图中的文字")
        print("=" * 80)

def main():
    """主函数"""
    extractor = ScreenshotContentExtractor()
    extractor.print_extraction_guide()
    
    print("\n📋 截图内容验证：")
    
    # 验证前提条件表格
    precondition_data = extractor.get_precondition_table_data()
    print(f"\n✅ 前提条件表格：")
    print(f"   标题：{precondition_data['表格标题']}")
    print(f"   列数：{len(precondition_data['列标题'])}")
    print(f"   数据行数：{len(precondition_data['数据内容'])}")
    
    # 验证业务条件
    business_data = extractor.get_business_conditions_data()
    print(f"\n✅ 业务申报书条件：")
    print(f"   条件数量：{len(business_data['条件列表'])}")
    for i, condition in enumerate(business_data['条件列表'][:3], 1):
        print(f"   {i}. {condition[:50]}...")
    
    # 验证持续条件表格
    continuous_data = extractor.get_continuous_conditions_data()
    print(f"\n✅ 持续条件表格：")
    print(f"   标题：{continuous_data['表格标题']}")
    print(f"   列数：{len(continuous_data['列标题'])}")
    print(f"   数据行数：{len(continuous_data['数据内容'])}")
    
    print(f"\n🎯 记忆要点：")
    print(f"   ✅ 已准确记录三张截图的具体内容")
    print(f"   ✅ 可以根据关键字精准提取对应内容")
    print(f"   ✅ 后续处理其他公司申报书时将使用相同模式")

if __name__ == "__main__":
    main()
