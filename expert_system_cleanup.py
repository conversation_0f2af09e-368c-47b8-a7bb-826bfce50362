#!/usr/bin/env python3
"""
专家级系统清理和修复
"""

import os
import shutil
from pathlib import Path
import json

def expert_system_cleanup():
    print('=== 专家级系统清理和修复 ===\n')
    
    # 1. 分析现有文件结构
    print('🔍 1. 分析现有文件结构...')
    analyze_current_structure()
    
    # 2. 清理冗余文件
    print('\n🧹 2. 清理冗余文件...')
    cleanup_redundant_files()
    
    # 3. 确定主界面
    print('\n🎯 3. 确定主界面...')
    identify_main_interface()
    
    # 4. 检查API连接问题
    print('\n🔧 4. 检查API连接问题...')
    diagnose_api_issues()
    
    # 5. 生成清理报告
    print('\n📋 5. 生成清理报告...')
    generate_cleanup_report()

def analyze_current_structure():
    """分析当前文件结构"""
    frontend_dir = Path('frontend')
    
    print('  📁 前端文件分析:')
    html_files = list(frontend_dir.glob('*.html'))
    for html_file in html_files:
        size = html_file.stat().st_size
        print(f'    {html_file.name} - {size} bytes')
    
    print('\n  📁 JavaScript文件分析:')
    js_dir = frontend_dir / 'js'
    if js_dir.exists():
        js_files = list(js_dir.glob('*.js'))
        for js_file in js_files:
            size = js_file.stat().st_size
            print(f'    {js_file.name} - {size} bytes')

def cleanup_redundant_files():
    """清理冗余文件"""
    redundant_files = [
        'frontend/cockpit_test.html',
        'frontend/index_cockpit.html',  # 保留unified版本
        'simple_api.py',
        'start_api_server.py',
        'expert_api_diagnosis.py',
        'expert_system_cleanup.py'  # 运行后删除自己
    ]
    
    for file_path in redundant_files:
        if Path(file_path).exists():
            print(f'  🗑️ 删除冗余文件: {file_path}')
            # Path(file_path).unlink()  # 暂时注释，先分析
        else:
            print(f'  ✅ 文件不存在: {file_path}')

def identify_main_interface():
    """确定主界面"""
    main_interface = 'frontend/index_unified_cockpit.html'
    
    if Path(main_interface).exists():
        print(f'  ✅ 主界面确定: {main_interface}')
        
        # 检查主界面的流程
        with open(main_interface, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if '选择您要服务的客户' in content:
            print('    ✅ 包含客户选择流程')
        if '业务模块' in content or 'module' in content:
            print('    ✅ 包含模块选择流程')
            
    else:
        print(f'  ❌ 主界面不存在: {main_interface}')

def diagnose_api_issues():
    """诊断API连接问题"""
    api_file = Path('api/app.py')
    
    if api_file.exists():
        print('  📄 检查API文件...')
        
        with open(api_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键路由
        routes_to_check = [
            '@app.route(\'/api/companies\'',
            '@app.route(\'/api/yingqi-zhilian',
            '@app.route(\'/api/deposit-services',
            '@app.route(\'/api/contract-disbursement',
            'CORS(app)'
        ]
        
        for route in routes_to_check:
            if route in content:
                print(f'    ✅ 找到路由: {route}')
            else:
                print(f'    ❌ 缺少路由: {route}')
    else:
        print('  ❌ API文件不存在')

def generate_cleanup_report():
    """生成清理报告"""
    report = {
        'timestamp': '2025-08-01',
        'main_interface': 'frontend/index_unified_cockpit.html',
        'api_file': 'api/app.py',
        'database': 'database/enterprise_service.db',
        'recommended_startup': 'python start_system.py',
        'issues_found': [],
        'cleanup_actions': []
    }
    
    # 检查关键文件
    critical_files = [
        'frontend/index_unified_cockpit.html',
        'frontend/js/unified-cockpit-manager.js',
        'api/app.py',
        'database/enterprise_service.db'
    ]
    
    for file_path in critical_files:
        if not Path(file_path).exists():
            report['issues_found'].append(f'关键文件缺失: {file_path}')
    
    print('  📋 清理报告:')
    print(f'    主界面: {report["main_interface"]}')
    print(f'    API文件: {report["api_file"]}')
    print(f'    数据库: {report["database"]}')
    
    if report['issues_found']:
        print('    ⚠️ 发现问题:')
        for issue in report['issues_found']:
            print(f'      - {issue}')

if __name__ == "__main__":
    expert_system_cleanup()
