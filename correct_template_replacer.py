#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的模板替换器 - 在原始落实情况表模板中插入真正的表格
用户要求：保持原始模板结构，在指定位置插入真正的Word表格
"""

import os
import shutil
from docx import Document
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
from datetime import datetime
from pathlib import Path

class CorrectTemplateReplacer:
    """正确的模板替换器"""
    
    def __init__(self):
        self.green_rgb = RGBColor(0, 128, 0)  # 绿色标记
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "落实情况表.docx"
        self.output_dir = self.project_root / "test_output"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
    def replace_with_real_tables(self):
        """在原始模板中插入真正的表格"""
        try:
            # 检查原始模板是否存在
            if not self.template_path.exists():
                print(f"❌ 原始模板不存在: {self.template_path}")
                return None
            
            # 创建输出文件路径
            output_file = self.output_dir / f"落实情况表_正确版本_卓尔_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            
            # 复制原始模板
            shutil.copy2(self.template_path, output_file)
            print(f"✅ 复制原始模板: {self.template_path}")
            print(f"✅ 输出文件: {output_file}")
            
            # 打开文档
            doc = Document(output_file)
            print("✅ 打开文档进行编辑")
            
            # 查找并在指定位置插入表格
            self.insert_tables_at_correct_positions(doc)
            
            # 保存文档
            doc.save(output_file)
            print(f"✅ 成功保存文档: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 替换过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def insert_tables_at_correct_positions(self, doc):
        """在正确位置插入表格"""
        print("🔍 查找文档中的插入位置...")

        # 遍历所有段落，查找标题并在其后插入表格
        paragraphs_to_process = []
        found_titles = []

        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()

            # 查找三个关键标题
            if "一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况" in text:
                print(f"✅ 找到前提条件标题: 段落 {i}")
                paragraphs_to_process.append((i, 'precondition'))
                found_titles.append('precondition')
            elif "二、单户综合融资总量方案申报书中列明的持续条件及落实情况" in text:
                print(f"✅ 找到持续条件标题: 段落 {i}")
                paragraphs_to_process.append((i, 'continuous'))
                found_titles.append('continuous')
            elif "三、单笔业务申报书中列明的贷款条件及落实情况" in text:
                print(f"✅ 找到业务申报书条件标题: 段落 {i}")
                paragraphs_to_process.append((i, 'business'))
                found_titles.append('business')

        # 如果没有找到标题，在文档末尾添加完整的三个部分
        if not found_titles:
            print("⚠️ 未找到预期的标题，在文档末尾添加完整的三个部分...")
            self.add_complete_sections_to_end(doc)
        else:
            # 从后往前处理，避免索引变化问题
            paragraphs_to_process.reverse()

            for paragraph_index, table_type in paragraphs_to_process:
                if table_type == 'precondition':
                    self.insert_precondition_table_after_paragraph(doc, paragraph_index)
                elif table_type == 'continuous':
                    self.insert_continuous_condition_table_after_paragraph(doc, paragraph_index)
                elif table_type == 'business':
                    self.insert_business_condition_table_after_paragraph(doc, paragraph_index)

    def add_complete_sections_to_end(self, doc):
        """在文档末尾添加完整的三个部分"""
        print("📝 在文档末尾添加完整的三个部分...")

        # 添加空行
        doc.add_paragraph("")

        # 1. 添加前提条件部分
        print("✅ 添加前提条件部分...")
        heading1 = doc.add_heading('一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况', level=2)
        self.add_precondition_table_to_doc(doc)
        doc.add_paragraph("")  # 空行

        # 2. 添加持续条件部分
        print("✅ 添加持续条件部分...")
        heading2 = doc.add_heading('二、单户综合融资总量方案申报书中列明的持续条件及落实情况', level=2)
        self.add_continuous_condition_table_to_doc(doc)
        doc.add_paragraph("")  # 空行

        # 3. 添加业务申报书条件部分
        print("✅ 添加业务申报书条件部分...")
        heading3 = doc.add_heading('三、单笔业务申报书中列明的贷款条件及落实情况', level=2)
        self.add_business_condition_table_to_doc(doc)

        print("✅ 完整的三个部分添加完成")
    
    def insert_precondition_table_after_paragraph(self, doc, paragraph_index):
        """在指定段落后插入前提条件表格"""
        print("📝 插入前提条件表格...")
        
        # 在指定段落后添加一个空段落，然后添加表格
        target_paragraph = doc.paragraphs[paragraph_index]
        
        # 添加空段落
        new_paragraph = doc.add_paragraph()
        target_paragraph._element.addnext(new_paragraph._element)
        
        # 创建表格
        table = doc.add_table(rows=2, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # 将表格插入到新段落后
        new_paragraph._element.addnext(table._element)
        
        # 设置表头
        headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 填充数据行
        data_row = table.rows[1]
        data_content = [
            "流动资金贷款（及可串用该额度的其他业务品种）",
            "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
            "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
            "我行将在每次放款前审核落实"
        ]
        
        for i, content in enumerate(data_content):
            data_row.cells[i].text = content
            # 设置为绿色
            for para in data_row.cells[i].paragraphs:
                for run in para.runs:
                    run.font.color.rgb = self.green_rgb
        
        print("✅ 前提条件表格插入完成")
    
    def insert_continuous_condition_table_after_paragraph(self, doc, paragraph_index):
        """在指定段落后插入持续条件表格"""
        print("📝 插入持续条件表格...")
        
        # 在指定段落后添加一个空段落，然后添加表格
        target_paragraph = doc.paragraphs[paragraph_index]
        
        # 添加空段落
        new_paragraph = doc.add_paragraph()
        target_paragraph._element.addnext(new_paragraph._element)
        
        # 创建表格
        table = doc.add_table(rows=13, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # 将表格插入到新段落后
        new_paragraph._element.addnext(table._element)
        
        # 设置表头
        headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 填充持续条件数据
        conditions_data = [
            ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
            ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
            ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
            ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
            ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "根据公司章程及股东会决议，公司股权结构稳定。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
            ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "根据公司财务报表，公司营收持续增长。", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
            ["对长期投资的限制", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "根据公司投资决策制度，重大投资需董事会审议。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
            ["对发行优先权债务的限制", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。", "根据公司融资政策，将优先保障我行债权。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
            ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "公司已在我行开立基本存款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
            ["其他条件1", "本次新增贷款的最终支用日不晚于2025年9月30日。", "根据公司资金使用计划，将在有效期内完成支用。", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
            ["其他条件2", "A+轮融资到账前的临时性条款有效期最长不超过6个月。", "公司A+轮融资正在推进中，预计6个月内完成。", "A+轮融资到账前的临时性条款有效期最长不超过6个月。"],
            ["其他条件3", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。", "根据公司财务规划，将严格控制有息负债规模。", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
        ]
        
        for i, condition in enumerate(conditions_data):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb
        
        print("✅ 持续条件表格插入完成")
    
    def insert_business_condition_table_after_paragraph(self, doc, paragraph_index):
        """在指定段落后插入业务申报书条件表格"""
        print("📝 插入业务申报书条件表格...")
        
        # 在指定段落后添加一个空段落，然后添加表格
        target_paragraph = doc.paragraphs[paragraph_index]
        
        # 添加空段落
        new_paragraph = doc.add_paragraph()
        target_paragraph._element.addnext(new_paragraph._element)
        
        # 创建表格
        table = doc.add_table(rows=6, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # 将表格插入到新段落后
        new_paragraph._element.addnext(table._element)
        
        # 设置表头
        headers = ['序号', '贷款条件内容']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 填充业务申报书条件数据
        business_conditions = [
            ["1", "借款人实际控制人提供连带责任保证担保"],
            ["2", "借款人以其拥有的部分专利产权提供质押担保"],
            ["3", "借款人承诺将A+轮股权融资资金指定我行为唯一收款账户"],
            ["4", "借款人承诺贷转存账户设置网银受控"],
            ["5", "其他约定条件按照合同执行"]
        ]
        
        for i, condition in enumerate(business_conditions):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb
        
        print("✅ 业务申报书条件表格插入完成")

    def add_precondition_table_to_doc(self, doc):
        """直接添加前提条件表格到文档"""
        # 创建表格
        table = doc.add_table(rows=2, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充数据行
        data_row = table.rows[1]
        data_content = [
            "流动资金贷款（及可串用该额度的其他业务品种）",
            "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
            "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
            "我行将在每次放款前审核落实"
        ]

        for i, content in enumerate(data_content):
            data_row.cells[i].text = content
            # 设置为绿色
            for para in data_row.cells[i].paragraphs:
                for run in para.runs:
                    run.font.color.rgb = self.green_rgb

    def add_continuous_condition_table_to_doc(self, doc):
        """直接添加持续条件表格到文档"""
        # 创建表格
        table = doc.add_table(rows=13, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充持续条件数据
        conditions_data = [
            ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
            ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
            ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
            ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
            ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "根据公司章程及股东会决议，公司股权结构稳定。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
            ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "根据公司财务报表，公司营收持续增长。", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
            ["对长期投资的限制", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "根据公司投资决策制度，重大投资需董事会审议。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
            ["对发行优先权债务的限制", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。", "根据公司融资政策，将优先保障我行债权。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
            ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "公司已在我行开立基本存款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
            ["其他条件1", "本次新增贷款的最终支用日不晚于2025年9月30日。", "根据公司资金使用计划，将在有效期内完成支用。", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
            ["其他条件2", "A+轮融资到账前的临时性条款有效期最长不超过6个月。", "公司A+轮融资正在推进中，预计6个月内完成。", "A+轮融资到账前的临时性条款有效期最长不超过6个月。"],
            ["其他条件3", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。", "根据公司财务规划，将严格控制有息负债规模。", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
        ]

        for i, condition in enumerate(conditions_data):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb

    def add_business_condition_table_to_doc(self, doc):
        """直接添加业务申报书条件表格到文档"""
        # 创建表格
        table = doc.add_table(rows=6, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['序号', '贷款条件内容']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充业务申报书条件数据
        business_conditions = [
            ["1", "借款人实际控制人提供连带责任保证担保"],
            ["2", "借款人以其拥有的部分专利产权提供质押担保"],
            ["3", "借款人承诺将A+轮股权融资资金指定我行为唯一收款账户"],
            ["4", "借款人承诺贷转存账户设置网银受控"],
            ["5", "其他约定条件按照合同执行"]
        ]

        for i, condition in enumerate(business_conditions):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb

def main():
    """主函数"""
    replacer = CorrectTemplateReplacer()
    
    print("=" * 80)
    print("📋 正确的模板替换器")
    print("=" * 80)
    print("🎯 用户要求:")
    print("  ✅ 保持原始落实情况表模板的完整结构")
    print("  ✅ 在指定位置插入真正的Word表格")
    print("  ✅ 不破坏原有格式")
    print("=" * 80)
    
    result = replacer.replace_with_real_tables()
    
    if result:
        print(f"\n🎉 落实情况表替换完成！")
        print(f"📄 文件位置: {result}")
        print(f"📝 完成内容:")
        print(f"  ✅ 保持了原始模板的所有格式")
        print(f"  ✅ 插入了前提条件表格 (2行4列)")
        print(f"  ✅ 插入了持续条件表格 (13行4列)")
        print(f"  ✅ 插入了业务申报书条件表格 (6行2列)")
        print(f"  ✅ 所有数据都标记为绿色")
    else:
        print(f"\n❌ 替换失败")

if __name__ == "__main__":
    main()
