#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协定存款协议生成器
根据企业数据自动填充协定存款协议模板
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import Pt
import sqlite3
from pathlib import Path
import logging
from datetime import datetime
import shutil
import random

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AgreedDepositGenerator:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.template_path = self.project_root / "templates" / "deposit_services" / "中国建设银行人民币单位协定存款合同（标准文本）1.docx"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        
        # 颜色定义
        self.yellow_color = WD_COLOR_INDEX.YELLOW  # 黄色 - 替换内容标记
        
    def generate_deposit_agreement(self, company_id, deposit_amount=1000):
        """生成协定存款协议"""
        logger.info(f"🏦 开始生成协定存款协议，企业ID: {company_id}")
        
        try:
            # 1. 获取企业数据
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到企业数据: {company_id}")
            
            # 2. 准备输出目录
            self.output_dir.mkdir(exist_ok=True)
            
            # 3. 复制模板文件
            output_path = self.output_dir / f"协定存款协议_{company_data['company_name']}.docx"
            shutil.copy2(self.template_path, output_path)
            
            # 4. 加载文档
            doc = docx.Document(output_path)
            
            # 5. 准备填充数据
            fill_data = self._prepare_fill_data(company_data, deposit_amount)
            
            # 6. 执行字段替换
            replacement_count = self._execute_field_replacements(doc, fill_data)
            
            # 7. 保存文件
            doc.save(output_path)
            
            logger.info(f"✅ 协定存款协议生成完成: {output_path}")
            return output_path, self._generate_summary(company_data, deposit_amount, replacement_count)
            
        except Exception as e:
            logger.error(f"❌ 生成失败: {e}")
            raise
    
    def _get_company_data(self, company_id):
        """从数据库获取企业数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    company_name, unified_social_credit_code, legal_representative,
                    registration_date, registered_capital, business_scope, business_description,
                    contact_phone, finance_manager_name, finance_manager_phone,
                    total_assets, total_liabilities, spouse_name, environmental_classification
                FROM companies 
                WHERE id = ?
            """, (company_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'company_name': result[0],
                    'unified_social_credit_code': result[1],
                    'legal_representative': result[2],
                    'registration_date': result[3],
                    'registered_capital': result[4],
                    'business_scope': result[5],
                    'business_description': result[6],
                    'contact_phone': result[7],
                    'finance_manager_name': result[8],
                    'finance_manager_phone': result[9],
                    'total_assets': result[10],
                    'total_liabilities': result[11],
                    'spouse_name': result[12],
                    'environmental_classification': result[13]
                }
            return None
            
        except Exception as e:
            logger.error(f"获取企业数据失败: {e}")
            return None
    
    def _prepare_fill_data(self, company_data, deposit_amount):
        """准备填充数据 - 按用户要求只填充必要信息"""
        # 从数据库获取账户信息
        account_info = self._get_account_info(company_data['company_name'])

        # 生成标准合同编号
        agreement_number = self._generate_agreement_number(company_data['company_name'])

        fill_data = {
            # 企业信息 - 只填充企业名称
            'company_name': company_data['company_name'],

            # 合同编号 - 标准格式
            'agreement_number': agreement_number,

            # 账户信息 - 使用统一账户号（活期和协定存款都用同一个）
            'company_account': account_info.get('company_account', '51050148850800008651'),
        }

        logger.info(f"📋 准备填充数据（按用户要求留空其他字段）:")
        for key, value in fill_data.items():
            logger.info(f"   {key}: {value}")

        return fill_data

    def _generate_agreement_number(self, company_name):
        """生成标准协议编号：建八[公司简称]协定2025001号"""
        year = datetime.now().year

        # 根据公司名称生成简称
        if '中科卓尔' in company_name:
            company_short = '卓尔'
        elif '神光' in company_name:
            company_short = '神光'
        elif '至臻' in company_name:
            company_short = '至臻'
        elif '卫讯' in company_name:
            company_short = '卫讯'
        else:
            # 默认取公司名称中的关键字
            company_short = '企业'

        return f"建八{company_short}协定{year}001号"

    def _get_account_info(self, company_name):
        """从数据库获取公司账号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询公司账号
            cursor.execute("""
                SELECT company_account
                FROM companies
                WHERE company_name = ?
            """, (company_name,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                company_account = result[0]
                return {
                    'company_account': company_account
                }
            else:
                # 如果没有公司账号，使用用户指定的统一账户号
                return {
                    'company_account': "51050148850800008651"
                }

        except Exception as e:
            logger.warning(f"获取公司账号失败: {e}，使用统一账户号")
            return {
                'company_account': "51050148850800008651"
            }
    
    def _execute_field_replacements(self, doc, fill_data):
        """执行字段替换 - 按用户要求只填充必要信息"""
        logger.info("🔄 开始执行字段替换（只填充企业名称和账户号）...")

        replacement_count = 0

        # 特殊处理：合同编号替换（在编号行）
        # 必须在通用企业名称替换之前处理
        for paragraph in doc.paragraphs:
            if '编号：' in paragraph.text:
                # 检查是否包含企业名称（可能是"待补充"被替换后的结果）
                para_text = paragraph.text
                if fill_data['company_name'] in para_text or '待补充' in para_text:
                    # 使用标准的字体格式保持替换方法
                    for run in paragraph.runs:
                        if fill_data['company_name'] in run.text:
                            # 保存原始字体格式
                            original_format = {
                                'font_name': run.font.name,
                                'font_size': run.font.size,
                                'bold': run.font.bold,
                                'italic': run.font.italic,
                                'underline': run.font.underline
                            }

                            # 替换文本
                            run.text = run.text.replace(fill_data['company_name'], fill_data['agreement_number'])
                            run.font.highlight_color = self.yellow_color

                            # 强制使用系统标准字体避免渲染问题
                            try:
                                run.font.name = '宋体'  # 使用系统标准字体
                                run.font.size = Pt(14)  # 明确设置14pt
                                if original_format['bold'] is not None:
                                    run.font.bold = original_format['bold']
                                if original_format['italic'] is not None:
                                    run.font.italic = original_format['italic']
                                if original_format['underline'] is not None:
                                    run.font.underline = original_format['underline']
                            except Exception as e:
                                logger.warning(f"合同编号字体格式设置失败: {e}")

                            replacement_count += 1
                            logger.info(f"   ✅ 合同编号: 替换1处")
                            break
                        elif '待补充' in run.text:
                            # 保存原始字体格式
                            original_format = {
                                'font_name': run.font.name,
                                'font_size': run.font.size,
                                'bold': run.font.bold,
                                'italic': run.font.italic,
                                'underline': run.font.underline
                            }

                            # 替换文本
                            run.text = run.text.replace('待补充', fill_data['agreement_number'])
                            run.font.highlight_color = self.yellow_color

                            # 强制使用系统标准字体避免渲染问题
                            try:
                                run.font.name = '宋体'  # 使用系统标准字体
                                run.font.size = Pt(14)  # 明确设置14pt
                                if original_format['bold'] is not None:
                                    run.font.bold = original_format['bold']
                                if original_format['italic'] is not None:
                                    run.font.italic = original_format['italic']
                                if original_format['underline'] is not None:
                                    run.font.underline = original_format['underline']
                            except Exception as e:
                                logger.warning(f"合同编号字体格式设置失败: {e}")

                            replacement_count += 1
                            logger.info(f"   ✅ 合同编号: 替换1处")
                            break
                    break  # 只处理第一个编号行

        # 定义替换映射 - 按用户要求只替换企业名称、账户号、利率调整
        # 重要：先替换具体的占位符，再替换通用的"待补充"
        # 基本存款额度、合同日期、通知期限都留空
        replacements = [
            {
                'old': '在乙方开立协定存款账户的账号：待补充',
                'new': f"在乙方开立协定存款账户的账号：{fill_data['company_account']}",
                'description': '协定存款账户号'
            },
            {
                'old': '活期存款账户（账号：待补充_）',
                'new': f"活期存款账户（账号：{fill_data['company_account']}）",
                'description': '活期存款账户号'
            },
            {
                'old': '25 bps',
                'new': '45 bps',
                'description': '利率调整幅度'
            },
            {
                'old': '待补充',
                'new': fill_data['company_name'],
                'description': '甲方企业名称'
            }
        ]
        
        # 执行替换
        for replacement in replacements:
            count = self._replace_text_with_highlight(doc, replacement['old'], replacement['new'])
            if count > 0:
                replacement_count += count
                logger.info(f"   ✅ {replacement['description']}: 替换{count}处")
            else:
                # 如果标准替换失败，尝试强制替换
                count = self._force_replace_text(doc, replacement['old'], replacement['new'])
                if count > 0:
                    replacement_count += count
                    logger.info(f"   ✅ {replacement['description']}: 强制替换{count}处")
                else:
                    logger.warning(f"   ⚠️ {replacement['description']}: 未找到匹配文本")
        
        # 不处理日期替换（按用户要求，日期全部不准写）
        logger.info(f"   ⚠️ 按用户要求，所有日期字段保持空白")
        
        logger.info(f"📊 字段替换完成: 总计{replacement_count}个替换")
        return replacement_count
    
    def _replace_text_with_highlight(self, doc, old_text, new_text):
        """替换文本并标记黄色"""
        count = 0
        
        # 在段落中替换
        for paragraph in doc.paragraphs:
            if old_text in paragraph.text:
                count += self._replace_in_paragraph_with_highlight(paragraph, old_text, new_text)
        
        # 在表格中替换
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if old_text in paragraph.text:
                            count += self._replace_in_paragraph_with_highlight(paragraph, old_text, new_text)
        
        return count
    
    def _replace_in_paragraph_with_highlight(self, paragraph, old_text, new_text):
        """在段落中替换文本并标记黄色（完全保持格式）"""
        if old_text not in paragraph.text:
            return 0

        # 方法1：在现有的runs中查找并替换，使用标准字体
        for run in paragraph.runs:
            if old_text in run.text:
                # 保存原有字体格式
                original_format = {
                    'bold': run.font.bold,
                    'italic': run.font.italic,
                    'underline': run.font.underline
                }

                # 替换文本并标记颜色
                run.text = run.text.replace(old_text, new_text)
                run.font.highlight_color = self.yellow_color

                # 强制使用系统标准字体避免渲染问题
                try:
                    run.font.name = '宋体'  # 使用系统标准字体
                    run.font.size = Pt(14)  # 明确设置14pt
                    if original_format['bold'] is not None:
                        run.font.bold = original_format['bold']
                    if original_format['italic'] is not None:
                        run.font.italic = original_format['italic']
                    if original_format['underline'] is not None:
                        run.font.underline = original_format['underline']
                except Exception as e:
                    logger.warning(f"字体格式设置失败: {e}")

                return 1

        # 方法2：如果文本跨越多个runs，使用更精确的方法
        paragraph_text = paragraph.text
        if old_text in paragraph_text:
            # 找到old_text在段落中的位置
            start_pos = paragraph_text.find(old_text)
            end_pos = start_pos + len(old_text)

            # 找到包含这个位置的runs
            current_pos = 0
            target_runs = []

            for run in paragraph.runs:
                run_start = current_pos
                run_end = current_pos + len(run.text)

                # 如果这个run与目标文本有重叠
                if run_start < end_pos and run_end > start_pos:
                    target_runs.append({
                        'run': run,
                        'start': run_start,
                        'end': run_end,
                        'text': run.text
                    })

                current_pos = run_end

            # 如果只涉及一个run，直接替换
            if len(target_runs) == 1:
                target_run = target_runs[0]['run']
                relative_start = start_pos - target_runs[0]['start']
                relative_end = end_pos - target_runs[0]['start']

                # 分割文本
                before_text = target_run.text[:relative_start]
                after_text = target_run.text[relative_end:]

                # 保存原有格式（完整保存）
                original_format = {
                    'font_name': target_run.font.name,
                    'font_size': target_run.font.size,
                    'bold': target_run.font.bold,
                    'italic': target_run.font.italic,
                    'underline': target_run.font.underline
                }

                # 重新设置run的文本
                target_run.text = before_text + new_text + after_text
                target_run.font.highlight_color = self.yellow_color

                # 强制使用系统标准字体避免渲染问题
                try:
                    target_run.font.name = '宋体'  # 使用系统标准字体
                    target_run.font.size = Pt(14)  # 明确设置14pt
                    if original_format['bold'] is not None:
                        target_run.font.bold = original_format['bold']
                    if original_format['italic'] is not None:
                        target_run.font.italic = original_format['italic']
                    if original_format['underline'] is not None:
                        target_run.font.underline = original_format['underline']
                except Exception as e:
                    logger.warning(f"字体格式设置失败: {e}")

                return 1

            # 如果涉及多个runs，使用最保守的方法：只替换第一个匹配的run
            for run in paragraph.runs:
                if old_text in run.text:
                    # 保存原有字体格式
                    original_format = {
                        'bold': run.font.bold,
                        'italic': run.font.italic,
                        'underline': run.font.underline
                    }

                    run.text = run.text.replace(old_text, new_text)
                    run.font.highlight_color = self.yellow_color

                    # 强制使用系统标准字体避免渲染问题
                    try:
                        run.font.name = '宋体'  # 使用系统标准字体
                        run.font.size = Pt(14)  # 明确设置14pt
                        if original_format['bold'] is not None:
                            run.font.bold = original_format['bold']
                        if original_format['italic'] is not None:
                            run.font.italic = original_format['italic']
                        if original_format['underline'] is not None:
                            run.font.underline = original_format['underline']
                    except Exception as e:
                        logger.warning(f"字体格式设置失败: {e}")

                    return 1

        return 0

    def _force_replace_text(self, doc, old_text, new_text):
        """强制替换文本（用于处理复杂的占位符）"""
        count = 0

        # 在所有段落中查找并替换
        for paragraph in doc.paragraphs:
            full_text = paragraph.text
            if old_text in full_text:
                # 清空段落并重新构建
                paragraph.clear()

                # 分割并重新添加文本
                parts = full_text.split(old_text)
                for i, part in enumerate(parts):
                    if i > 0:
                        # 添加替换的文本（黄色标记）
                        run = paragraph.add_run(new_text)
                        run.font.highlight_color = self.yellow_color

                    # 添加普通文本
                    if part:
                        paragraph.add_run(part)

                count += 1

        # 在所有表格中查找并替换
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        full_text = paragraph.text
                        if old_text in full_text:
                            # 清空段落并重新构建
                            paragraph.clear()

                            # 分割并重新添加文本
                            parts = full_text.split(old_text)
                            for i, part in enumerate(parts):
                                if i > 0:
                                    # 添加替换的文本（黄色标记）
                                    run = paragraph.add_run(new_text)
                                    run.font.highlight_color = self.yellow_color

                                # 添加普通文本
                                if part:
                                    paragraph.add_run(part)

                            count += 1

        return count
    
    def _replace_date_fields(self, doc, contract_date):
        """替换日期字段（只替换签署日期，不替换合同期限）"""
        count = 0

        # 非常精确的替换：只替换文档最后的签署日期
        # 避免替换合同期限中的日期

        # 先处理合同期限中的日期，确保只替换起始日期
        for paragraph in doc.paragraphs:
            para_text = paragraph.text
            # 如果是合同期限段落，只替换第一个日期占位符
            if '本合同的有效期' in para_text and '自' in para_text and '至' in para_text:
                # 特殊处理：只替换"自"后面的第一个日期
                if '自    年    月    日至' in para_text:
                    if self._replace_in_paragraph_with_highlight(paragraph, '自    年    月    日至', f'自{contract_date}至'):
                        count += 1
                        logger.info(f"   ✅ 合同起始日期: 替换1处")

        # 然后处理签署日期（在文档末尾）
        # 倒序遍历段落，从文档末尾开始查找
        paragraphs = list(doc.paragraphs)
        for paragraph in reversed(paragraphs):
            para_text = paragraph.text
            # 如果段落包含签署相关内容且包含日期占位符
            if ('甲方' in para_text or '乙方' in para_text or '公章' in para_text) and '年    月    日' in para_text:
                # 只替换最后几个段落中的签署日期
                if self._replace_in_paragraph_with_highlight(paragraph, '年    月    日', contract_date):
                    count += 1
                    break  # 只替换第一个找到的签署日期

        # 也检查表格中的签署日期（通常在文档末尾）
        for table in reversed(doc.tables):  # 倒序检查表格
            for row in reversed(table.rows):
                for cell in reversed(row.cells):
                    for paragraph in reversed(cell.paragraphs):
                        para_text = paragraph.text
                        if ('甲方' in para_text or '乙方' in para_text or '签署' in para_text) and '年    月    日' in para_text:
                            if self._replace_in_paragraph_with_highlight(paragraph, '年    月    日', contract_date):
                                count += 1
                                logger.info(f"   ✅ 表格签署日期: 替换1处")
                                return count  # 找到后立即返回

        if count > 0:
            logger.info(f"   ✅ 合同签署日期: 总计替换{count}处")
        else:
            logger.warning(f"   ⚠️ 合同签署日期: 未找到匹配的签署日期位置")

        return count
    
    def _convert_amount_to_chinese(self, amount):
        """转换金额为中文大写"""
        chinese_numbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
        chinese_units = ['', '拾', '佰', '仟']
        
        if amount == 0:
            return '零'
        
        amount_str = str(int(amount))
        result = ''
        
        for i, digit in enumerate(reversed(amount_str)):
            digit_int = int(digit)
            if digit_int != 0:
                result = chinese_numbers[digit_int] + chinese_units[i] + result
            elif result and not result.startswith('零'):
                result = '零' + result
        
        # 简化处理，对于常见金额
        amount_map = {
            1000: '壹仟',
            2000: '贰仟',
            3000: '叁仟',
            5000: '伍仟',
            10000: '壹万'
        }
        
        return amount_map.get(amount, f'{amount}')
    
    def _generate_summary(self, company_data, deposit_amount, replacement_count):
        """生成摘要"""
        return {
            'company_name': company_data['company_name'],
            'deposit_amount': deposit_amount,
            'total_replacements': replacement_count,
            'generation_time': datetime.now().isoformat(),
            'status': 'success'
        }

def main():
    """测试函数"""
    print("🏦 协定存款协议生成器测试")
    print("="*50)
    
    generator = AgreedDepositGenerator()
    
    # 测试中科卓尔
    company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    deposit_amount = 1000  # 1000万元
    
    try:
        output_path, summary = generator.generate_deposit_agreement(company_id, deposit_amount)
        
        print(f"✅ 生成成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"💰 存款额度: {summary['deposit_amount']}万元")
        print(f"📊 替换字段: {summary['total_replacements']}个")
        print(f"🟡 替换内容已标记为黄色")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
