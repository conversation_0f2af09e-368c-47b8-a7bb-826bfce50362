#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建信贷业务申请书模板
基于您提供的标黄字段分析，创建一个完整的Excel模板
"""

import sys
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

def create_credit_application_template():
    """创建信贷业务申请书Excel模板"""
    
    print("🔧 创建信贷业务申请书模板")
    print("=" * 50)
    
    try:
        # 创建新的工作簿
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.title = "信贷业务申请书"
        
        # 设置样式
        title_font = Font(name='宋体', size=16, bold=True)
        header_font = Font(name='宋体', size=12, bold=True)
        normal_font = Font(name='宋体', size=11)
        yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
        center_alignment = Alignment(horizontal='center', vertical='center')
        left_alignment = Alignment(horizontal='left', vertical='center')
        
        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 设置列宽
        sheet.column_dimensions['A'].width = 20
        sheet.column_dimensions['B'].width = 40
        sheet.column_dimensions['C'].width = 20
        sheet.column_dimensions['D'].width = 30
        
        # 第1行：标题
        sheet.merge_cells('A1:D1')
        sheet['A1'] = "信贷业务申请书"
        sheet['A1'].font = title_font
        sheet['A1'].alignment = center_alignment
        
        # 第2行：填报日期
        sheet['A2'] = "填报日期："
        sheet['A2'].font = normal_font
        sheet['B2'] = "2025年3月     日"
        sheet['B2'].font = normal_font
        sheet['B2'].fill = yellow_fill  # 标黄
        
        # 第3行：借款人信息
        sheet['A3'] = "借款人："
        sheet['A3'].font = normal_font
        sheet['B3'] = "成都中科卓尔智能科技集团有限公司（以下简称\"中科卓尔\"或\"公司\"）"
        sheet['B3'].font = normal_font
        sheet['B3'].fill = yellow_fill  # 标黄
        
        # 第4行：编号信息
        sheet['A4'] = "额度编号："
        sheet['A4'].font = normal_font
        sheet['B4'] = "PIFU510000000N202407210（额度）"
        sheet['B4'].font = normal_font
        sheet['B4'].fill = yellow_fill  # 标黄
        
        sheet['C4'] = "业务编号："
        sheet['C4'].font = normal_font
        sheet['D4'] = "PIFU5100000002025N00G8（业务）"
        sheet['D4'].font = normal_font
        sheet['D4'].fill = yellow_fill  # 标黄
        
        # 第5行：空行
        sheet.row_dimensions[5].height = 10
        
        # 第6行：单户综合融资总量有效期
        sheet['A6'] = "单户综合融资总量有效期："
        sheet['A6'].font = normal_font
        sheet['B6'] = "2024-03-06至2025-03-06"
        sheet['B6'].font = normal_font
        sheet['B6'].fill = yellow_fill  # 标黄
        
        # 第7行：空行
        sheet.row_dimensions[7].height = 10
        
        # 第8行：担保方式
        sheet['A8'] = "担保方式："
        sheet['A8'].font = normal_font
        sheet.merge_cells('B8:D8')
        sheet['B8'] = "信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施"
        sheet['B8'].font = normal_font
        sheet['B8'].fill = yellow_fill  # 标黄
        sheet['B8'].alignment = left_alignment
        
        # 第9行：本次支用金额
        sheet['A9'] = "本次支用金额："
        sheet['A9'].font = normal_font
        sheet['B9'] = "        万元"
        sheet['B9'].font = normal_font
        sheet['B9'].fill = yellow_fill  # 标黄
        
        # 第10行：空行
        sheet.row_dimensions[10].height = 15
        
        # 第11行：法审意见标题
        sheet['A11'] = "法审意见："
        sheet['A11'].font = header_font
        
        # 第12-15行：法审意见内容
        legal_opinion = """请将第十三条(六)修改为：\"本合同项下债务在甲、乙双方于2024年3月27日签署的《最高额专利质押合同》（编号：建八卓尔专质（2024）001号）的担保范围之内。\"并核实该质押合同担保的债项签订时间范围是否确实包含了本笔合同签订时间。 落实情况：已按要求修改流贷合同，已核实该笔质押合同担保的债项签订时间范围包含本笔合同签订时间。"""
        
        sheet.merge_cells('A12:D15')
        sheet['A12'] = legal_opinion
        sheet['A12'].font = normal_font
        sheet['A12'].fill = yellow_fill  # 标黄
        sheet['A12'].alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        # 第16行：空行
        sheet.row_dimensions[16].height = 10
        
        # 第17行：业务批复要求
        sheet['A17'] = "业务批复要求："
        sheet['A17'].font = header_font
        
        batch_approval = """同意为成都中科卓尔智能科技集团有限公司发放流动资金贷款2000万元（回收再贷），期限13个月，担保方式：信用，公司实际控制人杨伟提供连带责任保证，追加部分专利质押作为风险缓释措施。其他未尽事项在合规前提下按申报方案（含补充方案）执行。"""
        
        sheet.merge_cells('A18:D20')
        sheet['A18'] = batch_approval
        sheet['A18'].font = normal_font
        sheet['A18'].fill = yellow_fill  # 标黄
        sheet['A18'].alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        # 第21行：空行
        sheet.row_dimensions[21].height = 10
        
        # 第22行：担保情况详细说明
        sheet['A22'] = "担保情况说明："
        sheet['A22'].font = header_font
        
        guarantee_details = """本次公司流动资金贷款担保方式为信用，同时追加公司实控人杨伟连带责任担保，公司部分专利产权质押。
我行与担保人（公司实控人）杨伟签署了合同编号：建八卓尔保（2024）001号的本金最高额保证合同，担保限额为4000万元，担保债权确定期：2024年3月27日至2026年3月27日；已请杨伟配偶王斯颖签署配偶知晓函并上传相关文档。
我行与抵押人（中科卓尔）签订了合同编号：建八卓尔专质(2024）001号的最高额专利权质押合同，担保限额为4000万元，担保债权确定期：2024年3月27日至2026年3月27日，截止2024年8月，已取得抵质押人有权担保决议，本次质押物我行内部评估价值法确认价值为328.98万元，对专利权质押已办妥登记并获得质押登记通知书。"""
        
        sheet.merge_cells('A23:D28')
        sheet['A23'] = guarantee_details
        sheet['A23'].font = normal_font
        sheet['A23'].fill = yellow_fill  # 标黄
        sheet['A23'].alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        # 第29行：空行
        sheet.row_dimensions[29].height = 10
        
        # 第30行：环保分类
        sheet['A30'] = "环保分类："
        sheet['A30'].font = normal_font
        sheet['B30'] = "借款人环保分类为C类，不存在环境和社会风险/是否取得核准允许投放。借款人及其项目不属于涉及ESG风险的信贷客户和投资项目。"
        sheet['B30'].font = normal_font
        sheet['B30'].fill = yellow_fill  # 标黄
        
        # 第31-32行：空行
        sheet.row_dimensions[31].height = 15
        sheet.row_dimensions[32].height = 15
        
        # 第33行：签字区域
        sheet['A33'] = "客户经理（签字）："
        sheet['A33'].font = normal_font
        sheet['C33'] = "日期：  2025年   7  月     日"
        sheet['C33'].font = normal_font
        sheet['C33'].fill = yellow_fill  # 标黄
        
        # 第34行：空行
        sheet.row_dimensions[34].height = 10
        
        # 第35行：领导签字
        sheet['A35'] = "分管经营行领导(签字)："
        sheet['A35'].font = normal_font
        sheet['C35'] = "日期： 2025年   7  月      日"
        sheet['C35'].font = normal_font
        sheet['C35'].fill = yellow_fill  # 标黄
        
        # 设置行高
        for row in range(1, 36):
            if row not in [5, 7, 10, 16, 21, 29, 31, 32, 34]:
                sheet.row_dimensions[row].height = 20
        
        # 保存文件
        output_file = Path("test_output/信贷业务申请书_模板.xlsx")
        workbook.save(output_file)
        workbook.close()
        
        print(f"✅ 信贷业务申请书模板创建成功: {output_file}")
        
        # 验证创建的文件
        try:
            test_workbook = openpyxl.load_workbook(output_file)
            test_sheet = test_workbook.active
            print(f"✅ 验证成功")
            print(f"📋 工作表名称: {test_sheet.title}")
            print(f"📏 工作表尺寸: {test_sheet.max_row} 行 x {test_sheet.max_column} 列")
            print(f"📝 标题内容: {test_sheet['A1'].value}")
            print(f"📝 借款人: {test_sheet['B3'].value}")
            test_workbook.close()
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            
        return output_file
            
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    template_file = create_credit_application_template()
    
    if template_file:
        print(f"\n🎉 模板创建完成！")
        print(f"📁 文件位置: {template_file}")
        print(f"💡 现在可以用这个模板替换原来的模板文件")

if __name__ == "__main__":
    main()
