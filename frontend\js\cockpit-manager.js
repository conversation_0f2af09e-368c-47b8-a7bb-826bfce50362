/**
 * 驾驶舱管理器 - 实现"流程驱动"的核心逻辑
 * 从"信息陈列"到"流程驱动"的革命性转变
 * 版本: v2.0 - OA工作台已修复完成
 */

class CockpitManager {
    constructor() {
        this.currentCustomer = null;
        this.workflowTasks = [
            {
                id: 'service-agreement',
                title: '生成《服务协议》',
                description: '为客户生成银企直联服务协议文档',
                completed: false,
                action: 'generate-agreement'
            },
            {
                id: 'oa-document',
                title: '生成《OA正文》',
                description: '使用AI协作生成OA办公文档',
                completed: false,
                action: 'generate-oa'
            },
            {
                id: 'authorization-form',
                title: '下载《对公客户授权及承诺书》',
                description: '标准PDF表单，已预先准备',
                completed: true,
                action: 'download-auth'
            },
            {
                id: 'application-form',
                title: '下载《对公综合服务申请书》',
                description: '标准PDF表单，已预先准备',
                completed: true,
                action: 'download-app'
            }
        ];
        
        this.init();
    }

    init() {
        try {
            console.log('CockpitManager 初始化开始');
            this.bindEvents();
            this.updateProgress();

            // 延迟加载客户数据，确保API客户端已初始化
            setTimeout(() => {
                this.loadCustomers();
            }, 500);

            console.log('CockpitManager 初始化完成');
        } catch (error) {
            console.error('CockpitManager 初始化失败:', error);
            this.showErrorMessage('驾驶舱初始化失败', [
                `错误详情: ${error.message}`,
                '请刷新页面重试'
            ]);
        }
    }

    bindEvents() {
        // 客户选择事件
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.addEventListener('change', (e) => {
                this.selectCustomer(e.target.value);
            });
        }

        // 更换客户按钮
        const changeCustomerBtn = document.getElementById('change-customer-btn');
        if (changeCustomerBtn) {
            changeCustomerBtn.addEventListener('click', () => {
                this.showCustomerSelection();
            });
        }

        // 工作流程按钮事件 - 修复this绑定
        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-btn')) {
                const btn = e.target.closest('.action-btn');
                const action = btn.dataset.action;
                if (action) {
                    this.handleTaskAction(action);
                }
            }
        });

        // 任务复选框事件 - 修复this绑定
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('task-check')) {
                const taskId = e.target.id.replace('task-', '');
                // 修复taskId处理
                let cleanTaskId = taskId;
                if (taskId === 'agreement') cleanTaskId = 'service-agreement';
                else if (taskId === 'oa') cleanTaskId = 'oa-document';
                else if (taskId === 'auth') cleanTaskId = 'authorization-form';
                else if (taskId === 'app') cleanTaskId = 'application-form';

                this.toggleTask(cleanTaskId, e.target.checked);
            }
        });

        // 清空日志按钮
        const clearLogsBtn = document.getElementById('clear-logs-btn');
        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => {
                this.clearLogs();
            });
        }
    }

    loadCustomers = async () => {
        try {
            console.log('开始加载客户数据...');

            // 方法1：尝试使用API客户端
            if (window.apiClient) {
                console.log('使用API客户端加载数据');
                const response = await window.apiClient.get('/api/companies');
                const companies = response.data;
                this.populateCustomerSelect(companies);
                this.safeAddLog('success', `成功加载 ${companies.length} 个客户`);
                return;
            }

            // 方法2：直接使用fetch
            console.log('API客户端不可用，使用fetch直接调用');
            const response = await fetch('http://127.0.0.1:5000/api/companies');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            const companies = data.data || data;

            this.populateCustomerSelect(companies);
            this.safeAddLog('success', `成功加载 ${companies.length} 个客户`);

        } catch (error) {
            console.error('加载客户列表失败:', error);

            // 方法3：使用模拟数据
            console.log('API调用失败，使用模拟数据');
            const mockCompanies = [
                { id: 'mock1', company_name: '成都卫讯科技有限公司' },
                { id: 'mock2', company_name: '测试企业2' }
            ];

            this.populateCustomerSelect(mockCompanies);
            this.safeAddLog('warning', '使用模拟数据，部分功能可能不可用');
        }
    }

    populateCustomerSelect(companies) {
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.innerHTML = '<option value="">请选择客户...</option>';
            companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company.id;
                option.textContent = company.company_name;
                customerSelect.appendChild(option);
            });
        }
    }

    selectCustomer = async (customerId) => {
        if (!customerId) return;

        try {
            this.safeAddLog('info', '正在加载客户信息...');

            // 尝试从API获取详细信息
            if (window.apiClient) {
                const response = await window.apiClient.get(`/api/company/${customerId}`);
                this.currentCustomer = response.data;
            } else {
                // 使用模拟数据
                this.currentCustomer = {
                    id: customerId,
                    company_name: customerId === 'mock1' ? '成都卫讯科技有限公司' : '测试企业2',
                    unified_social_credit_code: '915101003320526751',
                    registered_address: '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                    personnel: [
                        { person_name: '万明刚', role: '法定代表人' }
                    ],
                    tags: [
                        { tag_name: '高新技术企业', tag_category: '企业资质' }
                    ]
                };
            }

            this.showWorkflowStage();
            this.updateCustomerSidebar();
            this.updateStatusFeedback('客户信息已加载，可以开始办理业务');

            this.safeAddLog('success', `已选择客户: ${this.currentCustomer.company_name}`);
        } catch (error) {
            console.error('选择客户失败:', error);
            this.safeAddLog('error', '加载客户信息失败: ' + error.message);
        }
    }

    showWorkflowStage() {
        // 隐藏客户选择舞台
        const selectionStage = document.getElementById('customer-selection-stage');
        if (selectionStage) {
            selectionStage.style.display = 'none';
        }

        // 显示工作流程舞台
        const workflowStage = document.getElementById('workflow-stage');
        if (workflowStage) {
            workflowStage.style.display = 'block';
        }

        // 显示侧边栏和日志面板
        const customerSidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');
        
        if (customerSidebar) customerSidebar.style.display = 'block';
        if (logsPanel) logsPanel.style.display = 'block';
    }

    showCustomerSelection() {
        // 显示客户选择舞台
        const selectionStage = document.getElementById('customer-selection-stage');
        if (selectionStage) {
            selectionStage.style.display = 'block';
        }

        // 隐藏工作流程舞台
        const workflowStage = document.getElementById('workflow-stage');
        if (workflowStage) {
            workflowStage.style.display = 'none';
        }

        // 隐藏侧边栏和日志面板
        const customerSidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');
        
        if (customerSidebar) customerSidebar.style.display = 'none';
        if (logsPanel) logsPanel.style.display = 'none';

        // 重置客户选择
        this.currentCustomer = null;
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.value = '';
        }
    }

    updateCustomerSidebar() {
        if (!this.currentCustomer) return;

        // 更新客户卡片
        const elements = {
            'sidebar-company-name': this.currentCustomer.company_name,
            'sidebar-credit-code': this.currentCustomer.unified_social_credit_code,
            'sidebar-registered-address': this.currentCustomer.registered_address || '未提供',
            'sidebar-communication-address': this.currentCustomer.communication_address || '未提供',
            'sidebar-business-description': this.currentCustomer.business_description || '未提供'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // 更新人员信息
        this.updatePersonnelSidebar();
        
        // 更新企业标签
        this.updateTagsSidebar();
    }

    updatePersonnelSidebar() {
        const personnelList = document.getElementById('sidebar-personnel-list');
        if (!personnelList || !this.currentCustomer.personnel) return;

        personnelList.innerHTML = '';
        this.currentCustomer.personnel.forEach(person => {
            const personDiv = document.createElement('div');
            personDiv.className = 'personnel-item-compact';
            personDiv.innerHTML = `
                <strong>${person.person_name}</strong>
                <div style="font-size: 0.8rem; color: #666;">${person.role}</div>
            `;
            personnelList.appendChild(personDiv);
        });
    }

    updateTagsSidebar() {
        const tagsContainer = document.getElementById('sidebar-company-tags');
        if (!tagsContainer || !this.currentCustomer.tags) return;

        tagsContainer.innerHTML = '';
        this.currentCustomer.tags.forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = 'tag-compact';
            tagSpan.textContent = tag.tag_name;
            tagsContainer.appendChild(tagSpan);
        });
    }

    handleTaskAction = async (action) => {
        if (!this.currentCustomer) {
            this.safeAddLog('error', '请先选择客户');
            return;
        }

        this.safeAddLog('info', `正在执行操作: ${this.getActionDescription(action)}`);

        try {
            switch (action) {
                case 'generate-agreement':
                    await this.generateServiceAgreement();
                    break;
                case 'generate-oa':
                    await this.openOAWorkspace();
                    break;
                case 'download-auth':
                    await this.downloadAuthForm();
                    break;
                case 'download-app':
                    await this.downloadAppForm();
                    break;
                default:
                    this.safeAddLog('error', '未知操作: ' + action);
            }
        } catch (error) {
            console.error('任务操作失败:', error);
            this.safeAddLog('error', `操作失败: ${error.message}`);
        }
    }

    generateServiceAgreement = async () => {
        this.updateStatusFeedback('正在为您生成《服务协议》，请稍候...', 'processing');

        try {
            // 使用fetch直接调用API
            const response = await fetch('http://localhost:5000/api/documents/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id,
                    document_type: 'agreement'
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 获取文件blob
            const blob = await response.blob();

            // 触发下载
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentCustomer.company_name}_服务协议.docx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.completeTask('service-agreement');
            this.safeAddLog('success', `《服务协议》生成成功，已自动下载`);
            this.updateStatusFeedback('《服务协议》生成完成！', 'success');
        } catch (error) {
            console.error('生成服务协议失败:', error);
            this.safeAddLog('error', `生成《服务协议》失败: ${error.message}`);
            this.updateStatusFeedback('文档生成失败，请重试', 'error');
        }
    }

    async openOAWorkspace() {
        console.log('🤖 OA工作台v2.0 - 开始执行新的工作台逻辑');
        this.addLog('info', '正在打开OA协作工作台v2.0...');

        try {
            // 显示OA工作台模态窗口
            const modal = document.getElementById('oa-workspace-modal');
            if (modal) {
                modal.style.display = 'flex';

                // 显示加载状态
                this.showOAWorkspaceLoading();

                // 准备半成品OA文档
                await this.prepareOADocument();

                this.addLog('success', 'OA协作工作台已打开');
            } else {
                throw new Error('OA工作台模态窗口未找到');
            }
        } catch (error) {
            this.addLog('error', `打开OA工作台失败: ${error.message}`);
            this.showOAWorkspaceError(error.message);
        }
    }

    showOAWorkspaceLoading() {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = `
                <div class="oa-workspace-loading">
                    <div class="loading-header">
                        <h2>🤖 OA正文人机协作工作台</h2>
                        <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                    </div>
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <p>正在为 <strong>${this.currentCustomer.company_name}</strong> 准备半成品OA文档...</p>
                        <p class="loading-detail">系统正在自动填充客户信息并高亮标记...</p>
                    </div>
                </div>
            `;
        }
    }

    async prepareOADocument() {
        try {
            // 调用后端API准备半成品文档
            const response = await fetch('/api/documents/prepare_oa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                // 显示完整的工作台界面
                this.showFullOAWorkspace(result.data);
            } else {
                throw new Error(result.message || '准备文档失败');
            }

        } catch (error) {
            console.error('准备OA文档失败:', error);
            throw error;
        }
    }

    showFullOAWorkspace(oaData) {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = this.generateOAWorkspaceHTML(oaData);

            // 绑定工作台事件
            this.bindOAWorkspaceEvents(oaData);
        }
    }

    generateOAWorkspaceHTML(oaData) {
        const geminiPrompt = this.generateGeminiPrompt(oaData.company_info);

        return `
            <div class="oa-workspace-container">
                <!-- 工作台头部 -->
                <div class="workspace-header">
                    <div class="header-left">
                        <h2>🤖 OA正文人机协作工作台</h2>
                        <p class="customer-info">当前客户：<strong>${oaData.company_info.company_name}</strong></p>
                    </div>
                    <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                </div>

                <!-- 工作台主体 -->
                <div class="workspace-body">
                    <!-- 左侧：半成品文档展示 -->
                    <div class="document-panel">
                        <div class="panel-header">
                            <h3>📄 半成品文档预览</h3>
                            <div class="replacement-info">
                                <span class="highlight-legend">🟡 黄色高亮 = 系统自动填充</span>
                                <span class="replacement-count">已替换 ${oaData.replacements_made.length} 处内容</span>
                            </div>
                        </div>
                        <div class="document-content" id="oa-document-preview">
                            ${oaData.html_content}
                        </div>
                    </div>

                    <!-- 右侧：AI协作面板 -->
                    <div class="ai-panel">
                        <!-- Gemini提示词区域 -->
                        <div class="prompt-section">
                            <div class="section-header">
                                <h3>🎯 Gemini提示词</h3>
                                <button class="copy-btn" id="copy-prompt-btn">📋 复制提示词</button>
                            </div>
                            <div class="prompt-content">
                                <textarea id="gemini-prompt" readonly>${geminiPrompt}</textarea>
                            </div>
                        </div>

                        <!-- AI回复输入区域 -->
                        <div class="ai-input-section">
                            <div class="section-header">
                                <h3>🤖 AI生成内容</h3>
                                <p class="instruction">请将Gemini的回复内容粘贴到下方文本框中</p>
                            </div>
                            <div class="ai-input-content">
                                <textarea id="ai-response" placeholder="请在此粘贴Gemini生成的内容...&#10;&#10;提示：&#10;1. 复制上方的提示词到Gemini&#10;2. 获取AI回复后粘贴到此处&#10;3. 点击下方按钮完成文档合成"></textarea>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <button class="action-btn secondary" id="preview-final-btn" disabled>
                                👁️ 预览最终文档
                            </button>
                            <button class="action-btn primary" id="generate-final-btn" disabled>
                                ✅ 完成并生成文档
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 工作台底部状态 -->
                <div class="workspace-footer">
                    <div class="status-info">
                        <span class="status-item">📊 模板加载完成</span>
                        <span class="status-item">🔄 自动替换完成</span>
                        <span class="status-item" id="ai-status">⏳ 等待AI内容</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateGeminiPrompt(companyInfo) {
        const companyName = companyInfo.company_name || '【公司全称】';

        return `请扮演一名专业的银行客户经理。我正在为客户"${companyName}"撰写一份关于开通"银企直联"服务的内部请示文件（OA）。请为我草拟其中"一、客户综合情况"和"二、业务价值及费用减免说明"这两个部分的正文。

要求：
1. 语言专业、逻辑清晰
2. 重点突出该客户的战略价值
3. 强调此项业务对我行的潜在收益
4. 内容应当具体且有说服力
5. 请直接输出正文内容，无需添加任何额外的标题或解释

客户基本信息：
- 公司名称：${companyName}
- 行业分类：${companyInfo.industry_category || '制造业'}
- 注册资本：${companyInfo.registered_capital || ''}万元
- 经营范围：${companyInfo.business_scope || ''}

请基于以上信息生成专业的OA正文内容。`;
    }

    bindOAWorkspaceEvents(oaData) {
        // 复制提示词按钮
        const copyPromptBtn = document.getElementById('copy-prompt-btn');
        if (copyPromptBtn) {
            copyPromptBtn.addEventListener('click', () => {
                const promptTextarea = document.getElementById('gemini-prompt');
                if (promptTextarea) {
                    promptTextarea.select();
                    document.execCommand('copy');

                    // 显示复制成功提示
                    copyPromptBtn.textContent = '✅ 已复制';
                    setTimeout(() => {
                        copyPromptBtn.textContent = '📋 复制提示词';
                    }, 2000);
                }
            });
        }

        // AI回复输入监听
        const aiResponseTextarea = document.getElementById('ai-response');
        if (aiResponseTextarea) {
            aiResponseTextarea.addEventListener('input', () => {
                const hasContent = aiResponseTextarea.value.trim().length > 0;

                // 启用/禁用按钮
                const previewBtn = document.getElementById('preview-final-btn');
                const generateBtn = document.getElementById('generate-final-btn');

                if (previewBtn) previewBtn.disabled = !hasContent;
                if (generateBtn) generateBtn.disabled = !hasContent;

                // 更新状态
                const aiStatus = document.getElementById('ai-status');
                if (aiStatus) {
                    if (hasContent) {
                        aiStatus.textContent = '✅ AI内容已输入';
                        aiStatus.style.color = '#4CAF50';
                    } else {
                        aiStatus.textContent = '⏳ 等待AI内容';
                        aiStatus.style.color = '#666';
                    }
                }
            });
        }

        // 预览最终文档按钮
        const previewBtn = document.getElementById('preview-final-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => {
                this.previewFinalOADocument(oaData);
            });
        }

        // 生成最终文档按钮
        const generateBtn = document.getElementById('generate-final-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateFinalOADocument(oaData);
            });
        }
    }

    previewFinalOADocument(oaData) {
        const aiContent = document.getElementById('ai-response').value.trim();
        if (!aiContent) {
            alert('请先输入AI生成的内容');
            return;
        }

        // 在新窗口中显示预览
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>OA文档预览 - ${oaData.company_info.company_name}</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; padding: 2rem; line-height: 1.6; }
                        .preview-header { border-bottom: 2px solid #333; padding-bottom: 1rem; margin-bottom: 2rem; }
                        .auto-replaced { background-color: yellow; padding: 2px 4px; }
                        .ai-content { background-color: #f0f8ff; padding: 1rem; border-left: 4px solid #2196F3; margin: 1rem 0; }
                        .doc-paragraph { margin: 0.5rem 0; }
                        .doc-heading { font-weight: bold; margin: 1rem 0 0.5rem 0; }
                    </style>
                </head>
                <body>
                    <div class="preview-header">
                        <h1>OA正文预览</h1>
                        <p>客户：${oaData.company_info.company_name}</p>
                    </div>
                    <div class="document-preview">
                        ${oaData.html_content}
                        <div class="ai-content">
                            <h3>AI生成内容：</h3>
                            <div>${aiContent.replace(/\n/g, '<br>')}</div>
                        </div>
                    </div>
                </body>
            </html>
        `);
        previewWindow.document.close();
    }

    async generateFinalOADocument(oaData) {
        const aiContent = document.getElementById('ai-response').value.trim();
        if (!aiContent) {
            alert('请先输入AI生成的内容');
            return;
        }

        try {
            // 显示生成中状态
            const generateBtn = document.getElementById('generate-final-btn');
            const originalText = generateBtn.textContent;
            generateBtn.textContent = '🔄 生成中...';
            generateBtn.disabled = true;

            // 调用后端API合成最终文档
            const response = await fetch('/api/documents/finalize_oa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id,
                    temp_doc_path: oaData.temp_doc_path,
                    ai_content: aiContent
                })
            });

            if (response.ok) {
                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.currentCustomer.company_name}_OA正文.docx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                // 显示成功消息
                this.addLog('success', 'OA正文文档生成成功');

                // 关闭工作台
                document.getElementById('oa-workspace-modal').style.display = 'none';

                // 标记任务完成
                this.toggleTask('oa-document', true);

            } else {
                throw new Error(`生成失败: ${response.status}`);
            }

        } catch (error) {
            console.error('生成最终文档失败:', error);
            this.addLog('error', `生成最终文档失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            const generateBtn = document.getElementById('generate-final-btn');
            if (generateBtn) {
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;
            }
        }
    }

    showOAWorkspaceError(errorMessage) {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = `
                <div class="oa-workspace-error">
                    <div class="error-header">
                        <h2>❌ OA工作台加载失败</h2>
                        <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                    </div>
                    <div class="error-content">
                        <p>错误信息：${errorMessage}</p>
                        <div class="error-actions">
                            <button onclick="location.reload()" class="retry-btn">🔄 重新加载</button>
                            <button onclick="this.closest('.oa-workspace-modal').style.display='none'" class="close-error-btn">关闭</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    async downloadAuthForm() {
        // 模拟PDF下载
        this.addLog('success', '《对公客户授权及承诺书》下载完成');
        this.updateStatusFeedback('PDF表单下载完成', 'success');
    }

    async downloadAppForm() {
        // 模拟PDF下载
        this.addLog('success', '《对公综合服务申请书》下载完成');
        this.updateStatusFeedback('PDF表单下载完成', 'success');
    }

    completeTask(taskId) {
        const task = this.workflowTasks.find(t => t.id === taskId);
        if (task) {
            task.completed = true;
            
            // 更新UI
            const checkbox = document.getElementById(`task-${taskId.replace('-', '')}`);
            if (checkbox) {
                checkbox.checked = true;
            }
            
            const workflowItem = document.querySelector(`[data-task="${taskId}"]`);
            if (workflowItem) {
                workflowItem.classList.add('completed');
            }
            
            this.updateProgress();
        }
    }

    toggleTask(taskId, completed) {
        const task = this.workflowTasks.find(t => t.id === taskId);
        if (task) {
            task.completed = completed;
            this.updateProgress();
        }
    }

    updateProgress() {
        const completedTasks = this.workflowTasks.filter(t => t.completed).length;
        const totalTasks = this.workflowTasks.length;
        const percentage = (completedTasks / totalTasks) * 100;

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${completedTasks}/${totalTasks} 已完成`;
        }
    }

    updateStatusFeedback(message, type = 'info') {
        const statusFeedback = document.getElementById('status-feedback');
        if (!statusFeedback) return;

        const feedbackText = statusFeedback.querySelector('.feedback-text');
        const feedbackIcon = statusFeedback.querySelector('.feedback-icon');

        if (feedbackText) {
            feedbackText.textContent = message;
        }

        if (feedbackIcon) {
            const icons = {
                'info': '✨',
                'success': '✅',
                'error': '❌',
                'processing': '⏳'
            };
            feedbackIcon.textContent = icons[type] || '✨';
        }

        // 添加动画效果
        statusFeedback.style.transform = 'scale(0.95)';
        setTimeout(() => {
            statusFeedback.style.transform = 'scale(1)';
        }, 150);
    }

    safeAddLog = (type, message) => {
        try {
            console.log(`[${type.toUpperCase()}] ${message}`);

            const logsContainer = document.getElementById('operation-logs');
            if (!logsContainer) {
                console.warn('日志容器未找到');
                return;
            }

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.innerHTML = `
                <div style="font-weight: 500; margin-bottom: 0.2rem;">
                    ${timestamp} - ${this.getLogTypeIcon(type)}
                </div>
                <div>${message}</div>
            `;

            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            // 限制日志数量
            const logs = logsContainer.querySelectorAll('.log-entry');
            if (logs.length > 20) {
                logs[logs.length - 1].remove();
            }

            // 滚动到顶部
            logsContainer.scrollTop = 0;
        } catch (error) {
            console.error('添加日志失败:', error);
        }
    }

    // 保持向后兼容
    addLog = this.safeAddLog;

    getLogTypeIcon(type) {
        const icons = {
            'success': '✅ 成功',
            'error': '❌ 错误',
            'info': 'ℹ️ 信息',
            'warning': '⚠️ 警告'
        };
        return icons[type] || 'ℹ️ 信息';
    }

    getActionDescription(action) {
        const descriptions = {
            'generate-agreement': '生成《服务协议》',
            'generate-oa': '打开OA协作工作台',
            'download-auth': '下载《对公客户授权及承诺书》',
            'download-app': '下载《对公综合服务申请书》'
        };
        return descriptions[action] || action;
    }

    clearLogs() {
        const logsContainer = document.getElementById('operation-logs');
        if (logsContainer) {
            logsContainer.innerHTML = '';
            this.addLog('info', '操作日志已清空');
        }
    }

    showErrorMessage(title, messages) {
        const selectionStage = document.getElementById('customer-selection-stage');
        if (selectionStage) {
            selectionStage.innerHTML = `
                <div class="selection-content">
                    <div class="selection-icon" style="color: #f44336;">❌</div>
                    <h2 style="color: #f44336;">${title}</h2>
                    <div style="text-align: left; max-width: 500px; margin: 0 auto;">
                        ${messages.map(msg => `<p style="margin: 0.5rem 0; color: #666;">${msg}</p>`).join('')}
                    </div>
                    <div style="margin-top: 2rem;">
                        <button onclick="location.reload()"
                                style="padding: 0.8rem 2rem; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    }
}

// 初始化驾驶舱管理器
document.addEventListener('DOMContentLoaded', () => {
    try {
        // 等待其他脚本加载完成
        setTimeout(() => {
            window.cockpitManager = new CockpitManager();
        }, 100);
    } catch (error) {
        console.error('驾驶舱初始化失败:', error);

        // 显示错误信息
        const selectionStage = document.getElementById('customer-selection-stage');
        if (selectionStage) {
            selectionStage.innerHTML = `
                <div class="selection-content">
                    <div class="selection-icon" style="color: #f44336;">❌</div>
                    <h2 style="color: #f44336;">应用启动失败</h2>
                    <p style="color: #666;">JavaScript初始化错误: ${error.message}</p>
                    <div style="margin-top: 2rem;">
                        <button onclick="location.reload()"
                                style="padding: 0.8rem 2rem; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    }
});
