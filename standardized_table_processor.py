#!/usr/bin/env python3
"""
标准化表格处理器 - 为后续客户提供标准化的提取和替换流程
"""

import docx
from pathlib import Path
from datetime import datetime

class StandardizedTableProcessor:
    """标准化表格处理器"""
    
    def __init__(self, quota_file_path, business_file_path):
        self.quota_file = Path(quota_file_path)
        self.business_file = Path(business_file_path)
        
    def extract_all_conditions(self):
        """提取所有条件信息"""
        print('=== 标准化提取流程 ===\n')
        
        conditions = {
            'precondition': self._extract_precondition(),
            'continuous_conditions': self._extract_continuous_conditions(),
            'loan_conditions': self._extract_loan_conditions()
        }
        
        return conditions
    
    def _extract_precondition(self):
        """提取用信前提条件"""
        print('📖 提取用信前提条件...')
        
        if not self.quota_file.exists():
            print('❌ 额度申报书文件不存在')
            return None
            
        doc = docx.Document(self.quota_file)
        
        # 标准位置：第5个表格（索引4），第2行（索引1），第2列（索引1）
        try:
            table = doc.tables[4]
            precondition_text = table.rows[1].cells[1].text.strip()
            print(f'✅ 用信前提条件提取成功: {len(precondition_text)}字符')
            return {
                'text': precondition_text,
                'source_location': '额度申报书-表格5-行2-列2'
            }
        except (IndexError, AttributeError) as e:
            print(f'❌ 用信前提条件提取失败: {e}')
            return None
    
    def _extract_continuous_conditions(self):
        """提取持续条件"""
        print('📖 提取持续条件...')
        
        if not self.quota_file.exists():
            print('❌ 额度申报书文件不存在')
            return None
            
        doc = docx.Document(self.quota_file)
        
        # 标准位置：第6个表格（索引5）
        try:
            table = doc.tables[5]
            conditions = []
            
            # 从第2行开始提取（跳过表头）
            for row_idx in range(1, len(table.rows)):
                row = table.rows[row_idx]
                if len(row.cells) >= 4:
                    condition = {
                        'name': row.cells[0].text.strip(),
                        'current_setting': row.cells[1].text.strip(),
                        'previous_setting': row.cells[2].text.strip(),
                        'current_status': row.cells[3].text.strip()
                    }
                    conditions.append(condition)
            
            print(f'✅ 持续条件提取成功: {len(conditions)}条')
            return {
                'conditions': conditions,
                'source_location': '额度申报书-表格6',
                'total_rows': len(table.rows)
            }
        except (IndexError, AttributeError) as e:
            print(f'❌ 持续条件提取失败: {e}')
            return None
    
    def _extract_loan_conditions(self):
        """提取贷款条件"""
        print('📖 提取贷款条件...')
        
        if not self.business_file.exists():
            print('❌ 业务申报书文件不存在')
            return None
            
        doc = docx.Document(self.business_file)
        
        # 标准位置：第7个表格（索引6）
        try:
            table = doc.tables[6]
            conditions = []
            
            for row in table.rows:
                if len(row.cells) >= 2:
                    number = row.cells[0].text.strip()
                    content = row.cells[1].text.strip()
                    if number and content:
                        conditions.append({
                            'number': number,
                            'content': content,
                            'full_text': f"{number}. {content}"
                        })
            
            print(f'✅ 贷款条件提取成功: {len(conditions)}条')
            return {
                'conditions': conditions,
                'source_location': '业务申报书-表格7'
            }
        except (IndexError, AttributeError) as e:
            print(f'❌ 贷款条件提取失败: {e}')
            return None
    
    def generate_implementation_table(self, conditions, output_dir='test_output'):
        """生成落实情况表"""
        print('\n🔄 生成落实情况表...')
        
        # 创建新文档
        doc = docx.Document()
        
        # 添加标题
        title = doc.add_heading('条件落实情况表', 0)
        title.alignment = docx.enum.text.WD_ALIGN_PARAGRAPH.CENTER
        
        # 1. 用信前提条件部分
        if conditions.get('precondition'):
            self._add_precondition_section(doc, conditions['precondition'])
        
        # 2. 持续条件部分
        if conditions.get('continuous_conditions'):
            self._add_continuous_conditions_section(doc, conditions['continuous_conditions'])
        
        # 3. 贷款条件部分
        if conditions.get('loan_conditions'):
            self._add_loan_conditions_section(doc, conditions['loan_conditions'])
        
        # 保存文档
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = Path(output_dir) / f'落实情况表_{timestamp}.docx'
        doc.save(output_path)
        
        print(f'✅ 落实情况表生成完成: {output_path}')
        return output_path
    
    def _add_precondition_section(self, doc, precondition_data):
        """添加用信前提条件部分"""
        doc.add_heading('一、用信前提条件及落实情况', level=1)
        
        table = doc.add_table(rows=2, cols=4)
        table.style = 'Table Grid'
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '产品'
        hdr_cells[1].text = '本次设置的用信前提条件'
        hdr_cells[2].text = '前次单户综合融资总量方案设定条件'
        hdr_cells[3].text = '本次申报时点实际情况'
        
        # 内容行
        content_cells = table.rows[1].cells
        content_cells[0].text = '流动资金贷款\n（及可串用该额度的其他业务品种）'
        content_cells[1].text = precondition_data['text']
        content_cells[2].text = '前次单户综合融资总量方案设定条件'
        content_cells[3].text = '本次申报时点实际情况'
    
    def _add_continuous_conditions_section(self, doc, continuous_data):
        """添加持续条件部分"""
        doc.add_heading('二、单户综合融资总量用信持续条件', level=1)
        
        conditions = continuous_data['conditions']
        table = doc.add_table(rows=len(conditions)+1, cols=4)
        table.style = 'Table Grid'
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '持续条件'
        hdr_cells[1].text = '本次设置的用信持续条件'
        hdr_cells[2].text = '前次单户综合融资总量方案设定条件'
        hdr_cells[3].text = '本次申报时点实际情况'
        
        # 内容行
        for idx, condition in enumerate(conditions):
            row = table.rows[idx+1]
            row.cells[0].text = condition['name']
            row.cells[1].text = condition['current_setting']
            row.cells[2].text = condition['previous_setting']
            row.cells[3].text = condition['current_status']
    
    def _add_loan_conditions_section(self, doc, loan_data):
        """添加贷款条件部分"""
        doc.add_heading('三、单笔业务申报书中列明的贷款条件及落实情况', level=1)
        
        conditions = loan_data['conditions']
        table = doc.add_table(rows=len(conditions)+1, cols=2)
        table.style = 'Table Grid'
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '序号'
        hdr_cells[1].text = '贷款条件内容'
        
        # 内容行
        for idx, condition in enumerate(conditions):
            row = table.rows[idx+1]
            row.cells[0].text = condition['number']
            row.cells[1].text = condition['content']

def main():
    """主函数 - 演示标准化流程"""
    # 初始化处理器
    processor = StandardizedTableProcessor(
        'templates/contract_disbursement/额度申报书.docx',
        'templates/contract_disbursement/业务申报书.docx'
    )
    
    # 提取所有条件
    conditions = processor.extract_all_conditions()
    
    # 生成落实情况表
    if any(conditions.values()):
        output_path = processor.generate_implementation_table(conditions)
        print(f'\n🎉 标准化处理完成！输出文件: {output_path}')
    else:
        print('\n❌ 未能提取到有效数据，无法生成落实情况表')

if __name__ == "__main__":
    main()
