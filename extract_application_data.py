#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从申报书中提取业务信息
包括额度申报书和业务申报书的关键数据
"""

import docx
from pathlib import Path
import logging
import re
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ApplicationDataExtractor:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_output_dir = self.project_root / "test_output"
        self.quota_file = self.test_output_dir / "额度申报书.docx"
        self.business_file = self.test_output_dir / "业务申报书.doc"
        
    def extract_all_data(self):
        """提取所有申报书数据"""
        logger.info("📖 开始提取申报书数据...")
        
        print("\n" + "="*80)
        print("📋 申报书数据提取分析")
        print("="*80)
        
        # 提取额度申报书数据
        quota_data = self._extract_quota_data()
        
        # 提取业务申报书数据
        business_data = self._extract_business_data()
        
        # 合并和分析数据
        combined_data = self._combine_data(quota_data, business_data)
        
        # 显示提取结果
        self._display_extracted_data(combined_data)
        
        # 分析数据库映射
        self._analyze_database_mapping(combined_data)
        
        print("\n" + "="*80)
        print("✅ 数据提取完成")
        print("="*80)
        
        return combined_data
    
    def _extract_quota_data(self):
        """提取额度申报书数据"""
        print(f"\n📄 分析额度申报书: {self.quota_file}")
        print("-" * 60)
        
        if not self.quota_file.exists():
            print("❌ 额度申报书文件不存在")
            return {}
        
        try:
            doc = docx.Document(self.quota_file)
            quota_data = {}
            
            # 提取文档内容
            full_text = ""
            for para in doc.paragraphs:
                full_text += para.text + "\n"
            
            # 提取表格内容
            table_data = []
            for table in doc.tables:
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                    full_text += " | ".join(row_data) + "\n"
            
            print(f"   📊 段落数: {len(doc.paragraphs)}")
            print(f"   📊 表格数: {len(doc.tables)}")
            print(f"   📊 总字符数: {len(full_text)}")
            
            # 使用正则表达式提取关键信息
            quota_data = self._extract_quota_patterns(full_text)
            
            return quota_data
            
        except Exception as e:
            logger.error(f"❌ 读取额度申报书失败: {e}")
            return {}
    
    def _extract_business_data(self):
        """提取业务申报书数据"""
        print(f"\n📄 分析业务申报书: {self.business_file}")
        print("-" * 60)

        # 检查是否有.docx版本
        business_docx = self.test_output_dir / "业务申报书.docx"

        if business_docx.exists():
            print(f"   ✅ 发现.docx版本，使用: {business_docx}")
            target_file = business_docx
        elif self.business_file.exists():
            target_file = self.business_file
        else:
            print("❌ 业务申报书文件不存在")
            return {}

        try:
            # 尝试读取文件
            if target_file.suffix.lower() == '.doc':
                # 如果是.doc格式，建议转换为.docx
                print("⚠️ 检测到.doc格式，尝试基本文本提取...")
                return self._extract_doc_content()
            else:
                doc = docx.Document(target_file)
                business_data = {}

                # 提取文档内容
                full_text = ""
                for para in doc.paragraphs:
                    full_text += para.text + "\n"

                # 提取表格内容
                for table in doc.tables:
                    for row in table.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        full_text += " | ".join(row_data) + "\n"

                print(f"   📊 段落数: {len(doc.paragraphs)}")
                print(f"   📊 表格数: {len(doc.tables)}")
                print(f"   📊 总字符数: {len(full_text)}")

                # 使用正则表达式提取关键信息
                business_data = self._extract_business_patterns(full_text)

                return business_data

        except Exception as e:
            logger.error(f"❌ 读取业务申报书失败: {e}")
            return {}
    
    def _extract_doc_content(self):
        """尝试提取.doc文件内容"""
        print("   🔄 尝试读取.doc格式文件...")
        
        # 这里可以尝试使用其他方法读取.doc文件
        # 或者提示用户转换格式
        print("   💡 建议：请将业务申报书.doc转换为.docx格式以获得更好的解析效果")
        return {}
    
    def _extract_quota_patterns(self, text):
        """从额度申报书中提取关键模式"""
        quota_data = {}

        print("   🔍 正在提取额度申报书关键信息...")

        # 提取额度编号 - 更全面的模式
        quota_patterns = [
            r'额度编号[：:]\s*([A-Z0-9]+)',
            r'编号[：:]\s*([A-Z0-9]+)',
            r'(PIFU\d+[A-Z0-9]*)',
            r'申报编号[：:]\s*([A-Z0-9]+)',
        ]

        for pattern in quota_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                quota_data['quota_number'] = matches[0]
                print(f"      ✅ 额度编号: {matches[0]}")
                break

        # 提取额度金额 - 更多模式
        amount_patterns = [
            r'额度[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'授信额度[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'融资额度[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'总额度[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'人民币[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
        ]

        for pattern in amount_patterns:
            matches = re.findall(pattern, text)
            if matches:
                quota_data['quota_amount'] = matches[0].replace(',', '')
                print(f"      ✅ 额度金额: {matches[0]}万元")
                break

        # 提取有效期 - 更灵活的日期格式
        period_patterns = [
            r'有效期[：:]?\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)\s*至\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)',
            r'期限[：:]?\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)\s*至\s*(\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?)',
            r'(\d{4}-\d{1,2}-\d{1,2})\s*至\s*(\d{4}-\d{1,2}-\d{1,2})',
            r'(\d{4}/\d{1,2}/\d{1,2})\s*至\s*(\d{4}/\d{1,2}/\d{1,2})',
        ]

        for pattern in period_patterns:
            matches = re.findall(pattern, text)
            if matches:
                quota_data['quota_valid_from'] = matches[0][0]
                quota_data['quota_valid_to'] = matches[0][1]
                print(f"      ✅ 有效期: {matches[0][0]} 至 {matches[0][1]}")
                break

        # 提取企业名称
        company_patterns = [
            r'(成都中科卓尔智能科技集团有限公司)',
            r'(神光光学集团有限公司)',
            r'借款人[：:]?\s*([^，。！？\n]*有限公司)',
            r'申请人[：:]?\s*([^，。！？\n]*有限公司)',
        ]

        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            if matches:
                quota_data['company_name'] = matches[0]
                print(f"      ✅ 企业名称: {matches[0]}")
                break

        # 提取担保方式
        guarantee_patterns = [
            r'担保方式[：:]?\s*([^，。！？\n]{1,100})',
            r'担保类型[：:]?\s*([^，。！？\n]{1,100})',
            r'担保措施[：:]?\s*([^，。！？\n]{1,100})',
        ]

        for pattern in guarantee_patterns:
            matches = re.findall(pattern, text)
            if matches:
                quota_data['guarantee_type'] = matches[0].strip()
                print(f"      ✅ 担保方式: {matches[0].strip()}")
                break

        # 提取额度持续条件
        quota_continuous_patterns = [
            r'额度持续条件[：:]?\s*([^。！？]*)',
            r'持续条件[：:]?\s*([^。！？]*)',
            r'额度管理条件[：:]?\s*([^。！？]*)',
        ]

        for pattern in quota_continuous_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                quota_data['quota_continuous_conditions'] = matches[0].strip()
                print(f"      ✅ 额度持续条件: {matches[0].strip()[:50]}...")
                break

        # 提取额度管理要求
        quota_management_patterns = [
            r'额度管理要求[：:]?\s*([^。！？]*)',
            r'管理要求[：:]?\s*([^。！？]*)',
            r'额度管理[：:]?\s*([^。！？]*)',
        ]

        for pattern in quota_management_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                quota_data['quota_management_requirements'] = matches[0].strip()
                print(f"      ✅ 额度管理要求: {matches[0].strip()[:50]}...")
                break

        # 提取风险缓释措施
        risk_mitigation_patterns = [
            r'风险缓释措施[：:]?\s*([^。！？]*)',
            r'风险控制措施[：:]?\s*([^。！？]*)',
            r'风险防控[：:]?\s*([^。！？]*)',
        ]

        for pattern in risk_mitigation_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                quota_data['risk_mitigation_measures'] = matches[0].strip()
                print(f"      ✅ 风险缓释措施: {matches[0].strip()[:50]}...")
                break

        return quota_data
    
    def _extract_business_patterns(self, text):
        """从业务申报书中提取关键模式"""
        business_data = {}

        print("   🔍 正在提取业务申报书关键信息...")

        # 提取业务编号 - 更全面的模式
        business_patterns = [
            r'业务编号[：:]\s*([A-Z0-9]+)',
            r'申请编号[：:]\s*([A-Z0-9]+)',
            r'(PIFU\d+[A-Z0-9]*)',
            r'单笔业务编号[：:]\s*([A-Z0-9]+)',
        ]

        for pattern in business_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                business_data['business_number'] = matches[0]
                print(f"      ✅ 业务编号: {matches[0]}")
                break

        # 提取贷款金额 - 更多模式
        loan_patterns = [
            r'贷款金额[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'申请金额[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'借款金额[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'本次申请[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
            r'发放[：:]?\s*([0-9,]+\.?[0-9]*)\s*万元',
        ]

        for pattern in loan_patterns:
            matches = re.findall(pattern, text)
            if matches:
                business_data['loan_amount'] = matches[0].replace(',', '')
                print(f"      ✅ 贷款金额: {matches[0]}万元")
                break

        # 提取贷款期限 - 更灵活的模式
        term_patterns = [
            r'期限[：:]?\s*(\d+)\s*个月',
            r'贷款期限[：:]?\s*(\d+)\s*个月',
            r'借款期限[：:]?\s*(\d+)\s*个月',
            r'(\d+)\s*个月',
        ]

        for pattern in term_patterns:
            matches = re.findall(pattern, text)
            if matches:
                business_data['loan_term'] = f"{matches[0]}个月"
                print(f"      ✅ 贷款期限: {matches[0]}个月")
                break

        # 提取担保方式 - 更详细
        guarantee_patterns = [
            r'担保方式[：:]?\s*([^，。！？\n]{1,100})',
            r'担保类型[：:]?\s*([^，。！？\n]{1,100})',
            r'担保措施[：:]?\s*([^，。！？\n]{1,100})',
        ]

        for pattern in guarantee_patterns:
            matches = re.findall(pattern, text)
            if matches:
                business_data['guarantee_type'] = matches[0].strip()
                print(f"      ✅ 担保方式: {matches[0].strip()}")
                break

        # 提取持续条件
        continuous_patterns = [
            r'持续条件[：:]?\s*([^。！？]*)',
            r'持续性条件[：:]?\s*([^。！？]*)',
            r'持续管理条件[：:]?\s*([^。！？]*)',
        ]

        for pattern in continuous_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                business_data['continuous_conditions'] = matches[0].strip()
                print(f"      ✅ 持续条件: {matches[0].strip()[:50]}...")
                break

        # 提取管理条件
        management_patterns = [
            r'管理条件[：:]?\s*([^。！？]*)',
            r'贷后管理[：:]?\s*([^。！？]*)',
            r'管理要求[：:]?\s*([^。！？]*)',
        ]

        for pattern in management_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                business_data['management_conditions'] = matches[0].strip()
                print(f"      ✅ 管理条件: {matches[0].strip()[:50]}...")
                break

        # 提取用信前提条件
        prerequisite_patterns = [
            r'用信前提条件[：:]?\s*([^。！？]*)',
            r'放款条件[：:]?\s*([^。！？]*)',
            r'提款条件[：:]?\s*([^。！？]*)',
        ]

        for pattern in prerequisite_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            if matches:
                business_data['prerequisite_conditions'] = matches[0].strip()
                print(f"      ✅ 用信前提条件: {matches[0].strip()[:50]}...")
                break

        return business_data
    
    def _combine_data(self, quota_data, business_data):
        """合并数据"""
        combined = {
            'quota_info': quota_data,
            'business_info': business_data,
            'extraction_time': datetime.now().isoformat()
        }
        return combined
    
    def _display_extracted_data(self, data):
        """显示提取的数据"""
        print(f"\n📊 提取结果汇总:")
        print("-" * 60)
        
        quota_info = data.get('quota_info', {})
        business_info = data.get('business_info', {})
        
        print("🏦 额度申报书信息:")
        if quota_info:
            for key, value in quota_info.items():
                print(f"   {key}: {value}")
        else:
            print("   ⚠️ 未提取到额度信息")
        
        print("\n💼 业务申报书信息:")
        if business_info:
            for key, value in business_info.items():
                print(f"   {key}: {value}")
        else:
            print("   ⚠️ 未提取到业务信息")
    
    def _analyze_database_mapping(self, data):
        """分析数据库映射"""
        print(f"\n🗄️ 数据库映射建议:")
        print("-" * 60)
        
        print("建议创建以下数据库表结构:")
        print("""
CREATE TABLE loan_applications (
    id TEXT PRIMARY KEY,
    company_id TEXT,
    quota_number TEXT,           -- 额度编号
    business_number TEXT,        -- 业务编号
    quota_amount REAL,           -- 额度金额
    quota_valid_from DATE,       -- 额度有效期开始
    quota_valid_to DATE,         -- 额度有效期结束
    loan_amount REAL,            -- 贷款金额
    loan_term TEXT,              -- 贷款期限
    guarantee_type TEXT,         -- 担保方式
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
        """)

def main():
    extractor = ApplicationDataExtractor()
    data = extractor.extract_all_data()
    return data

if __name__ == "__main__":
    main()
