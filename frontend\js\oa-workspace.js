/**
 * OA正文人机协作工作台
 * 支持与Gemini AI协同工作的智能文档生成
 */

class OAWorkspace {
    constructor() {
        this.currentCompany = null;
        this.workspaceData = null;
        this.isWorkspaceOpen = false;
        this.initializeWorkspace();
    }

    /**
     * 初始化工作台
     */
    initializeWorkspace() {
        this.createWorkspaceModal();
        this.bindEvents();
    }

    /**
     * 创建工作台模态窗口
     */
    createWorkspaceModal() {
        const modalHTML = `
            <div id="oaWorkspaceModal" class="oa-workspace-modal hidden">
                <div class="oa-workspace-overlay"></div>
                <div class="oa-workspace-container">
                    <div class="oa-workspace-header">
                        <h2>🤖 OA正文人机协作工作台</h2>
                        <button id="closeOAWorkspace" class="close-workspace-btn">&times;</button>
                    </div>
                    
                    <div class="oa-workspace-content">
                        <!-- 左侧：文档编辑区 -->
                        <div class="oa-workspace-left">
                            <div class="document-section">
                                <h3>📝 文档内容</h3>
                                <div class="document-info">
                                    <span id="oaCompanyName" class="company-name"></span>
                                    <span class="document-status">半成品文档 - 等待AI协作</span>
                                </div>
                                <textarea id="oaDocumentContent" class="oa-document-textarea" 
                                         placeholder="正在加载文档模板..."></textarea>
                            </div>
                            
                            <div class="gemini-response-section">
                                <h3>🤖 Gemini AI 回复</h3>
                                <textarea id="geminiResponseInput" class="gemini-response-textarea" 
                                         placeholder="请在此处粘贴Gemini的回复内容..."></textarea>
                                <div class="workspace-actions">
                                    <button id="completeOADocument" class="btn btn-primary" disabled>
                                        ✨ 完成并生成文档
                                    </button>
                                    <button id="resetOAWorkspace" class="btn btn-secondary">
                                        🔄 重置工作台
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：AI任务包 -->
                        <div class="oa-workspace-right">
                            <div class="task-package-section">
                                <h3>📋 Gemini 任务包</h3>
                                
                                <div class="materials-section">
                                    <h4>📊 背景材料</h4>
                                    <div id="backgroundMaterials" class="materials-content">
                                        <p>正在准备背景材料...</p>
                                    </div>
                                </div>
                                
                                <div class="prompt-section">
                                    <h4>💡 建议提示词</h4>
                                    <div class="prompt-container">
                                        <textarea id="suggestedPrompt" class="prompt-textarea" readonly></textarea>
                                        <button id="copyPrompt" class="btn btn-copy">📋 复制提示词</button>
                                    </div>
                                </div>
                                
                                <div class="instructions-section">
                                    <h4>📝 操作指南</h4>
                                    <ol id="operationInstructions" class="instructions-list">
                                        <li>正在加载操作指南...</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="oa-workspace-footer">
                        <div class="workspace-status">
                            <span id="workspaceStatus" class="status-text">工作台已就绪</span>
                        </div>
                        <div class="workspace-progress">
                            <div class="progress-steps">
                                <div class="step active" data-step="1">1. 模板加载</div>
                                <div class="step" data-step="2">2. AI协作</div>
                                <div class="step" data-step="3">3. 文档生成</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭工作台
        document.getElementById('closeOAWorkspace').addEventListener('click', () => {
            this.closeWorkspace();
        });

        // 点击遮罩关闭
        document.querySelector('.oa-workspace-overlay').addEventListener('click', () => {
            this.closeWorkspace();
        });

        // 复制提示词
        document.getElementById('copyPrompt').addEventListener('click', () => {
            this.copyPromptToClipboard();
        });

        // Gemini回复输入监听
        document.getElementById('geminiResponseInput').addEventListener('input', (e) => {
            this.onGeminiResponseChange(e.target.value);
        });

        // 完成文档生成
        document.getElementById('completeOADocument').addEventListener('click', () => {
            this.completeDocument();
        });

        // 重置工作台
        document.getElementById('resetOAWorkspace').addEventListener('click', () => {
            this.resetWorkspace();
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isWorkspaceOpen) {
                this.closeWorkspace();
            }
        });
    }

    /**
     * 打开工作台
     */
    async openWorkspace(company) {
        if (!company) {
            if (window.uiManager) {
                window.uiManager.showError('请先选择一个客户企业');
            }
            return;
        }

        this.currentCompany = company;
        
        try {
            // 显示工作台
            this.showWorkspace();
            this.updateStatus('正在准备OA文档工作台...');
            this.setStep(1);

            // 调用API准备工作台
            const response = await fetch('http://localhost:5000/api/documents/oa/prepare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: company.id
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.status === 'success') {
                this.workspaceData = result.data;
                this.populateWorkspace();
                this.updateStatus('工作台准备完成，请按照右侧指南操作');
                this.setStep(2);
                
                if (window.uiManager) {
                    window.uiManager.addLog(`OA工作台已为 ${company.company_name} 准备就绪`, 'success');
                }
            } else {
                throw new Error(result.message || '准备工作台失败');
            }

        } catch (error) {
            this.updateStatus(`工作台准备失败: ${error.message}`);
            if (window.uiManager) {
                window.uiManager.addLog(`OA工作台准备失败: ${error.message}`, 'error');
                window.uiManager.showError(`工作台准备失败: ${error.message}`);
            }
        }
    }

    /**
     * 显示工作台
     */
    showWorkspace() {
        const modal = document.getElementById('oaWorkspaceModal');
        modal.classList.remove('hidden');
        this.isWorkspaceOpen = true;
        
        // 防止背景滚动
        document.body.style.overflow = 'hidden';
    }

    /**
     * 关闭工作台
     */
    closeWorkspace() {
        const modal = document.getElementById('oaWorkspaceModal');
        modal.classList.add('hidden');
        this.isWorkspaceOpen = false;
        
        // 恢复背景滚动
        document.body.style.overflow = '';
        
        // 清理数据
        this.currentCompany = null;
        this.workspaceData = null;
    }

    /**
     * 填充工作台内容
     */
    populateWorkspace() {
        if (!this.workspaceData) return;

        // 设置公司名称
        document.getElementById('oaCompanyName').textContent = this.workspaceData.company_name;

        // 设置文档内容
        document.getElementById('oaDocumentContent').value = this.workspaceData.semi_finished_content;

        // 设置背景材料
        const materialsContainer = document.getElementById('backgroundMaterials');
        materialsContainer.innerHTML = this.workspaceData.task_package.background_materials
            .map(material => `<p>${material}</p>`)
            .join('');

        // 设置建议提示词
        document.getElementById('suggestedPrompt').value = this.workspaceData.task_package.suggested_prompt;

        // 设置操作指南
        const instructionsContainer = document.getElementById('operationInstructions');
        instructionsContainer.innerHTML = this.workspaceData.task_package.instructions
            .map(instruction => `<li>${instruction}</li>`)
            .join('');
    }

    /**
     * 复制提示词到剪贴板
     */
    async copyPromptToClipboard() {
        try {
            const promptText = document.getElementById('suggestedPrompt').value;
            await navigator.clipboard.writeText(promptText);
            
            // 显示成功提示
            const copyBtn = document.getElementById('copyPrompt');
            const originalText = copyBtn.textContent;
            copyBtn.textContent = '✅ 已复制';
            copyBtn.style.background = '#4CAF50';
            
            setTimeout(() => {
                copyBtn.textContent = originalText;
                copyBtn.style.background = '';
            }, 2000);
            
            if (window.uiManager) {
                window.uiManager.addLog('提示词已复制到剪贴板', 'success');
            }
            
        } catch (error) {
            if (window.uiManager) {
                window.uiManager.addLog('复制失败，请手动选择复制', 'error');
            }
        }
    }

    /**
     * Gemini回复变更处理
     */
    onGeminiResponseChange(value) {
        const completeBtn = document.getElementById('completeOADocument');
        const hasContent = value.trim().length > 0;
        
        completeBtn.disabled = !hasContent;
        
        if (hasContent) {
            this.updateStatus('AI回复已输入，可以生成最终文档');
            this.setStep(3);
        } else {
            this.updateStatus('等待输入Gemini AI回复...');
            this.setStep(2);
        }
    }

    /**
     * 完成文档生成
     */
    async completeDocument() {
        try {
            this.updateStatus('正在生成最终文档...');
            
            const semiFinishedContent = document.getElementById('oaDocumentContent').value;
            const geminiResponse = document.getElementById('geminiResponseInput').value;
            
            const response = await fetch('http://localhost:5000/api/documents/oa/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCompany.id,
                    semi_finished_content: semiFinishedContent,
                    gemini_response: geminiResponse
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 下载文档
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `OA正文-${this.currentCompany.company_name}.docx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.updateStatus('文档生成成功！');
            
            if (window.uiManager) {
                window.uiManager.addLog(`OA正文文档生成成功: ${this.currentCompany.company_name}`, 'success');
                window.uiManager.showSuccess('OA正文文档已生成并下载！');
            }

            // 延迟关闭工作台
            setTimeout(() => {
                this.closeWorkspace();
            }, 2000);

        } catch (error) {
            this.updateStatus(`文档生成失败: ${error.message}`);
            if (window.uiManager) {
                window.uiManager.addLog(`OA文档生成失败: ${error.message}`, 'error');
                window.uiManager.showError(`文档生成失败: ${error.message}`);
            }
        }
    }

    /**
     * 重置工作台
     */
    resetWorkspace() {
        if (this.workspaceData) {
            document.getElementById('oaDocumentContent').value = this.workspaceData.semi_finished_content;
            document.getElementById('geminiResponseInput').value = '';
            document.getElementById('completeOADocument').disabled = true;
            this.updateStatus('工作台已重置');
            this.setStep(2);
        }
    }

    /**
     * 更新状态文本
     */
    updateStatus(message) {
        document.getElementById('workspaceStatus').textContent = message;
    }

    /**
     * 设置当前步骤
     */
    setStep(stepNumber) {
        const steps = document.querySelectorAll('.progress-steps .step');
        steps.forEach((step, index) => {
            if (index + 1 <= stepNumber) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }
}

// 创建全局OA工作台实例
const oaWorkspace = new OAWorkspace();

// 导出供其他模块使用
window.OAWorkspace = OAWorkspace;
window.oaWorkspace = oaWorkspace;
