#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同放款模块 - 标准化文档生成器
基于协定存款模块的成功经验，实现标准化的字段替换和格式保持
"""

import logging
import sqlite3
import docx
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import Pt
from pathlib import Path
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StandardizedContractGenerator:
    """标准化合同放款文档生成器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "templates" / "contract_disbursement"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        # 颜色定义 - 与协定存款模块保持一致
        self.yellow_color = WD_COLOR_INDEX.YELLOW  # 黄色 - 替换内容标记
        self.green_color = WD_COLOR_INDEX.BRIGHT_GREEN  # 绿色 - 新增内容标记
        
    def generate_condition_checklist(self, company_id, loan_amount=None):
        """生成条件落实情况表"""
        logger.info(f"📋 开始生成条件落实情况表，企业ID: {company_id}")
        
        # 1. 获取企业数据
        company_data = self._get_company_data(company_id)
        if not company_data:
            raise ValueError(f"未找到企业信息: {company_id}")
        
        # 2. 加载模板
        template_path = self.templates_dir / "disbursement_condition_checklist_blueprint.docx"
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
        
        doc = docx.Document(template_path)
        
        # 3. 准备替换数据
        fill_data = self._prepare_replacement_data(company_data, loan_amount)
        
        # 4. 执行字段替换
        replacement_count = self._execute_field_replacements(doc, fill_data)
        
        # 5. 保存文件
        output_filename = f"标准化条件落实情况表_{company_data['company_name']}.docx"
        output_path = self.output_dir / output_filename
        doc.save(output_path)
        
        logger.info(f"✅ 条件落实情况表生成完成: {output_path}")
        
        return output_path, {
            'company_name': company_data['company_name'],
            'loan_amount': loan_amount or '待填写',
            'total_replacements': replacement_count,
            'template_type': 'condition_checklist'
        }
    
    def _get_company_data(self, company_id):
        """获取企业数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, unified_social_credit_code, legal_representative,
                       registered_address, contact_phone, contact_email
                FROM companies 
                WHERE id = ?
            """, (company_id,))
            
            result = cursor.fetchone()
            if not result:
                return None
            
            return {
                'company_name': result[0],
                'unified_social_credit_code': result[1],
                'legal_representative': result[2],
                'registered_address': result[3],
                'contact_phone': result[4],
                'contact_email': result[5]
            }
        except Exception as e:
            logger.error(f"获取企业数据失败: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()
    
    def _prepare_replacement_data(self, company_data, loan_amount):
        """准备替换数据 - 基于实际模板字段"""
        current_date = datetime.now()
        
        # 生成业务编号（模拟银行业务编号格式）
        business_number = f"KHED{company_data['unified_social_credit_code'][-6:]}{current_date.strftime('%Y%m%d')}"
        
        replacements = {
            # 基础企业信息
            '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
            '杨伟': company_data['legal_representative'],
            '91510100MA6CGUGA1W': company_data['unified_social_credit_code'],
            
            # 业务信息
            'PIFU5100000002025N00G8': business_number,
            'C类': 'ESG绿色',  # 环保分类
            '2025年3月': current_date.strftime('%Y年%m月'),
            
            # 金额信息（如果提供）
            '        万元': f'{loan_amount}万元' if loan_amount else '        万元',
        }
        
        logger.info("📝 准备替换数据:")
        for old_text, new_text in replacements.items():
            if old_text != new_text:  # 只显示实际会替换的内容
                logger.info(f"   {old_text} → {new_text}")
        
        return replacements
    
    def _execute_field_replacements(self, doc, fill_data):
        """执行字段替换，保持格式完整性"""
        replacement_count = 0
        
        for old_text, new_text in fill_data.items():
            if new_text and old_text != new_text:  # 只替换有值且不同的字段
                count = self._replace_text_in_document(doc, old_text, new_text)
                if count > 0:
                    replacement_count += count
                    logger.info(f"   ✅ {old_text[:20]}... → {new_text[:20]}... ({count}处)")
        
        logger.info(f"🔄 字段替换完成: 总计{replacement_count}个替换")
        return replacement_count

    def _replace_text_in_document(self, doc, old_text, new_text):
        """在文档中替换文本，保持格式完整性"""
        replacement_count = 0

        # 遍历所有段落
        for paragraph in doc.paragraphs:
            if old_text in paragraph.text:
                replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)

        # 遍历所有表格
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if old_text in paragraph.text:
                            replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)

        return replacement_count

    def _replace_text_in_paragraph(self, paragraph, old_text, new_text):
        """在段落中替换文本，保持格式完整性"""
        if old_text not in paragraph.text:
            return 0

        # 方法1：在现有的runs中查找并替换，使用标准字体
        for run in paragraph.runs:
            if old_text in run.text:
                # 保存原有字体格式
                original_format = {
                    'bold': run.font.bold,
                    'italic': run.font.italic,
                    'underline': run.font.underline
                }

                # 替换文本并标记颜色
                run.text = run.text.replace(old_text, new_text)
                run.font.highlight_color = self.yellow_color

                # 强制使用系统标准字体避免渲染问题
                try:
                    run.font.name = '宋体'  # 使用系统标准字体
                    run.font.size = Pt(14)  # 明确设置14pt
                    if original_format['bold'] is not None:
                        run.font.bold = original_format['bold']
                    if original_format['italic'] is not None:
                        run.font.italic = original_format['italic']
                    if original_format['underline'] is not None:
                        run.font.underline = original_format['underline']
                except Exception as e:
                    logger.warning(f"字体格式设置失败: {e}")

                return 1

        return 0


def main():
    """测试函数"""
    print("📋 标准化合同放款文档生成器测试")
    print("="*50)

    generator = StandardizedContractGenerator()

    # 测试中科卓尔
    company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    loan_amount = 1300  # 1300万元

    try:
        output_path, summary = generator.generate_condition_checklist(company_id, loan_amount)

        print(f"✅ 生成成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"💰 放款金额: {summary['loan_amount']}万元")
        print(f"📊 替换字段: {summary['total_replacements']}个")
        print(f"🟡 替换内容已标记为黄色")
        print(f"🎯 模板类型: {summary['template_type']}")

        print(f"\n🔍 特点:")
        print(f"   ✅ 基于协定存款模块的成功经验")
        print(f"   ✅ 标准化字段替换逻辑")
        print(f"   ✅ 完整的格式保持")
        print(f"   ✅ 黄色高亮标记替换内容")
        print(f"   ✅ 可复用到其他模块")

    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
