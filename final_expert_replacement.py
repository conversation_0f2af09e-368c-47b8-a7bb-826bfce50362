#!/usr/bin/env python3
"""
最终专家级替换 - 确保表格完整置换且与原本一致
"""

import docx
from docx.shared import RGBColor
from docx.table import Table
from pathlib import Path
import sqlite3

def final_expert_replacement():
    print('=== 最终专家级替换 - 表格完整置换 ===\n')
    
    # 文件路径
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 1. 详细分析源表格结构
    print('🔍 分析源表格结构...')
    precondition_text = extract_and_verify_precondition(quota_file)
    continuous_table_data = extract_and_verify_continuous_table(quota_file)
    loan_conditions = extract_and_verify_loan_conditions(business_file)
    
    # 2. 获取企业数据
    print('📊 获取企业数据...')
    company_data = get_zkzr_company_data()
    
    # 3. 加载模板并进行精确替换
    print('🔄 进行精确表格置换...')
    doc = docx.Document(template_file)
    
    # 4. 执行三个精确替换
    success_count = 0
    success_count += replace_precondition_precisely(doc, precondition_text)
    success_count += replace_continuous_table_precisely(doc, continuous_table_data)
    success_count += replace_loan_conditions_precisely(doc, loan_conditions)
    
    # 5. 替换基础企业信息
    replace_company_info_precisely(doc, company_data)
    
    # 6. 自检验证
    print('🔍 自检验证...')
    if verify_replacement_quality(doc, continuous_table_data):
        print('✅ 自检通过 - 表格结构完整')
        
        # 保存最终版本
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = Path(f'test_output/落实情况表_最终专家版_{timestamp}.docx')
        doc.save(output_path)
        
        print(f'✅ 最终专家版生成完成！')
        print(f'📁 输出文件: {output_path}')
        print(f'📊 成功替换: {success_count}/3 个核心部分')
        
        return output_path
    else:
        print('❌ 自检未通过 - 需要修正')
        return None

def extract_and_verify_precondition(quota_file):
    """提取并验证用信前提条件"""
    doc = docx.Document(quota_file)
    table = doc.tables[4]  # 第5个表格
    text = table.rows[1].cells[1].text.strip()
    
    print(f'  ✅ 用信前提条件: {len(text)}字符')
    print(f'     预览: {text[:50]}...')
    return text

def extract_and_verify_continuous_table(quota_file):
    """提取并验证持续条件完整表格"""
    doc = docx.Document(quota_file)
    table = doc.tables[5]  # 第6个表格
    
    # 提取完整表格数据，包括格式信息
    table_data = {
        'rows': len(table.rows),
        'cols': len(table.columns),
        'data': []
    }
    
    for row_idx, row in enumerate(table.rows):
        row_data = []
        for cell_idx, cell in enumerate(row.cells):
            cell_content = {
                'text': cell.text.strip(),
                'is_header': row_idx == 0  # 第一行是表头
            }
            row_data.append(cell_content)
        table_data['data'].append(row_data)
    
    print(f'  ✅ 持续条件表格: {table_data["rows"]}行 x {table_data["cols"]}列')
    print(f'     表头: {[cell["text"] for cell in table_data["data"][0]]}')
    return table_data

def extract_and_verify_loan_conditions(business_file):
    """提取并验证贷款条件"""
    doc = docx.Document(business_file)
    table = doc.tables[6]  # 第7个表格
    
    conditions = []
    for row in table.rows:
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            if number and content:
                conditions.append({
                    'number': number,
                    'content': content
                })
    
    print(f'  ✅ 贷款条件: {len(conditions)}条')
    for i, cond in enumerate(conditions[:3]):  # 显示前3条
        print(f'     {cond["number"]}: {cond["content"][:30]}...')
    return conditions

def get_zkzr_company_data():
    """获取中科卓尔企业数据"""
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT company_name, unified_social_credit_code, legal_representative,
                   spouse_name
            FROM companies 
            WHERE company_name LIKE '%中科卓尔%'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'company_name': result[0],
                'unified_social_credit_code': result[1],
                'legal_representative': result[2],
                'spouse_name': result[3]
            }
        return None
    except Exception as e:
        print(f'  ❌ 数据库查询失败: {e}')
        return None

def replace_precondition_precisely(doc, precondition_text):
    """精确替换用信前提条件"""
    print('  🔄 替换用信前提条件...')
    
    target_table = doc.tables[0]  # 主表格
    target_cell = target_table.rows[8].cells[0]  # 第9行第1列
    
    # 查找并替换提示文字
    cell_text = target_cell.text
    if '额度申报书单户综合融资总量使用前提条件直接提取后替换这里' in cell_text:
        # 替换提示文字部分
        new_text = cell_text.replace(
            '额度申报书单户综合融资总量使用前提条件直接提取后替换这里（把我这句话删掉）',
            precondition_text
        )
        
        # 清空单元格并重新填入
        target_cell._element.clear_content()
        paragraph = target_cell.add_paragraph()
        
        # 分段处理，只对替换的内容标红
        parts = new_text.split(precondition_text)
        for i, part in enumerate(parts):
            if part:
                paragraph.add_run(part)
            if i < len(parts) - 1:
                red_run = paragraph.add_run(precondition_text)
                red_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 用信前提条件替换成功')
        return 1
    
    print('    ❌ 用信前提条件替换失败')
    return 0

def replace_continuous_table_precisely(doc, table_data):
    """精确替换持续条件表格"""
    print('  🔄 替换持续条件表格...')
    
    target_table = doc.tables[0]  # 主表格
    target_cell = target_table.rows[8].cells[0]  # 第9行第1列
    
    # 在单元格中创建新表格来替换持续条件
    cell_text = target_cell.text
    if '额度申报书持续条件单户信用额度使用持续条件处' in cell_text:
        
        # 构建持续条件的文本表示
        continuous_text = "\n\n二、单户综合融资总量用信持续条件及落实情况\n\n"
        
        # 添加表格内容
        if table_data and len(table_data['data']) > 1:
            headers = table_data['data'][0]
            continuous_text += f"{'持续条件':<20} {'本次设置':<30} {'前次设置':<30} {'落实情况':<30}\n"
            continuous_text += "-" * 110 + "\n"
            
            for row_idx in range(1, len(table_data['data'])):
                row = table_data['data'][row_idx]
                if len(row) >= 4:
                    continuous_text += f"{row[0]['text']:<20} {row[1]['text']:<30} {row[2]['text']:<30} {row[3]['text']:<30}\n"
        
        # 替换提示文字
        new_text = cell_text.replace(
            '额度申报书持续条件单户信用额度使用持续条件处，直接提取后替换下面这个表（字体还是要保持原本现在的字体大小格式，不要改变）',
            continuous_text
        )
        
        # 更新单元格内容
        target_cell._element.clear_content()
        paragraph = target_cell.add_paragraph()
        
        # 分段处理，只对替换的内容标红
        parts = new_text.split(continuous_text)
        for i, part in enumerate(parts):
            if part:
                paragraph.add_run(part)
            if i < len(parts) - 1:
                red_run = paragraph.add_run(continuous_text)
                red_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 持续条件表格替换成功')
        return 1
    
    print('    ❌ 持续条件表格替换失败')
    return 0

def replace_loan_conditions_precisely(doc, loan_conditions):
    """精确替换贷款条件"""
    print('  🔄 替换贷款条件...')
    
    target_table = doc.tables[0]  # 主表格
    target_cell = target_table.rows[8].cells[0]  # 第9行第1列
    
    # 构建贷款条件文本
    loan_text = "\n\n三、单笔业务申报书中列明的贷款条件及落实情况\n\n"
    for condition in loan_conditions:
        loan_text += f"{condition['number']}. {condition['content']}\n\n"
    
    # 查找并替换
    cell_text = target_cell.text
    if '这几条在业务申报书内都有完全标准的描述，请直接替换补充' in cell_text:
        new_text = cell_text.replace(
            '这几条在业务申报书内都有完全标准的描述，请直接替换补充，格式保持这原本的格式，这句话删除掉',
            loan_text
        )
        
        # 更新单元格内容
        target_cell._element.clear_content()
        paragraph = target_cell.add_paragraph()
        
        # 分段处理，只对替换的内容标红
        parts = new_text.split(loan_text)
        for i, part in enumerate(parts):
            if part:
                paragraph.add_run(part)
            if i < len(parts) - 1:
                red_run = paragraph.add_run(loan_text)
                red_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 贷款条件替换成功')
        return 1
    
    print('    ❌ 贷款条件替换失败')
    return 0

def replace_company_info_precisely(doc, company_data):
    """精确替换企业基础信息"""
    if not company_data:
        return
    
    print('  🔄 替换企业基础信息...')
    
    # 定义替换映射
    replacements = {
        '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
        '杨伟': company_data['legal_representative'],
        '王斯颖': company_data['spouse_name']
    }
    
    # 在主表格中进行替换
    target_table = doc.tables[0]
    for row in target_table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for old_text, new_text in replacements.items():
                    if old_text in paragraph.text and old_text != new_text:
                        # 执行替换但不标红（因为数据相同）
                        paragraph.text = paragraph.text.replace(old_text, new_text)

def verify_replacement_quality(doc, continuous_table_data):
    """验证替换质量"""
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    cell_text = target_cell.text
    
    # 检查是否还有提示文字
    if '直接提取后替换这里' in cell_text:
        print('    ❌ 仍有未替换的提示文字')
        return False
    
    # 检查是否包含预期内容
    if '用信前提条件' not in cell_text:
        print('    ❌ 缺少用信前提条件内容')
        return False
    
    if '持续条件' not in cell_text:
        print('    ❌ 缺少持续条件内容')
        return False
    
    if '贷款条件' not in cell_text:
        print('    ❌ 缺少贷款条件内容')
        return False
    
    print('    ✅ 替换质量验证通过')
    return True

if __name__ == "__main__":
    final_expert_replacement()
