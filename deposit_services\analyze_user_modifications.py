#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析用户修改的协定存款协议文档
提取用户的具体需求和修改内容
"""

import docx
from pathlib import Path
import re

def analyze_user_modifications():
    """分析用户修改的文档内容"""
    project_root = Path(__file__).parent.parent
    user_modified_file = project_root / "test_output" / "协定存款协议_成都中科卓尔智能科技集团有限公司.docx"
    
    print("🔍 分析用户修改的协定存款协议")
    print("="*60)
    
    if not user_modified_file.exists():
        print(f"❌ 用户修改文件不存在: {user_modified_file}")
        return
    
    try:
        doc = docx.Document(user_modified_file)
        
        # 提取完整文档内容
        full_text = ""
        for paragraph in doc.paragraphs:
            full_text += paragraph.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    full_text += cell.text + " "
                full_text += "\n"
        
        print(f"📄 文档总字符数: {len(full_text)}")
        
        # 分析用户的具体修改和需求
        analyze_specific_modifications(full_text)
        
        # 分析字段填充情况
        analyze_field_completion(full_text)
        
        # 分析用户可能的需求模式
        analyze_user_requirements(full_text)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def analyze_specific_modifications(text):
    """分析具体的修改内容"""
    print(f"\n📝 具体修改内容分析:")
    print("-" * 50)
    
    # 查找关键信息
    key_info = {}
    
    # 1. 企业信息
    company_patterns = [
        r'甲方[：:]?\s*([^\n\r，。；]{5,50})',
        r'借款人[：:]?\s*([^\n\r，。；]{5,50})',
        r'存款人[：:]?\s*([^\n\r，。；]{5,50})'
    ]
    
    for pattern in company_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if matches:
            key_info['企业名称'] = matches[0].strip()
            break
    
    # 2. 账户信息
    account_patterns = [
        r'账户?[：:]?\s*(\d{10,20})',
        r'账号[：:]?\s*(\d{10,20})',
        r'开立.*账户.*[：:]?\s*(\d{10,20})'
    ]
    
    accounts = []
    for pattern in account_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        accounts.extend(matches)
    
    if accounts:
        key_info['账户信息'] = list(set(accounts))  # 去重
    
    # 3. 金额信息
    amount_patterns = [
        r'([壹贰叁肆伍陆柒捌玖拾佰仟万亿]+)万?元?',
        r'(\d+)万元',
        r'人民币\s*([^\n\r，。；]{2,20})'
    ]
    
    amounts = []
    for pattern in amount_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        amounts.extend(matches)
    
    if amounts:
        key_info['金额信息'] = list(set(amounts))
    
    # 4. 日期信息
    date_patterns = [
        r'(\d{4}年\d{1,2}月\d{1,2}日)',
        r'(\d{4}-\d{1,2}-\d{1,2})',
        r'(二〇\d{2}年\d{1,2}月\d{1,2}日)'
    ]
    
    dates = []
    for pattern in date_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        dates.extend(matches)
    
    if dates:
        key_info['日期信息'] = list(set(dates))
    
    # 5. 利率信息
    rate_patterns = [
        r'利率[^。]*?(\d+\.?\d*%)',
        r'年利率[^。]*?(\d+\.?\d*%)',
        r'(加|减)\s*(\d+)\s*bps',
        r'基准利率[^。]*?([^。]{5,20})'
    ]
    
    rates = []
    for pattern in rate_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        if isinstance(matches[0], tuple) if matches else False:
            rates.extend([' '.join(match) for match in matches])
        else:
            rates.extend(matches)
    
    if rates:
        key_info['利率信息'] = list(set(rates))
    
    # 显示提取的关键信息
    print("📊 提取的关键信息:")
    for category, info in key_info.items():
        print(f"   🔹 {category}: {info}")
    
    return key_info

def analyze_field_completion(text):
    """分析字段填充完成情况"""
    print(f"\n📋 字段填充完成情况:")
    print("-" * 50)
    
    # 检查未填充的占位符
    placeholders = {
        '待补充': text.count('待补充'),
        '年    月    日': text.count('年    月    日'),
        '【】': text.count('【】'),
        '____': text.count('____'),
        '空白万元': text.count('            万'),
        '空白账号': text.count('账号：待补充')
    }
    
    filled_fields = {
        '企业名称': '成都中科卓尔智能科技集团有限公司' in text,
        '具体日期': '2025年7月31日' in text,
        '中文金额': any(word in text for word in ['壹仟', '贰仟', '叁仟', '伍仟', '壹万']),
        '通知期限': '【5】' in text or '【3】' in text or '【7】' in text,
    }
    
    print("❌ 仍需填充的占位符:")
    for placeholder, count in placeholders.items():
        if count > 0:
            print(f"   - {placeholder}: {count}处")
    
    print("\n✅ 已填充的字段:")
    for field, is_filled in filled_fields.items():
        status = "✅" if is_filled else "❌"
        print(f"   {status} {field}: {'已填充' if is_filled else '未填充'}")

def analyze_user_requirements(text):
    """分析用户需求模式"""
    print(f"\n💡 用户需求分析:")
    print("-" * 50)
    
    requirements = []
    
    # 1. 检查是否有特定的账户号码格式
    if re.search(r'51001\d{12}', text):
        requirements.append("需要生成建设银行格式的账户号码")
    
    # 2. 检查金额格式偏好
    if '壹仟万' in text:
        requirements.append("金额需要转换为中文大写")
    elif re.search(r'\d+万元', text):
        requirements.append("金额使用阿拉伯数字格式")
    
    # 3. 检查日期格式偏好
    if '2025年7月31日' in text:
        requirements.append("日期使用完整的中文格式")
    
    # 4. 检查是否保留了某些空白字段
    if '年    月    日' in text:
        requirements.append("某些日期字段需要保持空白供手动填写")
    
    # 5. 检查利率相关内容
    if 'bps' in text.lower():
        requirements.append("利率调整使用基点(bps)表示")
    
    # 6. 检查通知期限
    if '【5】' in text:
        requirements.append("通知期限默认设置为5个工作日")
    
    # 7. 检查合同期限处理
    if '至    年    月    日止' in text:
        requirements.append("合同结束日期需要保持空白")
    
    print("🎯 识别的用户需求:")
    for i, req in enumerate(requirements, 1):
        print(f"   {i}. {req}")
    
    if not requirements:
        print("   📝 未识别到特定需求模式，建议进一步分析")
    
    return requirements

def generate_requirements_summary():
    """生成需求总结"""
    print(f"\n📋 需求总结:")
    print("="*60)
    
    print("基于文档分析，用户的主要需求包括：")
    print()
    print("1. **字段自动填充**:")
    print("   - 企业名称自动填充")
    print("   - 账户号码自动生成（建设银行格式）")
    print("   - 金额转换为中文大写")
    print("   - 当前日期自动填充")
    print()
    print("2. **格式保持**:")
    print("   - 保持原有文档格式和字体")
    print("   - 替换内容标记黄色便于识别")
    print()
    print("3. **选择性填充**:")
    print("   - 某些日期字段保持空白供手动填写")
    print("   - 合同结束日期不自动填充")
    print()
    print("4. **业务规则**:")
    print("   - 利率调整使用基点表示")
    print("   - 通知期限有默认值")
    print("   - 账户信息符合银行规范")

def main():
    """主函数"""
    analyze_user_modifications()
    generate_requirements_summary()

if __name__ == "__main__":
    main()
