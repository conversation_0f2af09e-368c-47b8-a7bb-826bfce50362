#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档比对分析器
分析修改完成的 2.docx 与原始模板的差异，提取标黄内容并生成字段替换清单
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from pathlib import Path
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentComparisonAnalyzer:
    """文档比对分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
        self.modified_path = self.project_root / "templates" / "contract_disbursement" / "2.docx"
        self.quota_doc_path = self.project_root / "templates" / "contract_disbursement" / "额度申报书.docx"
        self.business_doc_path = self.project_root / "templates" / "contract_disbursement" / "业务申报书.docx"
        
        # 存储分析结果
        self.field_replacements = []
        
    def analyze_documents(self):
        """分析文档差异"""
        logger.info("🔍 开始分析文档差异...")
        logger.info("="*70)
        
        # 1. 验证文件存在
        if not self._verify_files():
            return None
        
        # 2. 加载文档
        template_doc = docx.Document(self.template_path)
        modified_doc = docx.Document(self.modified_path)
        quota_doc = docx.Document(self.quota_doc_path)
        business_doc = docx.Document(self.business_doc_path)
        
        # 3. 提取修改文档中的标黄内容
        highlighted_content = self._extract_highlighted_content(modified_doc)
        
        # 4. 分析每个标黄内容的来源
        for content_info in highlighted_content:
            source_info = self._find_content_source(content_info, quota_doc, business_doc)
            if source_info:
                self.field_replacements.append({
                    **content_info,
                    **source_info
                })
        
        # 5. 生成结构化清单
        self._generate_replacement_list()
        
        return self.field_replacements
    
    def _verify_files(self):
        """验证所需文件是否存在"""
        logger.info("📁 验证文件存在性...")
        
        files_to_check = [
            ("原始模板", self.template_path),
            ("修改文件", self.modified_path),
            ("额度申报书", self.quota_doc_path),
            ("业务申报书", self.business_doc_path)
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                logger.info(f"   ✅ {name}: {path.name}")
            else:
                logger.error(f"   ❌ {name}不存在: {path}")
                all_exist = False
        
        return all_exist
    
    def _extract_highlighted_content(self, doc):
        """提取修改文档中的标黄内容"""
        logger.info("🟡 提取标黄内容...")
        
        highlighted_content = []
        
        # 检查段落中的标黄内容
        for para_idx, paragraph in enumerate(doc.paragraphs):
            for run_idx, run in enumerate(paragraph.runs):
                if self._is_highlighted(run):
                    content_info = {
                        'type': 'paragraph',
                        'location': f'段落{para_idx}',
                        'content': run.text.strip(),
                        'position_info': f'段落{para_idx}-run{run_idx}',
                        'context': paragraph.text.strip()
                    }
                    highlighted_content.append(content_info)
                    logger.info(f"   🟡 段落标黄: {run.text.strip()[:50]}...")
        
        # 检查表格中的标黄内容
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        for run_idx, run in enumerate(paragraph.runs):
                            if self._is_highlighted(run):
                                content_info = {
                                    'type': 'table_cell',
                                    'location': f'表格{table_idx}-行{row_idx}-列{cell_idx}',
                                    'content': run.text.strip(),
                                    'position_info': f'表格{table_idx}-行{row_idx}-列{cell_idx}-段落{para_idx}-run{run_idx}',
                                    'context': cell.text.strip()
                                }
                                highlighted_content.append(content_info)
                                logger.info(f"   🟡 表格标黄: {run.text.strip()[:50]}...")
        
        logger.info(f"   📊 总计找到 {len(highlighted_content)} 个标黄内容")
        return highlighted_content
    
    def _is_highlighted(self, run):
        """判断文本是否被标黄"""
        try:
            if hasattr(run.font, 'highlight_color'):
                return run.font.highlight_color == WD_COLOR_INDEX.YELLOW
            return False
        except:
            return False
    
    def _find_content_source(self, content_info, quota_doc, business_doc):
        """查找内容来源"""
        content = content_info['content']
        
        if not content or len(content) < 5:
            return None
        
        logger.info(f"🔍 查找内容来源: {content[:30]}...")
        
        # 在额度申报书中查找
        quota_source = self._search_in_document(content, quota_doc, "额度申报书.docx")
        if quota_source:
            logger.info(f"   ✅ 在额度申报书中找到来源")
            return quota_source
        
        # 在业务申报书中查找
        business_source = self._search_in_document(content, business_doc, "业务申报书.docx")
        if business_source:
            logger.info(f"   ✅ 在业务申报书中找到来源")
            return business_source
        
        # 检查是否为公司基础信息
        if self._is_company_basic_info(content):
            logger.info(f"   ✅ 识别为公司基础信息")
            return {
                'source_document': '数据库',
                'source_location': self._get_database_field(content),
                'source_type': '数据库字段',
                'extraction_method': '数据库查询'
            }
        
        logger.warning(f"   ⚠️ 未找到内容来源")
        return None
    
    def _search_in_document(self, target_content, doc, doc_name):
        """在文档中搜索内容"""
        # 清理目标内容
        target_clean = self._clean_text(target_content)
        
        # 在段落中搜索
        for para_idx, paragraph in enumerate(doc.paragraphs):
            para_clean = self._clean_text(paragraph.text)
            if target_clean in para_clean or self._similarity_match(target_clean, para_clean):
                # 查找所属标题
                title = self._find_preceding_title(doc.paragraphs, para_idx)
                return {
                    'source_document': doc_name,
                    'source_location': title or f'段落{para_idx}',
                    'source_type': '段落',
                    'extraction_method': '文本匹配'
                }
        
        # 在表格中搜索
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    cell_clean = self._clean_text(cell.text)
                    if target_clean in cell_clean or self._similarity_match(target_clean, cell_clean):
                        # 查找表格标题
                        table_title = self._find_table_title(doc, table_idx)
                        return {
                            'source_document': doc_name,
                            'source_location': table_title or f'表格{table_idx}',
                            'source_type': '表格',
                            'extraction_method': '表格匹配'
                        }
        
        return None
    
    def _clean_text(self, text):
        """清理文本用于比较"""
        if not text:
            return ""
        # 移除空白字符、标点符号等
        cleaned = re.sub(r'[\s\n\r\t\u3000]+', '', text)
        cleaned = re.sub(r'[，。、；：！？""''（）【】《》〈〉]', '', cleaned)
        return cleaned.lower()
    
    def _similarity_match(self, text1, text2, threshold=0.8):
        """简单的相似度匹配"""
        if not text1 or not text2:
            return False
        
        # 如果一个文本包含在另一个中，且长度合理
        if len(text1) > 10 and len(text2) > 10:
            if text1 in text2 or text2 in text1:
                return True
        
        return False
    
    def _find_preceding_title(self, paragraphs, current_idx):
        """查找前面的标题"""
        for i in range(current_idx - 1, -1, -1):
            para_text = paragraphs[i].text.strip()
            if self._is_title(para_text):
                return para_text
        return None
    
    def _find_table_title(self, doc, table_idx):
        """查找表格标题"""
        # 查找表格前面的段落作为标题
        table_position = self._get_table_position(doc, table_idx)
        
        for para_idx, paragraph in enumerate(doc.paragraphs):
            if para_idx < table_position:
                para_text = paragraph.text.strip()
                if self._is_title(para_text):
                    return para_text
        
        return None
    
    def _get_table_position(self, doc, table_idx):
        """获取表格在文档中的大致位置"""
        # 简化实现：返回一个估计位置
        return table_idx * 10
    
    def _is_title(self, text):
        """判断是否为标题"""
        if not text or len(text) > 50:
            return False
        
        # 标题模式
        title_patterns = [
            r'^[一二三四五六七八九十]+[、.]',
            r'^[1-9]\d*[、.]',
            r'^第[一二三四五六七八九十]+[章节部分条]',
            r'.*[条件|措施|分析|审查|说明]$'
        ]
        
        for pattern in title_patterns:
            if re.match(pattern, text):
                return True
        
        return False
    
    def _is_company_basic_info(self, content):
        """判断是否为公司基础信息"""
        company_patterns = [
            r'成都.*科技.*有限公司',
            r'杨伟',
            r'91\d{15}[0-9X]',
            r'\d+万元',
            r'20\d{2}年\d{1,2}月\d{1,2}日'
        ]
        
        for pattern in company_patterns:
            if re.search(pattern, content):
                return True
        
        return False
    
    def _get_database_field(self, content):
        """获取数据库字段名"""
        if '科技' in content and '有限公司' in content:
            return 'companies.company_name'
        elif content == '杨伟':
            return 'companies.legal_representative'
        elif re.match(r'91\d{15}[0-9X]', content):
            return 'companies.unified_social_credit_code'
        elif '万元' in content:
            return 'companies.registered_capital'
        elif re.match(r'20\d{2}年\d{1,2}月\d{1,2}日', content):
            return '系统生成日期'
        else:
            return 'companies.unknown_field'

    def _generate_replacement_list(self):
        """生成结构化字段替换清单"""
        logger.info("📋 生成结构化字段替换清单...")

        print("\n" + "="*80)
        print("📝 字段替换清单")
        print("="*80)

        # 表格标题
        print(f"{'字段名称':<20} {'模板锚点或插入位置':<25} {'来源文档':<15} {'来源位置说明':<20} {'类型':<10} {'格式要求':<15} {'示例内容预览':<30}")
        print("-" * 135)

        # 按位置排序
        sorted_replacements = sorted(self.field_replacements, key=lambda x: x.get('location', ''))

        for idx, replacement in enumerate(sorted_replacements, 1):
            field_name = self._generate_field_name(replacement)
            anchor_point = replacement.get('location', '未知位置')
            source_doc = replacement.get('source_document', '未知')
            source_location = replacement.get('source_location', '未知位置')
            content_type = replacement.get('source_type', replacement.get('type', '文本'))
            format_req = self._get_format_requirements(replacement)
            preview = replacement.get('content', '')[:30] + '...' if len(replacement.get('content', '')) > 30 else replacement.get('content', '')

            print(f"{field_name:<20} {anchor_point:<25} {source_doc:<15} {source_location:<20} {content_type:<10} {format_req:<15} {preview:<30}")

        print("-" * 135)
        print(f"总计: {len(sorted_replacements)} 个字段")

        # 详细分段说明
        print("\n" + "="*80)
        print("📊 详细分段说明")
        print("="*80)

        for idx, replacement in enumerate(sorted_replacements, 1):
            print(f"\n{idx}. {self._generate_field_name(replacement)}")
            print(f"   替换位置: {replacement.get('location', '未知位置')}")
            print(f"   替换内容: {replacement.get('content', '')[:100]}...")
            print(f"   内容来源: {replacement.get('source_document', '未知')} - {replacement.get('source_location', '未知位置')}")
            print(f"   内容类型: {replacement.get('source_type', replacement.get('type', '文本'))}")
            print(f"   替换方式: {self._get_replacement_method(replacement)}")

    def _generate_field_name(self, replacement):
        """生成字段名称"""
        content = replacement.get('content', '')

        if '科技' in content and '有限公司' in content:
            return '公司名称'
        elif content == '杨伟':
            return '法定代表人'
        elif re.match(r'91\d{15}[0-9X]', content):
            return '统一社会信用代码'
        elif '万元' in content:
            return '注册资本'
        elif '年' in content and '月' in content and '日' in content:
            return '日期字段'
        elif '用信' in content or '前提' in content:
            return '用信前提条件'
        elif '担保' in content or '保证' in content:
            return '担保措施'
        elif '还款' in content or '来源' in content:
            return '还款来源'
        elif '贷款用途' in content or '资金用途' in content:
            return '贷款用途'
        elif '持续条件' in content:
            return '持续条件'
        else:
            return f'字段{len(self.field_replacements)}'

    def _get_format_requirements(self, replacement):
        """获取格式要求"""
        content_type = replacement.get('source_type', replacement.get('type', ''))

        if content_type == '表格':
            return '保留表格格式'
        elif content_type == '段落':
            return '宋体12pt+黄标'
        elif replacement.get('source_document') == '数据库':
            return '宋体12pt'
        else:
            return '原格式+黄标'

    def _get_replacement_method(self, replacement):
        """获取替换方式建议"""
        content_type = replacement.get('source_type', replacement.get('type', ''))

        if content_type == '表格':
            return '表格整体插入，保留边框格式'
        elif content_type == '段落':
            return '段落替换，应用黄色高亮'
        elif replacement.get('source_document') == '数据库':
            return '直接文本替换'
        else:
            return '内容替换，保持原格式'


def main():
    """主函数"""
    print("🔍 文档比对分析器")
    print("="*70)

    analyzer = DocumentComparisonAnalyzer()

    try:
        results = analyzer.analyze_documents()

        if results:
            print(f"\n✅ 分析完成!")
            print(f"📊 找到 {len(results)} 个字段替换项")
            print(f"\n💡 后续可根据此清单进行自动替换和批量插入的开发工作")
        else:
            print("❌ 分析失败，请检查文件路径和内容")

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
