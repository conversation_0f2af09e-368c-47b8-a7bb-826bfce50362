#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式信贷业务申请书生成器
支持用户选择企业和输入贷款信息
"""

import sqlite3
from pathlib import Path
from credit_application_generator import CreditApplicationGenerator

def get_available_companies():
    """获取可用的企业列表"""
    db_path = Path(__file__).parent / "database" / "enterprise_service.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, company_name, unified_social_credit_code 
            FROM companies 
            WHERE business_status = 'active'
            ORDER BY company_name
        """)
        
        companies = cursor.fetchall()
        conn.close()
        return companies
        
    except Exception as e:
        print(f"❌ 获取企业列表失败: {e}")
        return []

def select_company():
    """让用户选择企业"""
    companies = get_available_companies()
    
    if not companies:
        print("❌ 没有找到可用的企业数据")
        return None
    
    print("\n📋 可用企业列表:")
    print("-" * 60)
    for i, (company_id, name, code) in enumerate(companies, 1):
        print(f"{i}. {name}")
        print(f"   统一社会信用代码: {code}")
        print(f"   ID: {company_id}")
        print()
    
    while True:
        try:
            choice = input(f"请选择企业 (1-{len(companies)}): ").strip()
            if not choice:
                continue
                
            index = int(choice) - 1
            if 0 <= index < len(companies):
                selected = companies[index]
                print(f"✅ 已选择: {selected[1]}")
                return selected[0]  # 返回company_id
            else:
                print(f"❌ 请输入 1-{len(companies)} 之间的数字")
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            return None

def get_loan_info():
    """获取贷款信息"""
    print("\n💰 请输入贷款信息:")
    print("-" * 40)
    
    # 贷款类型选择
    loan_types = [
        "流动资金贷款",
        "固定资产贷款", 
        "项目贷款",
        "信用贷款",
        "抵押贷款",
        "质押贷款"
    ]
    
    print("贷款类型选项:")
    for i, loan_type in enumerate(loan_types, 1):
        print(f"  {i}. {loan_type}")
    
    while True:
        try:
            choice = input(f"请选择贷款类型 (1-{len(loan_types)}): ").strip()
            if choice:
                index = int(choice) - 1
                if 0 <= index < len(loan_types):
                    selected_type = loan_types[index]
                    break
                else:
                    print(f"❌ 请输入 1-{len(loan_types)} 之间的数字")
            else:
                print("❌ 贷款类型不能为空")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 贷款金额
    while True:
        amount_text = input("请输入贷款金额大写 (如: 人民币壹仟万元整): ").strip()
        if amount_text:
            break
        print("❌ 贷款金额不能为空")
    
    # 贷款期限
    while True:
        term = input("请输入贷款期限 (如: 12个月, 2年): ").strip()
        if term:
            break
        print("❌ 贷款期限不能为空")
    
    # 贷款用途
    while True:
        purpose = input("请输入贷款用途: ").strip()
        if purpose:
            break
        print("❌ 贷款用途不能为空")
    
    return {
        'loan_type': selected_type,
        'loan_amount_text': amount_text,
        'loan_term': term,
        'loan_purpose': purpose
    }

def confirm_generation(company_name, loan_info):
    """确认生成信息"""
    print("\n📋 生成信息确认:")
    print("=" * 50)
    print(f"企业名称: {company_name}")
    print(f"贷款类型: {loan_info['loan_type']}")
    print(f"贷款金额: {loan_info['loan_amount_text']}")
    print(f"贷款期限: {loan_info['loan_term']}")
    print(f"贷款用途: {loan_info['loan_purpose']}")
    print("=" * 50)
    
    while True:
        confirm = input("确认生成申请书? (y/n): ").strip().lower()
        if confirm in ['y', 'yes', '是', '确认']:
            return True
        elif confirm in ['n', 'no', '否', '取消']:
            return False
        else:
            print("❌ 请输入 y/n")

def main():
    """主函数"""
    print("🏦 信贷业务申请书生成器")
    print("=" * 50)
    print("功能说明:")
    print("✅ 自动填充: 企业基本信息、财务信息、申请日期")
    print("📝 手动输入: 贷款类型、金额、期限、用途")
    print("🎨 颜色标记: 绿色=自动填充, 黄色=手动输入")
    print("=" * 50)
    
    try:
        # 1. 选择企业
        company_id = select_company()
        if not company_id:
            return
        
        # 获取企业名称用于确认
        companies = get_available_companies()
        company_name = next((name for cid, name, _ in companies if cid == company_id), "未知企业")
        
        # 2. 输入贷款信息
        loan_info = get_loan_info()
        
        # 3. 确认生成
        if not confirm_generation(company_name, loan_info):
            print("👋 用户取消生成")
            return
        
        # 4. 生成申请书
        print("\n🔄 正在生成信贷业务申请书...")
        generator = CreditApplicationGenerator()
        output_path, summary = generator.generate_application(company_id, loan_info)
        
        # 5. 显示结果
        print("\n🎉 生成成功!")
        print("=" * 50)
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"📊 自动填充: {summary['auto_filled_fields']} 个字段")
        print(f"📈 完成率: {summary['completion_rate']}")
        print("=" * 50)
        print("💡 提示:")
        print("   - 绿色高亮: 自动从数据库填充的字段")
        print("   - 黄色高亮: 根据您输入填充的字段")
        print("   - 请在Excel中检查并确认所有信息无误")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
