# 企业信息核心库 - 执行指南

## 快速开始

### 前置条件
- PostgreSQL 数据库 (版本 12 或更高)
- 具有创建表和插入数据权限的数据库用户
- 数据库客户端工具 (如 pgAdmin, DBeaver, 或 psql 命令行)

### 执行步骤

#### 第一步：创建数据库表结构
```sql
-- 在PostgreSQL中执行以下文件
\i 01_create_tables.sql
```
或者直接复制 `01_create_tables.sql` 文件内容到数据库客户端执行。

#### 第二步：插入初始数据
```sql
-- 执行初始数据插入
\i 02_insert_initial_data.sql
```

#### 第三步：验证数据
```sql
-- 验证数据插入结果
\i 03_verify_data.sql
```

### 预期结果

执行完成后，您应该看到：

1. **8个核心表**已创建：
   - companies (公司主体表)
   - companies_history (公司历史表)
   - persons (自然人表)
   - persons_history (自然人历史表)
   - roles (角色定义表)
   - company_person_roles (公司人员关系表)
   - tags (标签定义表)
   - company_tags (公司标签关联表)

2. **基础数据**已插入：
   - 3个角色定义：法定代表人、授权经办人、财务负责人
   - 3个标签定义：国家高新技术企业、省级"专精特新"企业、核心战略客户

3. **核心客户数据**已录入：
   - 成都中科卓尔智能科技集团有限公司
   - 法定代表人：杨伟
   - 完整的关系映射和标签分配

### 验证检查清单

运行验证脚本后，确认以下内容：

- [ ] 所有8个表都已成功创建
- [ ] 角色表包含3条记录
- [ ] 标签表包含3条记录  
- [ ] 公司表包含1条记录（成都中科卓尔）
- [ ] 人员表包含1条记录（杨伟）
- [ ] 公司人员关系表包含1条记录
- [ ] 公司标签关联表包含3条记录
- [ ] 历史表中包含相应的初始记录

### 常见问题排查

#### 问题1：UUID函数不存在
如果遇到 `gen_random_uuid()` 函数不存在的错误：
```sql
-- 启用uuid-ossp扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 或者使用pgcrypto扩展
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

#### 问题2：时区相关错误
如果遇到时区相关问题：
```sql
-- 检查当前时区设置
SHOW timezone;
-- 设置时区（如需要）
SET timezone = 'Asia/Shanghai';
```

#### 问题3：权限不足
确保数据库用户具有以下权限：
- CREATE TABLE
- INSERT
- SELECT
- CREATE INDEX
- COMMENT

### 下一步操作

数据库搭建完成后，您可以：

1. **连接应用程序**：使用您的应用程序连接到数据库
2. **扩展数据模型**：根据业务需求添加新的表和字段
3. **导入更多数据**：批量导入其他企业和人员信息
4. **开发API接口**：基于这个数据模型开发RESTful API
5. **配置备份策略**：设置定期数据备份

### 联系支持

如果在执行过程中遇到问题，请检查：
1. PostgreSQL版本兼容性
2. 数据库连接配置
3. 用户权限设置
4. 扩展模块安装情况

---

**重要提示**：本脚本中使用的身份证号码为示例数据，请在生产环境中使用真实且合规的数据。
