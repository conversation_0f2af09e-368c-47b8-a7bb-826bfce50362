#!/usr/bin/env python3
"""
检查数据库中成都中科卓尔智能科技集团有限公司的现有信息
"""

import sqlite3
from pathlib import Path

def check_company_data():
    db_path = Path('database/enterprise_service.db')
    if not db_path.exists():
        print('数据库文件不存在')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查找成都中科卓尔的基础信息
        cursor.execute("SELECT * FROM companies WHERE company_name LIKE '%中科卓尔%'")
        company_info = cursor.fetchone()
        
        if company_info:
            print('=== 企业基础信息 ===')
            columns = [description[0] for description in cursor.description]
            for i, value in enumerate(company_info):
                print(f'{columns[i]}: {value}')
            
            company_id = company_info[0]
            print(f'\n企业ID: {company_id}')
            
            # 查找相关的存款协议信息
            cursor.execute('SELECT * FROM deposit_agreements WHERE company_id = ?', (company_id,))
            deposit_info = cursor.fetchall()
            
            if deposit_info:
                print('\n=== 存款协议信息 ===')
                cursor.execute('PRAGMA table_info(deposit_agreements)')
                deposit_columns = [col[1] for col in cursor.fetchall()]
                
                for record in deposit_info:
                    print('\n协议记录:')
                    for i, value in enumerate(record):
                        print(f'  {deposit_columns[i]}: {value}')
            else:
                print('\n=== 存款协议信息 ===')
                print('暂无存款协议记录')
            
            # 查找其他可能相关的表
            print('\n=== 检查其他相关信息 ===')
            
            # 检查是否有合同支用相关的表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%contract%' OR name LIKE '%disbursement%'")
            contract_tables = cursor.fetchall()
            
            if contract_tables:
                print('合同支用相关表:')
                for table in contract_tables:
                    print(f'  - {table[0]}')
            else:
                print('未找到合同支用相关表')
            
        else:
            print('未找到成都中科卓尔智能科技集团有限公司的记录')
        
        # 显示所有表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print('\n=== 数据库表结构概览 ===')
        for table in tables:
            table_name = table[0]
            print(f'\n表名: {table_name}')
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            for col in columns:
                print(f'  - {col[1]} ({col[2]})')
        
    except Exception as e:
        print(f'查询出错: {e}')
    finally:
        conn.close()

if __name__ == "__main__":
    check_company_data()
