#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展数据库结构以支持信贷业务申请书
添加财务主管和财务数据字段
"""

import sqlite3
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseExtender:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
    def extend_database(self):
        """扩展数据库结构"""
        logger.info("🔧 开始扩展数据库结构...")
        
        if not self.db_path.exists():
            logger.error(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 1. 添加新字段
            self._add_new_columns(cursor)
            
            # 2. 更新神光光学数据
            self._update_shenguang_data(cursor)
            
            # 3. 验证更新结果
            self._verify_updates(cursor)
            
            conn.commit()
            conn.close()
            
            logger.info("✅ 数据库扩展完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库扩展失败: {e}")
            return False
    
    def _add_new_columns(self, cursor):
        """添加新字段"""
        logger.info("📋 添加新字段...")
        
        new_columns = [
            ("finance_manager_name", "TEXT", "财务主管姓名"),
            ("finance_manager_phone", "TEXT", "财务主管电话"),
            ("total_assets", "REAL", "资产总额（万元）"),
            ("total_liabilities", "REAL", "负债总额（万元）")
        ]
        
        for column_name, column_type, description in new_columns:
            try:
                cursor.execute(f"ALTER TABLE companies ADD COLUMN {column_name} {column_type}")
                logger.info(f"   ✅ 添加字段: {column_name} ({description})")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    logger.info(f"   ⚠️ 字段已存在: {column_name}")
                else:
                    logger.error(f"   ❌ 添加字段失败: {column_name} - {e}")
                    raise
    
    def _update_shenguang_data(self, cursor):
        """更新神光光学集团有限公司的数据"""
        logger.info("📊 更新神光光学集团数据...")
        
        # 神光光学的ID
        shenguang_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
        
        # 更新数据
        update_data = {
            "finance_manager_name": "钟蓬川",
            "finance_manager_phone": "***********", 
            "total_assets": 75930.0,  # 万元
            "total_liabilities": 53752.0,  # 万元
            "business_scope": """一般项目：新材料技术研发；新材料技术推广服务；技术玻璃制品制造【分支机构经营】；光学玻璃销售；技术玻璃制品销售；电子专用材料销售；技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；功能玻璃和新型光学材料销售；货物进出口；技术进出口；进出口代理。(除依法须经批准的项目外，凭营业执照依法自主开展经营活动)""",
            "registration_date": "2012-10-11",
            "registered_capital": 18607.4012,  # 万元
            "contact_phone": "***********"
        }
        
        # 构建更新SQL
        set_clause = ", ".join([f"{key} = ?" for key in update_data.keys()])
        values = list(update_data.values())
        values.append(shenguang_id)  # WHERE条件的值
        
        cursor.execute(f"""
            UPDATE companies 
            SET {set_clause}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """, values)
        
        affected_rows = cursor.rowcount
        if affected_rows > 0:
            logger.info(f"   ✅ 成功更新神光光学数据，影响行数: {affected_rows}")
        else:
            logger.warning(f"   ⚠️ 未找到神光光学记录，ID: {shenguang_id}")
    
    def _verify_updates(self, cursor):
        """验证更新结果"""
        logger.info("🔍 验证更新结果...")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(companies)")
        columns = cursor.fetchall()
        
        new_column_names = ["finance_manager_name", "finance_manager_phone", "total_assets", "total_liabilities"]
        found_columns = [col[1] for col in columns if col[1] in new_column_names]
        
        logger.info(f"   📋 新增字段检查: {len(found_columns)}/{len(new_column_names)} 个字段已添加")
        for col_name in found_columns:
            logger.info(f"      ✅ {col_name}")
        
        # 检查神光光学数据
        cursor.execute("""
            SELECT 
                company_name,
                finance_manager_name,
                finance_manager_phone,
                total_assets,
                total_liabilities,
                business_scope,
                registration_date,
                registered_capital,
                contact_phone
            FROM companies 
            WHERE id = ?
        """, ("14371dda-2f8f-4d4c-82e6-c431dcf3b146",))
        
        result = cursor.fetchone()
        if result:
            logger.info("   📊 神光光学数据验证:")
            fields = [
                "企业名称", "财务主管", "财务主管电话", "资产总额", "负债总额", 
                "经营范围", "注册日期", "注册资本", "联系电话"
            ]
            for i, (field, value) in enumerate(zip(fields, result)):
                if isinstance(value, str) and len(value) > 50:
                    display_value = value[:50] + "..."
                else:
                    display_value = value
                logger.info(f"      {field}: {display_value}")
        else:
            logger.warning("   ⚠️ 未找到神光光学数据")

def main():
    """主函数"""
    print("🚀 开始扩展数据库以支持信贷业务申请书")
    print("="*60)
    
    extender = DatabaseExtender()
    success = extender.extend_database()
    
    print("="*60)
    if success:
        print("✅ 数据库扩展成功完成！")
        print("📋 新增字段:")
        print("   - finance_manager_name (财务主管姓名)")
        print("   - finance_manager_phone (财务主管电话)")
        print("   - total_assets (资产总额)")
        print("   - total_liabilities (负债总额)")
        print("📊 神光光学集团数据已更新")
    else:
        print("❌ 数据库扩展失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
