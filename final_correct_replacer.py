#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终正确的替换器 - 从条件汇总表中提取表格，插入到落实情况表的对应位置
用户要求：把条件汇总表_卓尔_20250804_170832.docx中的三个表格放到落实情况表.docx的一、二、三部分
"""

import os
import shutil
from docx import Document
from docx.shared import RGBColor, Pt
from docx.enum.table import WD_TABLE_ALIGNMENT
from datetime import datetime
from pathlib import Path

class FinalCorrectReplacer:
    """最终正确的替换器"""
    
    def __init__(self):
        self.green_rgb = RGBColor(0, 128, 0)  # 绿色标记
        self.project_root = Path(__file__).parent
        
        # 源文件路径
        self.source_file = self.project_root / "templates" / "contract_disbursement" / "卓尔" / "条件汇总表_卓尔_20250804_170832.docx"
        self.template_file = self.project_root / "templates" / "contract_disbursement" / "落实情况表.docx"
        self.output_dir = self.project_root / "test_output"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
    def extract_tables_from_source(self):
        """从条件汇总表中提取三个表格"""
        try:
            if not self.source_file.exists():
                print(f"❌ 源文件不存在: {self.source_file}")
                return None
            
            print(f"📖 读取源文件: {self.source_file}")
            source_doc = Document(self.source_file)
            
            # 提取所有表格
            tables = source_doc.tables
            print(f"✅ 找到 {len(tables)} 个表格")
            
            # 分析表格内容，识别三个目标表格
            extracted_tables = {
                'precondition': None,    # 前提条件表格
                'continuous': None,      # 持续条件表格  
                'business': None         # 业务申报书条件表格
            }
            
            for i, table in enumerate(tables):
                print(f"🔍 分析第 {i+1} 个表格...")
                table_text = self.get_table_text(table)
                print(f"   表格内容预览: {table_text[:100]}...")
                
                # 根据表格内容判断类型
                if "持续条件" in table_text or "评级水平" in table_text:
                    extracted_tables['continuous'] = table
                    print(f"✅ 识别为持续条件表格")
                elif "用信前提条件" in table_text or "流动资金贷款" in table_text:
                    extracted_tables['precondition'] = table
                    print(f"✅ 识别为前提条件表格")
                elif ("担保措施" in table_text or "贸易背景" in table_text or
                      "项目" in table_text and "具体内容" in table_text):
                    extracted_tables['business'] = table
                    print(f"✅ 识别为业务申报书条件表格")
            
            return extracted_tables
            
        except Exception as e:
            print(f"❌ 提取表格时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_table_text(self, table):
        """获取表格的所有文本内容"""
        text_parts = []
        for row in table.rows:
            for cell in row.cells:
                text_parts.append(cell.text.strip())
        return " ".join(text_parts)
    
    def copy_table_structure(self, source_table, target_doc):
        """复制表格结构和内容到目标文档"""
        if source_table is None:
            return None
        
        # 获取源表格的行列数
        rows_count = len(source_table.rows)
        cols_count = len(source_table.columns)
        
        print(f"   复制表格: {rows_count}行 x {cols_count}列")
        
        # 在目标文档中创建相同结构的表格
        new_table = target_doc.add_table(rows=rows_count, cols=cols_count)
        new_table.style = 'Table Grid'
        new_table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # 复制每个单元格的内容和格式
        for row_idx, source_row in enumerate(source_table.rows):
            target_row = new_table.rows[row_idx]
            for col_idx, source_cell in enumerate(source_row.cells):
                target_cell = target_row.cells[col_idx]
                target_cell.text = source_cell.text
                
                # 设置格式
                for para in target_cell.paragraphs:
                    for run in para.runs:
                        # 如果是表头行，设置为黑色加粗
                        if row_idx == 0:
                            run.font.bold = True
                            run.font.color.rgb = RGBColor(0, 0, 0)
                        else:
                            # 数据行设置为绿色
                            run.font.color.rgb = self.green_rgb
        
        return new_table
    
    def insert_tables_into_template(self, extracted_tables):
        """将提取的表格插入到落实情况表模板中"""
        try:
            if not self.template_file.exists():
                print(f"❌ 模板文件不存在: {self.template_file}")
                return None
            
            # 创建输出文件
            output_file = self.output_dir / f"落实情况表_最终版本_卓尔_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(self.template_file, output_file)
            print(f"✅ 复制模板文件: {output_file}")
            
            # 打开目标文档
            target_doc = Document(output_file)
            
            # 在文档末尾添加三个部分和对应的表格
            self.add_sections_with_tables(target_doc, extracted_tables)
            
            # 保存文档
            target_doc.save(output_file)
            print(f"✅ 成功保存文档: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"❌ 插入表格时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def add_sections_with_tables(self, target_doc, extracted_tables):
        """在文档末尾添加三个部分和对应的表格"""
        print("📝 在文档末尾添加三个部分...")
        
        # 添加空行
        target_doc.add_paragraph("")
        
        # 1. 添加前提条件部分
        if extracted_tables['precondition']:
            print("✅ 添加前提条件部分...")
            heading1 = target_doc.add_paragraph('一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况')
            heading1.runs[0].font.bold = True
            heading1.runs[0].font.size = Pt(14)
            self.copy_table_structure(extracted_tables['precondition'], target_doc)
            target_doc.add_paragraph("")  # 空行
        else:
            print("⚠️ 未找到前提条件表格，跳过")

        # 2. 添加持续条件部分
        if extracted_tables['continuous']:
            print("✅ 添加持续条件部分...")
            heading2 = target_doc.add_paragraph('二、单户综合融资总量方案申报书中列明的持续条件及落实情况')
            heading2.runs[0].font.bold = True
            heading2.runs[0].font.size = Pt(14)
            self.copy_table_structure(extracted_tables['continuous'], target_doc)
            target_doc.add_paragraph("")  # 空行
        else:
            print("⚠️ 未找到持续条件表格，跳过")

        # 3. 添加业务申报书条件部分
        if extracted_tables['business']:
            print("✅ 添加业务申报书条件部分...")
            heading3 = target_doc.add_paragraph('三、单笔业务申报书中列明的贷款条件及落实情况')
            heading3.runs[0].font.bold = True
            heading3.runs[0].font.size = Pt(14)
            self.copy_table_structure(extracted_tables['business'], target_doc)
        else:
            print("⚠️ 未找到业务申报书条件表格，跳过")
        
        print("✅ 所有部分添加完成")
    
    def execute_replacement(self):
        """执行完整的替换流程"""
        print("🚀 开始执行最终正确的替换...")
        
        # 1. 从源文件提取表格
        extracted_tables = self.extract_tables_from_source()
        if not extracted_tables:
            print("❌ 无法提取表格")
            return None
        
        # 检查是否成功提取了所有表格
        found_count = sum(1 for table in extracted_tables.values() if table is not None)
        print(f"📊 成功识别 {found_count}/3 个表格")
        
        # 2. 将表格插入到模板中
        result = self.insert_tables_into_template(extracted_tables)
        return result

def main():
    """主函数"""
    replacer = FinalCorrectReplacer()
    
    print("=" * 80)
    print("📋 最终正确的落实情况表替换器")
    print("=" * 80)
    print("🎯 任务:")
    print("  ✅ 从条件汇总表_卓尔_20250804_170832.docx中提取三个表格")
    print("  ✅ 插入到落实情况表.docx的对应位置")
    print("  ✅ 保持原始模板的所有格式")
    print("=" * 80)
    
    result = replacer.execute_replacement()
    
    if result:
        print(f"\n🎉 落实情况表替换完成！")
        print(f"📄 文件位置: {result}")
        print(f"📝 完成内容:")
        print(f"  ✅ 保持了原始模板的所有格式")
        print(f"  ✅ 从源文件提取并插入了表格")
        print(f"  ✅ 添加了标准的三个部分标题")
        print(f"  ✅ 表头为黑色加粗，数据为绿色标记")
    else:
        print(f"\n❌ 替换失败")

if __name__ == "__main__":
    main()
