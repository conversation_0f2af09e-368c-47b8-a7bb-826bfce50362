#!/usr/bin/env python3
"""
精确找到需要替换的三个目标部分
"""

import docx
from pathlib import Path

def find_exact_replacement_targets():
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    
    print('=== 精确定位替换目标 ===\n')
    
    doc = docx.Document(template_file)
    
    # 查找三个目标部分
    targets_found = {
        'precondition': None,
        'continuous': None, 
        'loan_condition': None
    }
    
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                
                # 1. 查找用信前提条件部分
                if '用信前提条件及落实情况' in cell_text:
                    print(f'🎯 找到用信前提条件目标:')
                    print(f'   位置: 表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1}')
                    print(f'   当前内容: {cell_text[:100]}...')
                    targets_found['precondition'] = {
                        'table_idx': table_idx,
                        'row_idx': row_idx,
                        'cell_idx': cell_idx,
                        'current_content': cell_text
                    }
                    print()
                
                # 2. 查找持续条件部分
                elif '持续条件及落实情况' in cell_text:
                    print(f'🎯 找到持续条件目标:')
                    print(f'   位置: 表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1}')
                    print(f'   当前内容: {cell_text[:100]}...')
                    targets_found['continuous'] = {
                        'table_idx': table_idx,
                        'row_idx': row_idx,
                        'cell_idx': cell_idx,
                        'current_content': cell_text
                    }
                    print()
                
                # 3. 查找贷款条件部分
                elif '贷款条件及落实情况' in cell_text:
                    print(f'🎯 找到贷款条件目标:')
                    print(f'   位置: 表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1}')
                    print(f'   当前内容: {cell_text[:100]}...')
                    targets_found['loan_condition'] = {
                        'table_idx': table_idx,
                        'row_idx': row_idx,
                        'cell_idx': cell_idx,
                        'current_content': cell_text
                    }
                    print()
    
    # 检查是否找到所有目标
    print('📊 目标查找结果:')
    for target_name, target_info in targets_found.items():
        if target_info:
            print(f'✅ {target_name}: 已找到')
        else:
            print(f'❌ {target_name}: 未找到')
    
    return targets_found

def show_all_cells_with_keywords():
    """显示所有包含关键词的单元格"""
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    doc = docx.Document(template_file)
    
    print('\n=== 所有包含关键词的单元格 ===\n')
    
    keywords = ['前提条件', '持续条件', '贷款条件', '落实情况']
    
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                
                # 检查是否包含任何关键词
                for keyword in keywords:
                    if keyword in cell_text:
                        print(f'📍 表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1}:')
                        print(f'   关键词: {keyword}')
                        print(f'   内容: {cell_text[:150]}...')
                        print()
                        break

if __name__ == "__main__":
    targets = find_exact_replacement_targets()
    show_all_cells_with_keywords()
