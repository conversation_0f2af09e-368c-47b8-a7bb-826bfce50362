#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析条件落实情况表模板的详细结构
"""

import docx
from pathlib import Path

def analyze_template_structure():
    """分析模板结构"""
    project_root = Path(__file__).parent
    template_path = project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
    
    print("📋 条件落实情况表模板结构分析")
    print("="*60)
    
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    try:
        doc = docx.Document(template_path)
        
        print(f"📄 模板文件: {template_path.name}")
        print(f"📊 段落总数: {len(doc.paragraphs)}")
        print(f"📊 表格总数: {len(doc.tables)}")
        
        # 分析段落内容
        print(f"\n📝 段落内容分析:")
        print("-" * 40)
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:  # 只显示非空段落
                print(f"段落 {i+1:2d}: {text[:100]}{'...' if len(text) > 100 else ''}")
                
                # 检查是否包含可能需要替换的关键词
                keywords_to_check = [
                    "成都中科卓尔", "中科卓尔", "杨伟", "王斯颖",
                    "PIFU", "KHED", "万元", "2025", "2024",
                    "建八", "保证", "质押", "担保", "ESG"
                ]
                
                found_keywords = []
                for keyword in keywords_to_check:
                    if keyword in text:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"         🔍 包含关键词: {', '.join(found_keywords)}")
        
        # 分析表格内容
        if doc.tables:
            print(f"\n📊 表格内容分析:")
            print("-" * 40)
            
            for table_idx, table in enumerate(doc.tables):
                print(f"\n表格 {table_idx + 1}:")
                print(f"   行数: {len(table.rows)}")
                print(f"   列数: {len(table.columns) if table.rows else 0}")
                
                # 显示表格内容
                for row_idx, row in enumerate(table.rows):
                    row_texts = []
                    for cell_idx, cell in enumerate(row.cells):
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_texts.append(f"列{cell_idx+1}: {cell_text[:50]}{'...' if len(cell_text) > 50 else ''}")
                    
                    if row_texts:
                        print(f"   行{row_idx+1}: {' | '.join(row_texts)}")
                        
                        # 检查表格中的关键词
                        full_row_text = ' '.join([cell.text for cell in row.cells])
                        found_keywords = []
                        for keyword in keywords_to_check:
                            if keyword in full_row_text:
                                found_keywords.append(keyword)
                        
                        if found_keywords:
                            print(f"         🔍 包含关键词: {', '.join(found_keywords)}")
        
        # 分析可能的替换字段
        print(f"\n🎯 可能需要替换的字段分析:")
        print("-" * 40)
        
        all_text = ""
        for para in doc.paragraphs:
            all_text += para.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    all_text += cell.text + " "
        
        # 查找特定模式
        import re
        
        patterns_to_find = {
            "企业名称": r"成都中科卓尔智能科技集团有限公司|中科卓尔",
            "法定代表人": r"杨伟",
            "配偶": r"王斯颖", 
            "编号": r"PIFU\d+[A-Z0-9]*|KHED\d+[A-Z0-9]*",
            "金额": r"\d+万元",
            "日期": r"202[4-5]年\d+月\d*日?",
            "合同编号": r"建八[^（]*（202[4-5]）\d+号",
            "担保相关": r"担保|保证|质押",
            "ESG相关": r"ESG|环保|绿色"
        }
        
        for pattern_name, pattern in patterns_to_find.items():
            matches = re.findall(pattern, all_text)
            if matches:
                unique_matches = list(set(matches))
                print(f"   {pattern_name}: {unique_matches}")
        
        # 生成替换建议
        print(f"\n💡 替换策略建议:")
        print("-" * 40)
        print("1. 精确文本替换:")
        print("   - 企业全名: '成都中科卓尔智能科技集团有限公司' → 数据库企业名称")
        print("   - 企业简称: '中科卓尔' → 数据库企业简称")
        print("   - 法定代表人: '杨伟' → 数据库法人姓名")
        print("   - 配偶: '王斯颖' → 数据库配偶姓名")
        
        print("\n2. 模式匹配替换:")
        print("   - PIFU编号: 查找PIFU开头的编号进行替换")
        print("   - KHED编号: 查找KHED开头的编号进行替换")
        print("   - 金额: 查找'数字+万元'模式进行替换")
        print("   - 合同编号: 查找'建八+内容+(年份)+号'模式进行替换")
        
        print("\n3. 颜色标记策略:")
        print("   - 只对实际替换的内容进行颜色标记")
        print("   - 不标记标题和说明文字")
        print("   - 绿色标记成功替换的字段")
        print("   - 黄色标记需要手动填写的字段")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    analyze_template_structure()

if __name__ == "__main__":
    main()
