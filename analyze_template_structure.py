#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析落实情况表模板结构
"""

from docx import Document
from pathlib import Path

def analyze_template():
    """分析落实情况表模板的结构"""
    template_path = Path("templates/contract_disbursement/落实情况表.docx")
    
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    print("📋 分析落实情况表模板结构...")
    print("=" * 60)
    
    doc = Document(template_path)
    
    # 分析段落
    print("📝 段落内容:")
    for i, para in enumerate(doc.paragraphs):
        if para.text.strip():
            print(f"  {i+1:2d}. {para.text[:100]}...")
            if "一、" in para.text or "二、" in para.text or "三、" in para.text:
                print(f"      ⭐ 发现插入位置标记!")
    
    print("\n📊 表格结构:")
    for i, table in enumerate(doc.tables):
        print(f"  表格 {i+1}: {len(table.rows)}行 x {len(table.columns)}列")
        # 显示表格第一行内容
        if table.rows:
            first_row_text = " | ".join([cell.text[:20] for cell in table.rows[0].cells])
            print(f"    第一行: {first_row_text}")
    
    print(f"\n📄 文档总结:")
    print(f"  - 总段落数: {len(doc.paragraphs)}")
    print(f"  - 总表格数: {len(doc.tables)}")

if __name__ == "__main__":
    analyze_template()
