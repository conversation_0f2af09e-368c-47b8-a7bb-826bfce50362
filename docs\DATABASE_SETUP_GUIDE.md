# 数据库部署指南

## 🎯 目标
将企业信息核心库系统从"演示模式"切换到"生产就绪模式"，连接真实的PostgreSQL数据库。

## 📋 前置条件
- ✅ PostgreSQL数据库服务已安装并运行
- ✅ 拥有数据库的连接信息（主机、端口、用户名、密码）
- ✅ 数据库用户具有创建表和插入数据的权限

## 🚀 部署步骤

### 第一步：安装数据库驱动
```bash
cd enterprise_service_db
python -m pip install psycopg2-binary
```

### 第二步：配置数据库连接
1. 编辑 `.env` 文件（已创建模板）
2. 修改以下配置项为您的实际数据库信息：
```bash
DB_HOST=your_database_host          # 例如: localhost 或 *************
DB_PORT=your_database_port          # 例如: 5432
DB_NAME=your_database_name          # 例如: enterprise_service
DB_USER=your_database_user          # 例如: postgres
DB_PASSWORD=your_database_password  # 您的实际密码
```

### 第三步：测试数据库连接
```bash
python test_db_connection.py
```
**预期输出**：
```
✓ 已加载.env配置文件
✓ psycopg2库可用
✓ 数据库连接成功
  PostgreSQL版本: PostgreSQL 14.x ...
```

### 第四步：初始化数据库
```bash
python init_database.py
```
**预期输出**：
```
✓ 已创建 8 个表
✓ 初始数据统计:
  企业数量: 1
  人员数量: 1
  角色数量: 3
  标签数量: 3
🎉 数据库初始化完成！
```

### 第五步：重启API服务
```bash
python run_api.py
```
**预期输出**：
```
企业信息核心库 API 服务
服务地址: http://0.0.0.0:5000
调试模式: True
```

### 第六步：验证真实数据库连接
```bash
curl http://localhost:5000/api/companies
```
**预期输出**：应返回来自PostgreSQL数据库的真实数据

## 🔍 故障排除

### 问题1：psycopg2安装失败
**症状**：`pip install psycopg2-binary` 失败
**解决方案**：
- Windows: 确保安装了Visual C++ Build Tools
- Linux: `sudo apt-get install libpq-dev python3-dev`
- macOS: `brew install postgresql`

### 问题2：数据库连接失败
**症状**：`test_db_connection.py` 报错
**检查项**：
- PostgreSQL服务是否运行
- 防火墙是否阻止连接
- 用户名密码是否正确
- 数据库是否存在

### 问题3：权限不足
**症状**：无法创建表或插入数据
**解决方案**：
```sql
-- 在PostgreSQL中执行
GRANT ALL PRIVILEGES ON DATABASE your_database_name TO your_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO your_user;
```

### 问题4：API仍使用模拟数据
**症状**：API返回模拟数据而非数据库数据
**检查项**：
- 重启API服务
- 检查.env文件配置
- 查看API启动日志

## 📊 验证清单

### ✅ 数据库层验证
- [ ] PostgreSQL服务运行正常
- [ ] 数据库连接测试通过
- [ ] 8个表成功创建
- [ ] 初始数据正确插入

### ✅ API层验证
- [ ] API服务启动无错误
- [ ] `/health` 端点响应正常
- [ ] `/api/companies` 返回数据库数据
- [ ] `/api/company/{id}` 返回完整企业信息

### ✅ 功能验证
- [ ] 前端可以加载企业列表
- [ ] 文档生成功能正常
- [ ] 模板处理功能可用

## 🔄 回滚方案

如果遇到问题需要回滚到演示模式：
1. 重命名或删除 `.env` 文件
2. 重启API服务
3. 系统将自动切换回模拟数据库模式

## 📈 性能优化建议

### 数据库优化
- 调整PostgreSQL配置参数
- 监控连接池使用情况
- 定期执行VACUUM和ANALYZE

### 应用优化
- 调整连接池大小（DB_MIN_CONN, DB_MAX_CONN）
- 启用查询缓存
- 监控API响应时间

## 🔐 安全建议

### 数据库安全
- 使用强密码
- 限制数据库访问IP
- 定期备份数据
- 启用SSL连接

### 应用安全
- 不要在代码中硬编码密码
- 定期更新依赖包
- 监控异常访问

## 📞 技术支持

如遇到问题：
1. 查看API服务日志
2. 运行 `test_db_connection.py` 诊断
3. 检查PostgreSQL日志
4. 验证网络连接

---

**完成以上步骤后，您的企业信息核心库将成功连接到真实的PostgreSQL数据库！** 🎉
