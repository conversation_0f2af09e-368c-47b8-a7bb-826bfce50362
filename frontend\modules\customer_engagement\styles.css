/* 客户接洽模块样式 - 完整功能版本 */

.customer-engagement-module {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
}

/* 模块头部 */
.module-header {
    padding: 2rem;
    background: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    flex: 1;
}

.module-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.module-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.customer-info {
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.info-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    min-width: 80px;
}

.info-value {
    opacity: 0.9;
}

/* 主要内容区域 */
.module-content {
    flex: 1;
    display: flex;
    gap: 2rem;
    padding: 2rem;
    overflow: hidden;
}

/* 操作面板 */
.operations-panel {
    width: 350px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.panel-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    background: white;
    border-radius: 12px 12px 0 0;
}

.panel-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.operation-groups {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.operation-group {
    margin-bottom: 2rem;
}

.operation-group:last-child {
    margin-bottom: 0;
}

.group-title {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.operation-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.operation-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: left;
}

.operation-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.operation-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.operation-btn.secondary {
    background: #6c757d;
    color: white;
}

.operation-btn.secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    flex: 1;
}

/* 文档面板 */
.documents-panel {
    flex: 1;
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.documents-panel .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.document-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.stats-item {
    background: #f8f9fa;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
}

.documents-container {
    flex: 1;
    overflow: hidden;
}

.documents-list {
    height: 100%;
    overflow-y: auto;
    padding: 1rem;
}

/* 文档项目 */
.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.8rem;
    background: white;
    transition: all 0.3s ease;
}

.document-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.doc-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.doc-info {
    flex: 1;
}

.doc-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.doc-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.doc-category {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

.doc-actions {
    display: flex;
    gap: 0.5rem;
}

.doc-action-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.doc-action-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.empty-hint {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 加载状态 */
.loading-placeholder {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模块底部状态栏 */
.module-footer {
    padding: 1rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
}

.status-indicator {
    color: #495057;
    font-weight: 500;
}

.last-update {
    color: #6c757d;
}

/* 上传模态窗口 */
.upload-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.upload-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.upload-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.upload-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.upload-body {
    padding: 2rem;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.upload-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.upload-area p {
    margin: 0;
    color: #6c757d;
}

.upload-area input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.upload-options {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.upload-options label {
    font-weight: 500;
    color: #495057;
}

.upload-options select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

.upload-footer {
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn-cancel, .btn-upload {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
}

.btn-upload {
    background: #667eea;
    color: white;
}

.btn-upload:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-upload:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .module-content {
        flex-direction: column;
        gap: 1rem;
    }

    .operations-panel {
        width: 100%;
    }

    .upload-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .upload-body {
        padding: 1rem;
    }

    .upload-footer {
        padding: 1rem;
    }
}
