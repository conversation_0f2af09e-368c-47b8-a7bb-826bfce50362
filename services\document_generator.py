#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档生成器模块
负责生成流程调度、批量处理、异常处理
"""

import os
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from .data_service import UnifiedDataService
from .template_engine import TemplateEngine
from utils.logger import SystemLogger


class DocumentGenerator:
    """文档生成器类 - 整合数据服务和模板引擎，统一调度文档生成过程"""

    def __init__(self, data_service: Optional[UnifiedDataService] = None,
                 template_config_path: Optional[str] = None):
        """初始化文档生成器"""
        self.logger = SystemLogger("document_generator")

        # 初始化数据服务
        self.data_service = data_service or UnifiedDataService()

        # 初始化模板引擎
        self.template_engine = TemplateEngine()

        # 加载模板配置
        self.template_config_path = template_config_path or self._get_default_template_config_path()
        self.template_config = self._load_template_config()

        # 确保输出目录存在
        self.output_root = Path(__file__).parent.parent / "output"
        self.output_root.mkdir(exist_ok=True)

        self.logger.info("DocumentGenerator 初始化完成")

    def _get_default_template_config_path(self) -> str:
        """获取默认模板配置路径"""
        project_root = Path(__file__).parent.parent
        return str(project_root / "config" / "template_config.yaml")

    def _load_template_config(self) -> Dict[str, Any]:
        """加载模板配置"""
        try:
            config_path = Path(self.template_config_path)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    templates = config.get('templates', {})
                    self.logger.info(f"加载模板配置成功，共 {len(templates)} 个模板")
                    return config
            else:
                self.logger.warning("模板配置文件不存在，使用默认配置")
                return self._get_default_template_config()
        except Exception as e:
            self.logger.error(f"加载模板配置失败: {e}")
            return self._get_default_template_config()

    def _get_default_template_config(self) -> Dict[str, Any]:
        """获取默认模板配置"""
        return {
            'templates': {},
            'global_settings': {
                'output_base_directory': 'output',
                'file_naming_pattern': '{template_id}_{company_name}_{timestamp}',
                'timestamp_format': '%Y%m%d_%H%M%S'
            }
        }

    def generate_document(self, company_id: str, template_id: str,
                         custom_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成单个文档的主函数"""
        try:
            self.logger.info(f"开始生成文档: 企业ID={company_id}, 模板ID={template_id}")

            # 1. 获取模板配置
            template_config = self._get_template_config(template_id)
            if not template_config:
                raise ValueError(f"未找到模板配置: {template_id}")

            # 2. 获取企业数据
            company_data = self.data_service.get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到企业数据: {company_id}")

            # 3. 合并自定义数据
            if custom_data:
                company_data.update(custom_data)

            # 4. 校验必填字段
            required_fields = template_config.get('fields', {}).get('required', [])
            validation_result = self.data_service.validate_required_fields(company_data, required_fields)

            if not validation_result['is_valid']:
                self.logger.warning(f"数据校验未通过: {validation_result['summary']}")

            # 5. 生成输出路径
            output_config = template_config.get('output', {})
            subdirectory = output_config.get('subdirectory', 'output')
            filename_prefix = output_config.get('filename_prefix', template_id)
            file_extension = output_config.get('file_extension', 'docx')

            # 生成输出文件路径
            output_dir = Path(subdirectory)
            output_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{filename_prefix}_{company_data['company_name']}_{timestamp}.{file_extension}"
            output_path = output_dir / filename

            # 6. 获取模板文件路径
            template_file_config = template_config.get('template_file', {})
            template_path = template_file_config.get('path', '')

            if not template_path:
                raise FileNotFoundError(f"模板文件路径未配置")

            # 处理相对路径
            if not Path(template_path).is_absolute():
                project_root = Path(__file__).parent.parent
                template_path = project_root / template_path
            else:
                template_path = Path(template_path)

            if not template_path.exists():
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            # 7. 获取字段映射
            field_mappings = template_config.get('field_mappings', {})

            # 8. 处理模板
            result = self.template_engine.process_template(
                template_path, output_path, company_data, field_mappings
            )

            # 9. 添加生成信息
            if result['success']:
                result.update({
                    'company_id': company_id,
                    'company_name': company_data['company_name'],
                    'template_id': template_id,
                    'generated_at': datetime.now().isoformat(),
                    'validation_result': validation_result
                })

                self.logger.info(f"文档生成成功: {output_path}")
            else:
                self.logger.error(f"文档生成失败: {result.get('error')}")

            return result

        except Exception as e:
            self.logger.error(f"文档生成异常: {e}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'company_id': company_id,
                'template_id': template_id,
                'generated_at': datetime.now().isoformat()
            }

    def _get_template_config(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板配置"""
        try:
            templates = self.template_config.get('templates', {})

            # 直接查找模板ID
            if template_id in templates:
                return templates[template_id]

            # 在所有模板中查找
            for template_name, template_config in templates.items():
                if template_config.get('id') == template_id:
                    return template_config

            self.logger.warning(f"未找到模板配置: {template_id}")
            return None

        except Exception as e:
            self.logger.error(f"获取模板配置失败: {e}")
            return None

    def _generate_output_path(self, template_config: Dict[str, Any], company_name: str) -> str:
        """生成输出文件路径"""
        try:
            # 获取全局设置
            global_settings = self.template_config.get('global_settings', {})

            # 获取输出配置
            output_config = template_config.get('output', {})
            subdirectory = output_config.get('subdirectory', 'documents')
            filename_prefix = output_config.get('filename_prefix', template_config.get('name', 'document'))
            file_extension = output_config.get('file_extension', 'docx')

            # 创建输出目录
            output_dir = self.output_root / subdirectory
            output_dir.mkdir(exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime(global_settings.get('timestamp_format', '%Y%m%d_%H%M%S'))
            safe_company_name = self._sanitize_filename(company_name)

            filename = f"{filename_prefix}_{safe_company_name}_{timestamp}.{file_extension}"

            return str(output_dir / filename)

        except Exception as e:
            self.logger.error(f"生成输出路径失败: {e}")
            # 使用默认路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return str(self.output_root / f"document_{timestamp}.docx")

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        import re
        # 移除或替换非法字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(sanitized) > 50:
            sanitized = sanitized[:50]
        return sanitized

    def get_available_templates(self) -> Dict[str, Any]:
        """获取所有可用模板"""
        try:
            templates = self.template_config.get('templates', {})
            categories = self.template_config.get('categories', {})

            available_templates = {}

            for template_id, template_config in templates.items():
                category = template_config.get('category', 'other')
                category_info = categories.get(category, {'name': category})

                # 验证模板文件是否存在
                template_path = template_config.get('template_file', {}).get('path', '')
                if template_path and Path(template_path).exists():
                    if category_info['name'] not in available_templates:
                        available_templates[category_info['name']] = []

                    available_templates[category_info['name']].append({
                        'id': template_id,
                        'name': template_config.get('name', template_id),
                        'description': template_config.get('description', ''),
                        'category': category
                    })
                else:
                    self.logger.warning(f"模板文件不存在: {template_path}")

            return available_templates

        except Exception as e:
            self.logger.error(f"获取可用模板失败: {e}")
            return {}

    def batch_generate(self, company_ids: List[str], template_ids: List[str],
                      custom_data: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """批量生成文档"""
        try:
            self.logger.info(f"开始批量生成: {len(company_ids)}个企业 x {len(template_ids)}个模板")

            results = []
            total_tasks = len(company_ids) * len(template_ids)
            completed_tasks = 0

            for company_id in company_ids:
                for template_id in template_ids:
                    try:
                        result = self.generate_document(company_id, template_id, custom_data)
                        results.append(result)

                        completed_tasks += 1
                        progress = (completed_tasks / total_tasks) * 100
                        self.logger.info(f"批量生成进度: {progress:.1f}% ({completed_tasks}/{total_tasks})")

                    except Exception as e:
                        self.logger.error(f"批量生成单项失败: {company_id} + {template_id}: {e}")
                        results.append({
                            'success': False,
                            'error': str(e),
                            'company_id': company_id,
                            'template_id': template_id,
                            'generated_at': datetime.now().isoformat()
                        })

            success_count = sum(1 for r in results if r['success'])
            self.logger.info(f"批量生成完成: {success_count}/{len(results)} 成功")

            return results

        except Exception as e:
            self.logger.error(f"批量生成异常: {e}")
            raise
    
    def _parse_template_key(self, template_key: str) -> tuple:
        """解析模板键"""
        try:
            parts = template_key.split('.')
            if len(parts) != 2:
                raise ValueError(f"模板键格式错误: {template_key}")
            return parts[0], parts[1]
        except Exception as e:
            raise ValueError(f"解析模板键失败: {e}")
    
    def _generate_output_path(self, category: str, template_id: str, company_name: str) -> str:
        """生成输出文件路径"""
        try:
            # 创建分类目录
            category_dir = self.output_root / category
            category_dir.mkdir(exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_company_name = self._sanitize_filename(company_name)
            
            filename = f"{template_id}_{safe_company_name}_{timestamp}.docx"
            
            return str(category_dir / filename)
            
        except Exception as e:
            self.logger.error(f"生成输出路径失败: {e}")
            # 使用默认路径
            return str(self.output_root / f"document_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx")
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        import re
        # 移除或替换非法字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(sanitized) > 50:
            sanitized = sanitized[:50]
        return sanitized
    
    def get_generation_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取生成历史记录"""
        try:
            # 这里可以从日志文件或数据库中读取历史记录
            # 暂时返回空列表
            self.logger.info("获取生成历史记录")
            return []
            
        except Exception as e:
            self.logger.error(f"获取生成历史失败: {e}")
            return []
    
    def cleanup_old_files(self, days: int = 30) -> Dict[str, Any]:
        """清理旧的输出文件"""
        try:
            self.logger.info(f"开始清理 {days} 天前的文件")
            
            from datetime import timedelta
            cutoff_date = datetime.now() - timedelta(days=days)
            
            deleted_count = 0
            total_size = 0
            
            for file_path in self.output_root.rglob('*'):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        deleted_count += 1
                        total_size += file_size
            
            result = {
                'deleted_files': deleted_count,
                'freed_space_mb': total_size / (1024 * 1024),
                'cutoff_date': cutoff_date.isoformat()
            }
            
            self.logger.info(f"文件清理完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"文件清理失败: {e}")
            return {
                'deleted_files': 0,
                'freed_space_mb': 0,
                'error': str(e)
            }
