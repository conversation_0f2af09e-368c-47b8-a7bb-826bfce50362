#!/usr/bin/env python3
"""
查询真实公司数据
"""

import requests
import time

def check_companies():
    try:
        # 等待API启动
        time.sleep(2)
        
        print("查询真实公司数据...")
        response = requests.get('http://localhost:5000/api/companies')
        
        if response.status_code == 200:
            data = response.json()
            companies = data.get('data', [])
            
            print(f"找到 {len(companies)} 个公司:")
            print("-" * 60)
            
            for i, company in enumerate(companies[:5]):
                print(f"{i+1}. ID: {company.get('id', 'N/A')}")
                print(f"   名称: {company.get('company_name', 'N/A')}")
                print(f"   信用代码: {company.get('unified_social_credit_code', 'N/A')}")
                print("-" * 60)
                
        else:
            print(f"API调用失败: {response.status_code}")
            print(f"错误: {response.text}")
            
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == '__main__':
    check_companies()
