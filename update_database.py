#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库更新脚本 - 添加信贷业务申请书相关字段
"""

import sqlite3
import sys
from pathlib import Path

def update_database():
    """更新数据库结构"""
    
    # 数据库路径
    db_path = Path(__file__).parent / "database" / "enterprise_service.db"
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            print("🔄 开始更新数据库结构...")
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(companies)")
            existing_columns = [column[1] for column in cursor.fetchall()]
            print(f"📋 当前字段: {existing_columns}")
            
            # 需要添加的字段
            new_fields = [
                ("credit_line_number", "TEXT DEFAULT 'KHED5104885002025228054'"),
                ("business_number", "TEXT DEFAULT 'KHED510488500202522805'"),
                ("guarantee_method", "TEXT"),
                ("loan_amount", "DECIMAL(15,2)"),
                ("loan_term_months", "INTEGER"),
                ("guarantee_amount", "DECIMAL(15,2) DEFAULT 4000.00"),
                ("pledge_value", "DECIMAL(15,2) DEFAULT 328.98"),
                ("company_short_name", "TEXT")
            ]
            
            # 添加新字段
            for field_name, field_type in new_fields:
                if field_name not in existing_columns:
                    try:
                        sql = f"ALTER TABLE companies ADD COLUMN {field_name} {field_type}"
                        cursor.execute(sql)
                        print(f"✅ 添加字段: {field_name}")
                    except Exception as e:
                        print(f"❌ 添加字段失败 {field_name}: {e}")
                else:
                    print(f"⏭️  字段已存在: {field_name}")
            
            # 更新现有数据的公司简称
            cursor.execute("""
                UPDATE companies SET company_short_name = 
                  CASE 
                    WHEN company_name LIKE '%卓尔%' THEN '卓尔'
                    WHEN company_name LIKE '%神光%' THEN '神光'
                    ELSE SUBSTR(company_name, 1, 2)
                  END
                WHERE company_short_name IS NULL OR company_short_name = ''
            """)
            
            updated_rows = cursor.rowcount
            print(f"✅ 更新公司简称: {updated_rows} 条记录")
            
            # 创建索引
            try:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_companies_credit_line ON companies(credit_line_number)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_companies_business_number ON companies(business_number)")
                print("✅ 创建索引成功")
            except Exception as e:
                print(f"⚠️  创建索引失败: {e}")
            
            # 提交更改
            conn.commit()
            
            # 验证更新结果
            cursor.execute("PRAGMA table_info(companies)")
            updated_columns = [column[1] for column in cursor.fetchall()]
            print(f"📋 更新后字段: {updated_columns}")
            
            print("🎉 数据库更新完成！")
            return True
            
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 信贷业务申请书数据库更新工具")
    print("=" * 50)
    
    success = update_database()
    
    if success:
        print("\n✅ 数据库更新成功！")
        print("现在可以使用信贷业务申请书模板了。")
    else:
        print("\n❌ 数据库更新失败！")
        print("请检查错误信息并重试。")
        sys.exit(1)

if __name__ == "__main__":
    main()
