#!/usr/bin/env python3
"""
标准化落实情况表处理器 - 适用于所有客户
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
import sqlite3
from datetime import datetime

class StandardizedImplementationProcessor:
    """标准化落实情况表处理器"""
    
    def __init__(self, company_name_keyword):
        self.company_name_keyword = company_name_keyword
        self.template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
        self.quota_file = Path('templates/contract_disbursement/额度申报书.docx')
        self.business_file = Path('templates/contract_disbursement/业务申报书.docx')
        
    def process_implementation_table(self):
        """处理落实情况表 - 标准化流程"""
        print(f'=== 标准化处理落实情况表 - {self.company_name_keyword} ===\n')
        
        # 1. 提取源数据（标准位置）
        print('📖 提取源数据...')
        precondition_text = self._extract_precondition()
        continuous_table_data = self._extract_continuous_table()
        loan_table_data = self._extract_loan_table()
        
        # 2. 获取企业数据
        print('📊 获取企业数据...')
        company_data = self._get_company_data()
        
        # 3. 加载模板并替换
        print('🔄 进行标准化替换...')
        doc = docx.Document(self.template_file)
        
        # 4. 执行三个标准替换
        self._replace_precondition_text(doc, precondition_text)
        self._insert_continuous_table(doc, continuous_table_data)
        self._insert_loan_table(doc, loan_table_data)
        self._replace_basic_info(doc, company_data)
        
        # 5. 保存文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        company_name = company_data['company_name'] if company_data else self.company_name_keyword
        output_path = Path(f'test_output/落实情况表_{company_name}_{timestamp}.docx')
        doc.save(output_path)
        
        print(f'✅ 标准化处理完成！')
        print(f'📁 输出文件: {output_path}')
        
        return output_path
    
    def _extract_precondition(self):
        """标准位置：额度申报书第5个表格，第2行第2列"""
        doc = docx.Document(self.quota_file)
        table = doc.tables[4]  # 第5个表格（索引4）
        text = table.rows[1].cells[1].text.strip()  # 第2行第2列
        print(f'  ✅ 用信前提条件: {len(text)}字符')
        return text
    
    def _extract_continuous_table(self):
        """标准位置：额度申报书第6个表格（完整表格）"""
        doc = docx.Document(self.quota_file)
        table = doc.tables[5]  # 第6个表格（索引5）
        
        table_data = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                row_data.append(cell.text.strip())
            table_data.append(row_data)
        
        print(f'  ✅ 持续条件表格: {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列')
        return table_data
    
    def _extract_loan_table(self):
        """标准位置：业务申报书第7个表格（完整表格）"""
        doc = docx.Document(self.business_file)
        table = doc.tables[6]  # 第7个表格（索引6）
        
        table_data = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                row_data.append(cell.text.strip())
            table_data.append(row_data)
        
        print(f'  ✅ 贷款条件表格: {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列')
        return table_data
    
    def _get_company_data(self):
        """从数据库获取企业数据"""
        try:
            conn = sqlite3.connect('database/enterprise_service.db')
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, legal_representative, spouse_name,
                       unified_social_credit_code
                FROM companies 
                WHERE company_name LIKE ?
                LIMIT 1
            """, (f'%{self.company_name_keyword}%',))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'company_name': result[0],
                    'legal_representative': result[1],
                    'spouse_name': result[2],
                    'unified_social_credit_code': result[3]
                }
            return None
        except Exception as e:
            print(f'  ❌ 数据库查询失败: {e}')
            return None
    
    def _replace_precondition_text(self, doc, precondition_text):
        """替换用信前提条件（文本格式）"""
        print('  🔄 替换用信前提条件...')
        
        target_table = doc.tables[0]
        target_cell = target_table.rows[8].cells[0]  # 标准位置：第9行第1列
        
        # 清空单元格
        target_cell._element.clear_content()
        
        # 添加标题
        title_para = target_cell.add_paragraph()
        title_run = title_para.add_run('单户综合融资总量方案申报书中列明的用信前提条件及落实情况\n\n')
        
        # 添加内容（红色标记）
        content_para = target_cell.add_paragraph()
        content_run = content_para.add_run(precondition_text)
        content_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 用信前提条件替换成功')
    
    def _insert_continuous_table(self, doc, table_data):
        """插入持续条件表格（13行x4列）"""
        print('  🔄 插入持续条件表格...')
        
        target_table = doc.tables[0]
        target_cell = target_table.rows[8].cells[0]
        
        # 添加标题
        title_para = target_cell.add_paragraph()
        title_run = title_para.add_run('\n\n二、单户综合融资总量方案申报书中列明的持续条件及落实情况\n\n')
        
        # 插入表格
        if table_data and len(table_data) > 0:
            continuous_table = target_cell.add_table(rows=len(table_data), cols=len(table_data[0]))
            continuous_table.style = 'Table Grid'
            
            # 填入数据
            for row_idx, row_data in enumerate(table_data):
                for col_idx, cell_content in enumerate(row_data):
                    if col_idx < len(continuous_table.rows[row_idx].cells):
                        cell = continuous_table.rows[row_idx].cells[col_idx]
                        paragraph = cell.paragraphs[0]
                        run = paragraph.add_run(cell_content)
                        
                        # 表头黑色，数据红色
                        if row_idx > 0:
                            run.font.color.rgb = RGBColor(255, 0, 0)
                        else:
                            run.font.color.rgb = RGBColor(0, 0, 0)
        
        print('    ✅ 持续条件表格插入成功')
    
    def _insert_loan_table(self, doc, loan_table_data):
        """插入贷款条件表格（5行x2列）"""
        print('  🔄 插入贷款条件表格...')
        
        target_table = doc.tables[0]
        target_cell = target_table.rows[8].cells[0]
        
        # 添加标题
        title_para = target_cell.add_paragraph()
        title_run = title_para.add_run('\n\n三、单笔业务申报书中列明的贷款条件及落实情况\n\n')
        
        # 插入表格
        if loan_table_data and len(loan_table_data) > 0:
            loan_table = target_cell.add_table(rows=len(loan_table_data), cols=len(loan_table_data[0]))
            loan_table.style = 'Table Grid'
            
            # 填入数据（全部红色标记）
            for row_idx, row_data in enumerate(loan_table_data):
                for col_idx, cell_content in enumerate(row_data):
                    if col_idx < len(loan_table.rows[row_idx].cells):
                        cell = loan_table.rows[row_idx].cells[col_idx]
                        paragraph = cell.paragraphs[0]
                        run = paragraph.add_run(cell_content)
                        run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 贷款条件表格插入成功')
    
    def _replace_basic_info(self, doc, company_data):
        """替换基础企业信息"""
        if not company_data:
            return
        
        print('  🔄 替换基础信息...')
        
        # 标准替换映射
        replacements = {
            '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
            '杨伟': company_data['legal_representative'],
            '王斯颖': company_data['spouse_name']
        }
        
        target_table = doc.tables[0]
        for row in target_table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    for old_text, new_text in replacements.items():
                        if old_text in paragraph.text and old_text != new_text:
                            paragraph.text = paragraph.text.replace(old_text, new_text)

# 使用示例
def process_for_company(company_keyword):
    """为指定公司处理落实情况表"""
    processor = StandardizedImplementationProcessor(company_keyword)
    return processor.process_implementation_table()

if __name__ == "__main__":
    # 示例：处理中科卓尔
    output_file = process_for_company("中科卓尔")
    print(f"\n🎉 处理完成！输出文件：{output_file}")
