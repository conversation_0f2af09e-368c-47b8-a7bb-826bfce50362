#!/usr/bin/env python3
"""
真正插入表格结构的替换
"""

import docx
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
from pathlib import Path
import sqlite3

def insert_real_table_replacement():
    print('=== 真正插入表格结构的替换 ===\n')
    
    # 文件路径
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 1. 提取源数据
    print('📖 提取源数据...')
    precondition_text = extract_precondition(quota_file)
    continuous_table_data = extract_continuous_table_data(quota_file)
    loan_conditions = extract_loan_conditions(business_file)
    
    # 2. 获取企业数据
    company_data = get_company_data()
    
    # 3. 加载模板
    doc = docx.Document(template_file)
    
    # 4. 进行真正的表格插入替换
    print('🔄 进行真正的表格插入...')
    
    # 替换用信前提条件
    replace_precondition_with_text(doc, precondition_text)
    
    # 插入真正的持续条件表格
    insert_continuous_conditions_table(doc, continuous_table_data)
    
    # 替换贷款条件
    replace_loan_conditions_with_text(doc, loan_conditions)
    
    # 替换基础信息
    replace_basic_info(doc, company_data)
    
    # 5. 保存文件
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(f'test_output/落实情况表_真实表格版_{timestamp}.docx')
    doc.save(output_path)
    
    print(f'✅ 真实表格版生成完成！')
    print(f'📁 输出文件: {output_path}')
    
    return output_path

def extract_precondition(quota_file):
    """提取用信前提条件"""
    doc = docx.Document(quota_file)
    table = doc.tables[4]
    text = table.rows[1].cells[1].text.strip()
    print(f'  ✅ 用信前提条件: {len(text)}字符')
    return text

def extract_continuous_table_data(quota_file):
    """提取持续条件完整表格数据"""
    doc = docx.Document(quota_file)
    table = doc.tables[5]
    
    table_data = []
    for row in table.rows:
        row_data = []
        for cell in row.cells:
            row_data.append(cell.text.strip())
        table_data.append(row_data)
    
    print(f'  ✅ 持续条件表格: {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列')
    return table_data

def extract_loan_conditions(business_file):
    """提取贷款条件"""
    doc = docx.Document(business_file)
    table = doc.tables[6]
    
    conditions = []
    for row in table.rows:
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            if number and content:
                conditions.append({'number': number, 'content': content})
    
    print(f'  ✅ 贷款条件: {len(conditions)}条')
    return conditions

def get_company_data():
    """获取企业数据"""
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT company_name, legal_representative, spouse_name
            FROM companies 
            WHERE company_name LIKE '%中科卓尔%'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'company_name': result[0],
                'legal_representative': result[1],
                'spouse_name': result[2]
            }
        return None
    except Exception as e:
        print(f'  ❌ 数据库查询失败: {e}')
        return None

def replace_precondition_with_text(doc, precondition_text):
    """替换用信前提条件为文本"""
    print('  🔄 替换用信前提条件...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    cell_text = target_cell.text
    if '额度申报书单户综合融资总量使用前提条件直接提取后替换这里' in cell_text:
        new_text = cell_text.replace(
            '额度申报书单户综合融资总量使用前提条件直接提取后替换这里（把我这句话删掉）',
            precondition_text
        )
        
        # 清空并重新填入
        target_cell._element.clear_content()
        paragraph = target_cell.add_paragraph()
        
        # 分段处理，只对新内容标红
        parts = new_text.split(precondition_text)
        for i, part in enumerate(parts):
            if part:
                paragraph.add_run(part)
            if i < len(parts) - 1:
                red_run = paragraph.add_run(precondition_text)
                red_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 用信前提条件替换成功')

def insert_continuous_conditions_table(doc, table_data):
    """插入真正的持续条件表格"""
    print('  🔄 插入持续条件表格...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    # 查找持续条件的提示文字位置
    cell_text = target_cell.text
    if '额度申报书持续条件单户信用额度使用持续条件处' in cell_text:
        
        # 先替换提示文字
        new_text = cell_text.replace(
            '额度申报书持续条件单户信用额度使用持续条件处，直接提取后替换下面这个表（字体还是要保持原本现在的字体大小格式，不要改变）',
            ''
        )
        
        # 清空单元格
        target_cell._element.clear_content()
        
        # 添加标题
        title_paragraph = target_cell.add_paragraph()
        title_run = title_paragraph.add_run('单户综合融资总量方案申报书中列明的持续条件及落实情况')
        title_run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色标题
        
        # 在单元格中插入表格
        if table_data and len(table_data) > 0:
            # 创建表格
            table = target_cell.add_table(rows=len(table_data), cols=len(table_data[0]))
            table.style = 'Table Grid'
            
            # 填入数据
            for row_idx, row_data in enumerate(table_data):
                for col_idx, cell_content in enumerate(row_data):
                    if col_idx < len(table.rows[row_idx].cells):
                        cell = table.rows[row_idx].cells[col_idx]
                        paragraph = cell.paragraphs[0]
                        run = paragraph.add_run(cell_content)
                        
                        # 表头不标红，数据标红
                        if row_idx > 0:  # 非表头行
                            run.font.color.rgb = RGBColor(255, 0, 0)
                        else:  # 表头行
                            run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 添加其余内容
        remaining_paragraph = target_cell.add_paragraph()
        remaining_run = remaining_paragraph.add_run(new_text)
        
        print('    ✅ 持续条件表格插入成功')

def replace_loan_conditions_with_text(doc, loan_conditions):
    """替换贷款条件为文本"""
    print('  🔄 替换贷款条件...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    # 构建贷款条件文本
    loan_text = "\n\n三、单笔业务申报书中列明的贷款条件及落实情况\n\n"
    for condition in loan_conditions:
        loan_text += f"{condition['number']}. {condition['content']}\n\n"
    
    # 查找并替换
    cell_text = target_cell.text
    if '这几条在业务申报书内都有完全标准的描述，请直接替换补充' in cell_text:
        # 在现有内容基础上添加
        paragraph = target_cell.add_paragraph()
        red_run = paragraph.add_run(loan_text)
        red_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 贷款条件替换成功')

def replace_basic_info(doc, company_data):
    """替换基础信息"""
    if not company_data:
        return
    
    print('  🔄 替换基础信息...')
    
    replacements = {
        '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
        '杨伟': company_data['legal_representative'],
        '王斯颖': company_data['spouse_name']
    }
    
    target_table = doc.tables[0]
    for row in target_table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for old_text, new_text in replacements.items():
                    if old_text in paragraph.text and old_text != new_text:
                        paragraph.text = paragraph.text.replace(old_text, new_text)

if __name__ == "__main__":
    insert_real_table_replacement()
