#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工商信息更新脚本：根据工商查询结果更新公司信息
"""

import requests
import json

def update_company_info():
    """更新数据库中的公司信息"""
    
    # API端点
    api_base_url = "http://localhost:5000/api"
    
    # 准确的工商信息
    companies_to_update = [
        {
            "id": "34af7659-d69a-4c05-a697-6ae6eb00aad3",
            "current_name": "成都卫讯科技有限公司",
            "correct_info": {
                "company_name": "成都卫讯科技有限公司",
                "unified_social_credit_code": "915101003320526751",
                "registered_address": "中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号",
                "legal_representative": "待查询",  # 需要进一步查询
                "business_description": "通信设备制造、软件开发"
            }
        },
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "current_name": "成都中科卓尔智能科技集团有限公司",
            "correct_info": {
                "company_name": "成都中科卓尔智能科技集团有限公司",
                "unified_social_credit_code": "91510100MA6CGUGA1W",
                "registered_address": "中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区8楼",
                "legal_representative": "杨伟",
                "business_description": "技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；光学玻璃制造；半导体器件专用设备制造；光学仪器制造等"
            }
        },
        {
            "id": "14371dda-2f8f-4d4c-82e6-c431dcf3b146",
            "current_name": "神光光学集团有限公司",
            "correct_info": {
                "company_name": "神光光学集团有限公司",
                "unified_social_credit_code": "91510100MA62TGHL5X",  # 使用之前生成的有效代码
                "registered_address": "待补充 - 集团总部注册地址",
                "legal_representative": "待查询",
                "business_description": "主营业务为高纯度合成熔石英的研发和生产，是可控核聚变、半导体光掩膜版、精密光学仪器等领域此前'卡脖子'的关键上游原材料"
            }
        }
    ]
    
    print("🔄 开始更新公司工商信息...")
    print("=" * 60)
    
    for company in companies_to_update:
        print(f"\n📋 更新公司: {company['current_name']}")
        print(f"🆔 公司ID: {company['id']}")
        
        try:
            # 构造更新数据
            update_data = {
                "company_name": company['correct_info']['company_name'],
                "unified_social_credit_code": company['correct_info']['unified_social_credit_code'],
                "registered_address": company['correct_info']['registered_address'],
                "business_description": company['correct_info']['business_description'],
                "operated_by_user_id": "12345678-1234-1234-1234-123456789012",
                "operation_reason": f"工商信息核实更新 - {company['correct_info']['company_name']}"
            }
            
            # 如果有法人代表信息，添加到备注中
            if company['correct_info']['legal_representative'] != "待查询":
                update_data['company_notes'] = f"法定代表人: {company['correct_info']['legal_representative']}"
            
            print("📤 发送更新数据:")
            print(json.dumps(update_data, ensure_ascii=False, indent=2))
            
            # 发送PUT请求更新公司信息
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            update_url = f"{api_base_url}/company/{company['id']}"
            response = requests.put(
                update_url,
                json=update_data,
                headers=headers,
                timeout=30
            )
            
            print(f"📡 API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 更新成功!")
                print("📄 响应数据:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                # 验证更新结果
                verify_update(company['id'], company['correct_info'])
                
            else:
                print("❌ 更新失败!")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API调用异常: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
        
        print("-" * 40)

def verify_update(company_id, expected_info):
    """验证更新结果"""
    try:
        verify_url = f"http://localhost:5000/api/company/{company_id}"
        response = requests.get(verify_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            company_data = result.get('data', {})
            
            print("\n🔍 验证更新结果:")
            print(f"   ✅ 公司名称: {company_data.get('company_name')}")
            print(f"   ✅ 信用代码: {company_data.get('unified_social_credit_code')}")
            print(f"   ✅ 注册地址: {company_data.get('registered_address', '')[:50]}...")
            
            # 检查关键字段是否正确更新
            if company_data.get('company_name') == expected_info['company_name']:
                print("   ✅ 公司名称更新正确")
            else:
                print("   ❌ 公司名称更新失败")
                
            if company_data.get('unified_social_credit_code') == expected_info['unified_social_credit_code']:
                print("   ✅ 信用代码更新正确")
            else:
                print("   ❌ 信用代码更新失败")
                
        else:
            print(f"\n⚠️ 验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"\n⚠️ 验证过程出错: {e}")

def get_current_companies():
    """获取当前数据库中的公司信息"""
    try:
        response = requests.get("http://localhost:5000/api/companies", timeout=10)
        if response.status_code == 200:
            companies = response.json()['data']
            print("📋 当前数据库中的公司:")
            for company in companies:
                print(f"   - {company['company_name']} (ID: {company['id']})")
                print(f"     信用代码: {company.get('unified_social_credit_code', 'N/A')}")
                print(f"     注册地址: {company.get('registered_address', 'N/A')}")
                print()
        else:
            print("❌ 获取公司列表失败")
    except Exception as e:
        print(f"❌ 获取公司列表异常: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 企业工商信息更新系统")
    print("📋 任务: 根据工商查询结果更新公司信息")
    print("=" * 60)
    
    # 显示当前公司信息
    print("\n📊 更新前的公司信息:")
    get_current_companies()
    
    # 执行更新
    update_company_info()
    
    print("\n" + "=" * 60)
    print("🎉 工商信息更新任务完成!")
    print("📊 更新后的公司信息:")
    get_current_companies()
    print("=" * 60)
