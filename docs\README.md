# 企业信息核心库 (Enterprise Service Database)

## 项目概述
这是一个完整的企业服务系统，包含数据库、后端API和前端应用，专为银企直联业务设计。系统提供企业信息管理、人员关系维护、资质标签管理等核心功能。

## 🏗️ 系统架构

```
企业信息核心库
├── 数据库层 (PostgreSQL)
│   ├── 企业主体表
│   ├── 人员信息表
│   ├── 关系映射表
│   └── 历史追踪表
├── API层 (Flask)
│   ├── RESTful接口
│   ├── 数据验证
│   ├── 事务管理
│   └── 错误处理
└── 前端层 (HTML/CSS/JavaScript)
    ├── 客户选择界面
    ├── 信息展示面板
    ├── 文档生成器
    └── 操作日志系统
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.7+
- PostgreSQL 12+
- 现代Web浏览器

### 2. 一键部署
```bash
# 克隆或下载项目到本地
cd enterprise_service_db

# 运行自动部署脚本
python deploy.py
```

### 3. 手动部署步骤

#### 步骤1: 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 步骤2: 配置数据库
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置数据库连接信息
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=enterprise_service
# DB_USER=postgres
# DB_PASSWORD=your_password
```

#### 步骤3: 创建数据库结构
```sql
-- 在PostgreSQL中执行
\i 01_create_tables.sql
\i 02_insert_initial_data.sql
\i 03_verify_data.sql
```

#### 步骤4: 启动API服务
```bash
python run_api.py
```

#### 步骤5: 访问前端应用
在Web浏览器中打开 `frontend/index.html`

## 📊 数据库结构

### 核心实体表
1. **companies** - 公司主体表
   - 存储企业的核心标识信息（统一社会信用代码、企业名称）

2. **persons** - 自然人表
   - 存储与企业相关的自然人信息（身份证号、姓名等）

### 历史追踪表
3. **companies_history** - 公司历史版本表
   - 追踪公司信息的所有变更历史

4. **persons_history** - 自然人历史版本表
   - 追踪自然人信息的所有变更历史

### 关系和分类表
5. **roles** - 角色定义表
   - 定义企业中人员可能担任的角色（法定代表人、授权经办人、财务负责人）

6. **company_person_roles** - 公司与人员关系表
   - 建立企业与人员之间的角色关系

7. **tags** - 标签定义表
   - 定义可用于分类企业和关系的标签

8. **company_tags** - 公司标签关联表
   - 为企业分配相应的标签

## 🔌 API接口文档

### 基础信息
- **Base URL**: `http://localhost:5000`
- **Content-Type**: `application/json`

### 端点列表

#### 1. 健康检查
```http
GET /health
```

#### 2. 获取公司列表
```http
GET /api/companies
```
**响应示例**:
```json
{
  "status": "success",
  "message": "获取公司列表成功",
  "data": [
    {
      "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "company_name": "成都中科卓尔智能科技集团有限公司"
    }
  ]
}
```

#### 3. 获取公司详细信息
```http
GET /api/company/{company_id}
```
**响应示例**:
```json
{
  "status": "success",
  "data": {
    "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "unified_social_credit_code": "91510100MA6CL77FXX",
    "company_name": "成都中科卓尔智能科技集团有限公司",
    "personnel": [
      {
        "person_id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
        "person_name": "杨伟",
        "role": "法定代表人",
        "id_number": "51010019800101XXXX",
        "id_type": "身份证"
      }
    ],
    "tags": [
      {
        "tag_name": "国家高新技术企业",
        "tag_category": "企业资质"
      }
    ]
  }
}
```

#### 4. 创建公司
```http
POST /api/companies
```
**请求体**:
```json
{
  "unified_social_credit_code": "91510100MA6CL77FXX",
  "company_name": "新企业有限公司",
  "operated_by_user_id": "c3d4e5f6-a7b8-9012-3456-7890abcdef01",
  "operation_reason": "新增企业信息"
}
```

#### 5. 更新公司
```http
PUT /api/company/{company_id}
```

#### 6. 获取公司历史
```http
GET /api/company/{company_id}/history
```

#### 7. 获取可用模板列表
```http
GET /api/templates
```
**响应示例**:
```json
{
  "status": "success",
  "data": {
    "word": ["yingqi_zhilian_agreement_blueprint.docx", "yingqi_zhilian_oa_text_blueprint.docx"],
    "pdf": ["yingqi_zhilian_authorization_form_template.pdf", "yingqi_zhilian_application_form_template.pdf"]
  }
}
```

#### 8. 生成Word文档
```http
GET /api/company/{company_id}/document/word/{template_name}
```
**功能**: 根据公司数据动态填充Word模板，返回可下载的文档文件

#### 9. 生成PDF文档
```http
GET /api/document/pdf/{template_name}
```
**功能**: 处理PDF表单，自动勾选银企直联相关选项，返回可下载的文档文件

## 🎨 前端功能

### 主要特性
- **响应式设计**: 适配桌面和移动设备
- **实时数据**: 与后端API实时同步
- **动态扩展**: 自动适应数据库结构变化
- **文档生成**: 自动生成业务文档
- **操作日志**: 记录所有用户操作

### 核心模块
1. **客户选择器**: 下拉列表选择企业
2. **信息展示面板**: 动态显示企业详细信息
3. **文档生成器**: 生成银企直联申请表等文档
4. **扩展信息区**: 显示未知字段，支持系统扩展
5. **操作日志**: 实时显示系统操作记录

## 📚 模板档案馆

### 设计理念
模板档案馆是企业信息核心库系统的知识库，采用模块化组织结构，为所有业务模块提供标准化的模板、文档和资产文件管理。

### 核心特性
- **模块化组织**: 每个业务模块拥有独立的分馆目录
- **标准化结构**: 统一的目录结构规范，便于维护和扩展
- **版本管理**: 支持模板的版本控制和历史追踪
- **可扩展性**: 为未来新增业务模块预留充足空间

### 当前业务模块

#### 银企直联分馆 (yingqi_zhilian)
- **路径**: `templates/yingqi_zhilian/`
- **用途**: 存放银企直联业务相关的所有模板和资产
- **结构**: 包含文档模板、表单模板、业务流程、配置文件和静态资源
- **状态**: 已创建基础结构，等待接收业务资产文件

### 扩展规划
未来可添加的业务模块分馆：
- 信贷管理 (xindai_guanli)
- 风险控制 (fengxian_kongzhi)
- 客户关系 (kehu_guanxi)
- 财务管理 (caiwu_guanli)
- 合规审计 (heguei_shenji)

## 🧪 测试

### API测试
```bash
# 运行完整的API测试套件
python test_api.py

# 测试特定URL
python test_api.py --url http://localhost:5000
```

### 前端测试
1. 启动API服务器
2. 在浏览器中打开 `frontend/index.html`
3. 测试各项功能

## 📁 项目结构

```
enterprise_service_db/
├── api/                          # 后端API代码
│   ├── __init__.py
│   ├── app.py                    # Flask应用主文件
│   ├── database.py               # 数据库连接管理
│   ├── models.py                 # 数据模型和验证
│   └── company_service.py        # 企业服务层
├── frontend/                     # 前端代码
│   ├── index.html               # 主页面
│   ├── styles.css               # 样式文件
│   ├── api-client.js            # API客户端
│   ├── ui-manager.js            # UI管理器
│   ├── document-generator.js    # 文档生成器
│   └── app.js                   # 主应用逻辑
├── templates/                    # 模板档案馆
│   ├── README.md                # 档案馆总说明
│   └── yingqi_zhilian/          # 银企直联模板分馆
│       ├── README.md            # 分馆说明文档
│       ├── documents/           # 文档模板（待添加）
│       ├── forms/               # 表单模板（待添加）
│       ├── workflows/           # 业务流程模板（待添加）
│       ├── configs/             # 配置文件模板（待添加）
│       └── assets/              # 静态资源（待添加）
├── 01_create_tables.sql         # 数据库表创建脚本
├── 02_insert_initial_data.sql   # 初始数据插入脚本
├── 03_verify_data.sql           # 数据验证脚本
├── requirements.txt             # Python依赖
├── run_api.py                   # API启动脚本
├── deploy.py                    # 自动部署脚本
├── test_api.py                  # API测试脚本
├── demo.py                      # 系统演示脚本
├── .env.example                 # 环境变量模板
├── EXECUTION_GUIDE.md           # 执行指南
└── README.md                    # 项目文档
```

## 💾 初始数据
系统已预置了第一个核心客户的信息：
- **企业名称**: 成都中科卓尔智能科技集团有限公司
- **统一社会信用代码**: 91510100MA6CL77FXX
- **法定代表人**: 杨伟
- **企业标签**: 国家高新技术企业、省级"专精特新"企业、核心战略客户

## ⚡ 技术特点

### 后端特性
- **动态数据结构**: API自动适应数据库表结构变化
- **事务完整性**: 所有写操作都在事务中完成
- **版本化追踪**: 完整的数据变更历史记录
- **数据验证**: 严格的输入数据校验
- **错误处理**: 完善的异常处理和用户友好的错误信息

### 前端特性
- **响应式设计**: 适配各种屏幕尺寸
- **实时更新**: 与后端API实时同步
- **扩展性支持**: 自动显示未知字段
- **文档生成**: 动态生成业务文档
- **操作日志**: 详细的操作记录和状态跟踪

### 数据库特性
- 使用UUID作为物理主键确保全局唯一性
- 使用业务标识（信用代码、身份证号）作为业务唯一约束
- 所有关键变更都会在历史表中留存审计轨迹
- 采用版本化设计支持数据变更追踪
- 创建了必要的索引以优化查询性能

## 🔧 配置说明

### 环境变量配置
创建 `.env` 文件并配置以下参数：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=enterprise_service
DB_USER=postgres
DB_PASSWORD=your_password
DB_MIN_CONN=1
DB_MAX_CONN=20

# API服务配置
API_HOST=0.0.0.0
API_PORT=5000
API_DEBUG=True

# 日志配置
LOG_LEVEL=INFO
```

### 数据库兼容性
本系统设计为PostgreSQL数据库，使用了以下PostgreSQL特性：
- UUID数据类型
- SERIAL自增类型
- TIMESTAMP WITH TIME ZONE时间戳类型
- gen_random_uuid()函数

## 🚀 部署选项

### 开发环境
```bash
# 启动开发服务器
python run_api.py

# 在浏览器中打开前端
open frontend/index.html
```

### 生产环境
```bash
# 使用Gunicorn启动
gunicorn -w 4 -b 0.0.0.0:5000 api.app:app

# 使用Nginx代理前端静态文件
# 配置Nginx指向frontend目录
```

## 🔍 故障排除

### 常见问题

#### 1. API连接失败
- 检查API服务是否正在运行
- 确认端口5000没有被占用
- 检查防火墙设置

#### 2. 数据库连接失败
- 验证PostgreSQL服务状态
- 检查.env文件中的数据库配置
- 确认数据库用户权限

#### 3. 前端显示异常
- 检查浏览器控制台错误信息
- 确认API服务正常响应
- 验证CORS配置

### 日志查看
```bash
# API服务日志
tail -f api.log

# 数据库查询日志
# 在PostgreSQL配置中启用查询日志
```

## 🔮 后续扩展

该系统架构为后续业务模块扩展预留了充足空间：

### 数据库扩展
- 更多企业属性信息（注册资本、经营范围等）
- 更复杂的人员关系（股东关系、授权关系等）
- 更丰富的标签分类体系
- 业务流程相关的表结构

### API扩展
- 人员管理API
- 标签管理API
- 文件上传API
- 批量操作API

### 前端扩展
- 企业信息编辑功能
- 人员关系管理界面
- 数据统计和图表
- 高级搜索和筛选

## 📄 许可证
本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献指南
欢迎提交Issue和Pull Request来改进这个项目。

## 📞 技术支持
如有技术问题，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至技术支持团队

---

**企业信息核心库** - 为银企直联业务提供强大的数据支撑 🏦✨
