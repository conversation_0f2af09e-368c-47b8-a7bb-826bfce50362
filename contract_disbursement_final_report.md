# 合同支用模块字段分析报告

## 📋 分析概述

通过对 `templates/contract_disbursement` 目录下6个标准文件的分析，识别出102个高亮字段，现整理如下：

## 🎯 核心发现

### ✅ 可直接使用的现有数据库字段 (8个)

| 高亮内容 | 现有数据库字段 | 说明 |
|---------|---------------|------|
| 神光光学集团有限公司 | `companies.company_name` | 企业名称 |
| 成都中科卓尔智能科技集团有限公司 | `companies.company_name` | 企业名称 |

### 🔧 需要扩展的关键数据库字段

#### 1. 合同信息表 (contracts) - 新建表

```sql
CREATE TABLE contracts (
    id UUID PRIMARY KEY,
    company_id UUID REFERENCES companies(id),
    contract_number VARCHAR(100),           -- 合同编号 (如: 建八卓尔（2025）001号)这里命名就按照建八加公司卓尔或其他两个字（如神光）加（2025）001号这样命名
    contract_type VARCHAR(50),              -- 合同类型 (如: 流动资金贷款)这里不改
    contract_amount DECIMAL(15,2),          -- 合同金额 这里不改
    contract_term INTEGER,                  -- 合同期限(月)这里不改
    contract_date DATE,                     -- 合同签订日期 这里不改
    maturity_date DATE,                     -- 到期日期 这里要生成到月份，比如今日就是2025年8月  日
    contract_status VARCHAR(20),            -- 合同状态 这里不改
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 放款信息表 (disbursements) - 新建表

```sql
CREATE TABLE disbursements (
    id UUID PRIMARY KEY,
    company_id UUID REFERENCES companies(id),
    contract_id UUID REFERENCES contracts(id),
    disbursement_amount DECIMAL(15,2),      -- 本次支用金额 等待我提供后生成
    disbursement_date DATE,                 -- 放款日期 不改
    account_number VARCHAR(50),             -- 放款账户 (如: 51050148850800008651，是的按照每个公司的信息)
    loan_purpose TEXT,                      -- 贷款用途 每个公司我提供一次后，你自己写入对应的数据库内
    disbursement_status VARCHAR(20),        -- 放款状态 
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 审批信息表 (approvals) - 新建表

```sql
CREATE TABLE approvals (
    id UUID PRIMARY KEY,
    company_id UUID REFERENCES companies(id),
    approval_number VARCHAR(100),           -- 审批文号 (如: PIFU510000000N202407210，每个公司等待我提供一次后内置数据库)
    approval_type VARCHAR(50),              -- 审批类型 (额度/业务)
    approved_amount DECIMAL(15,2),          -- 批复金额 我自己写不需要内置
    approval_date DATE,                     -- 审批日期 我自己写不需要内置
    validity_start DATE,                    -- 有效期开始 我自己写不需要内置
    validity_end DATE,                      -- 有效期结束 我自己写不需要内置
    approval_status VARCHAR(20),            -- 审批状态 我自己写不需要内置
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 扩展企业表 (companies) - 添加字段

```sql
ALTER TABLE companies ADD COLUMN 
    -- 财务信息
    total_assets DECIMAL(15,2),      需要我
    total_liabilities DECIMAL(15,2),        -- 总负债 (如: 53752万元)
    
    -- 联系人信息
    finance_manager VARCHAR(100),           -- 财务经理 (如: 钟蓬川)
    finance_manager_phone VARCHAR(20),      -- 财务经理电话 (如: ***********)
    
    -- 业务信息
    main_business TEXT,                     -- 主营业务描述
    business_license_number VARCHAR(50),    -- 营业执照号 (如: ********-3)
    
    -- 银行信息
    primary_account VARCHAR(50),            -- 主要账户
    account_manager VARCHAR(100),           -- 客户经理
    account_manager_phone VARCHAR(20);      -- 客户经理电话
```

## 📊 按文件分类的字段分析

### 1. 信贷业务申请书.xlsx
**关键字段**:
- 企业基本信息: 公司名称、法人、注册资本、联系方式
- 财务数据: 总资产、总负债
- 业务描述: 主营业务、经营范围

### 2. 提款申请书.docx
**关键字段**:
- 合同编号: 建八卓尔（2025）001号
- 提款金额: 壹仟万元整
- 提款用途: 日常经营周转需求
- 收款账户: 51050148850800008651

### 3. 落实情况表.docx
**关键字段**:
- 审批信息: 审批文号、批复金额、有效期
- 担保信息: 担保方式、担保条件
- 合同条款: 合同修改要求、落实情况

### 4. 规模台账.xlsm
**关键字段**:
- 产品替代: 建行e贷替代方案
- 业务分类: 供应链产品适用性

## 🎯 优先级建议

### 高优先级 (立即实施)
1. **contracts表** - 合同管理核心
2. **disbursements表** - 放款业务核心
3. **companies表扩展** - 完善企业信息

### 中优先级 (后续实施)
1. **approvals表** - 审批流程管理
2. **guarantee_info表** - 担保信息管理

### 低优先级 (可选)
1. 详细的条件落实跟踪
2. 产品替代方案管理

## 💡 实施建议

1. **分阶段实施**: 先实现核心表结构，再逐步完善
2. **数据迁移**: 现有数据需要清理和标准化
3. **接口适配**: API需要相应调整以支持新字段
4. **前端更新**: 界面需要支持新的数据录入和展示

## 🔍 特别注意

1. **账户号码**: `51050148850800008651` 是重要的业务数据
2. **合同编号**: `建八卓尔（2025）001号` 格式需要标准化
3. **金额字段**: 需要统一使用DECIMAL(15,2)类型
4. **日期字段**: 需要标准化日期格式处理

## 📋 下一步行动

1. 确认字段需求和优先级
2. 创建数据库迁移脚本
3. 更新API接口
4. 调整前端界面
5. 测试数据完整性
