<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业服务驾驶舱 - 外壳应用</title>
    <link rel="stylesheet" href="css/shell.css">
</head>
<body>
    <!-- 主应用外壳 -->
    <div class="shell-container">
        <!-- 顶部导航栏 -->
        <header class="shell-header">
            <div class="header-left">
                <h1 class="shell-title">🏢 企业服务驾驶舱</h1>
                <div class="shell-version">v2.0 - 模块化架构</div>
            </div>
            <div class="header-right">
                <div class="current-customer" id="current-customer-display" style="display: none;">
                    <span class="customer-label">当前客户：</span>
                    <span class="customer-name" id="customer-name-display">-</span>
                    <button class="change-customer-btn" id="change-customer-btn">更换</button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="shell-main">
            <!-- 客户选择阶段 -->
            <div id="customer-selection-stage" class="stage customer-selection-stage">
                <div class="stage-header">
                    <h2>👤 选择客户</h2>
                    <p class="stage-subtitle">请选择要服务的客户</p>
                </div>
                <div class="customer-selector">
                    <select id="customer-select" class="customer-select">
                        <option value="">请选择客户...</option>
                    </select>
                </div>
            </div>

            <!-- 模块选择阶段 -->
            <div id="module-selection-stage" class="stage module-selection-stage" style="display: none;">
                <div class="stage-header">
                    <h2>🎯 选择业务模块</h2>
                    <p class="stage-subtitle">请选择要使用的业务模块</p>
                </div>
                <div class="modules-grid" id="modules-grid">
                    <!-- 动态生成模块卡片 -->
                </div>
            </div>

            <!-- 模块容器（插件舞台） -->
            <div id="module-container" class="module-container" style="display: none;">
                <!-- 业务模块将在此处渲染 -->
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer class="shell-footer">
            <div class="footer-left">
                <span class="status-indicator" id="status-indicator">🟢 系统就绪</span>
            </div>
            <div class="footer-right">
                <span class="current-module" id="current-module-display" style="display: none;">
                    当前模块：<span id="current-module-name">-</span>
                    <button class="change-module-btn" id="change-module-btn">更换模块</button>
                </span>
            </div>
        </footer>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在加载模块...</p>
        </div>
    </div>

    <!-- 外壳应用核心脚本 -->
    <script src="js/shell-core.js"></script>
</body>
</html>
