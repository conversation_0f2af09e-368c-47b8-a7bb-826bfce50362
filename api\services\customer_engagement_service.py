"""
客户接洽与资料准备服务层
处理客户接洽相关的业务逻辑和文档操作
"""

import os
import uuid
import shutil
from datetime import datetime
from typing import Dict, Any, List, Optional, BinaryIO
import logging
from pathlib import Path

# PPT处理库
from pptx import Presentation

# 数据库管理器
try:
    from ..sqlite_database import db_manager
    USE_REAL_DB = True if db_manager else False
except ImportError:
    USE_REAL_DB = False
    db_manager = None

logger = logging.getLogger(__name__)

class CustomerEngagementService:
    """客户接洽与资料准备服务类"""
    
    def __init__(self):
        self.upload_base_dir = "uploads"
        # 使用绝对路径解决路径问题
        self.templates_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "templates", "customer_engagement")

        # 确保目录存在
        os.makedirs(self.upload_base_dir, exist_ok=True)
        os.makedirs(self.templates_dir, exist_ok=True)
    
    def get_checklist_template_path(self) -> str:
        """获取融资资料清单模板文件路径"""
        template_path = os.path.join(self.templates_dir, "customer_engagement_checklist_template.docx")
        
        if not os.path.exists(template_path):
            # 如果模板文件不存在，创建一个示例文件
            self._create_sample_checklist_template(template_path)
        
        return template_path
    
    def _create_sample_checklist_template(self, template_path: str):
        """创建示例融资资料清单模板"""
        try:
            from docx import Document
            
            doc = Document()
            doc.add_heading('融资资料清单', 0)
            
            doc.add_heading('一、企业基本资料', level=1)
            doc.add_paragraph('1. 营业执照副本（加盖公章）')
            doc.add_paragraph('2. 组织机构代码证（加盖公章）')
            doc.add_paragraph('3. 税务登记证（加盖公章）')
            doc.add_paragraph('4. 开户许可证（加盖公章）')
            
            doc.add_heading('二、财务资料', level=1)
            doc.add_paragraph('1. 近三年审计报告')
            doc.add_paragraph('2. 近三个月财务报表')
            doc.add_paragraph('3. 近六个月银行流水')
            doc.add_paragraph('4. 纳税证明')
            
            doc.add_heading('三、法律文件', level=1)
            doc.add_paragraph('1. 公司章程')
            doc.add_paragraph('2. 股东会决议')
            doc.add_paragraph('3. 重要合同协议')
            
            doc.save(template_path)
            logger.info(f"创建示例融资资料清单模板: {template_path}")
            
        except ImportError:
            logger.warning("python-docx库未安装，无法创建示例模板")
        except Exception as e:
            logger.error(f"创建示例模板失败: {e}")
    
    def generate_document(self, company_id: str, template_type: str) -> tuple[bytes, str]:
        """通用文档生成方法，支持多种模板类型"""

        if not USE_REAL_DB:
            raise NotImplementedError("需要真实数据库支持")

        # 获取公司信息
        company = db_manager.get_company_detail(company_id)
        if not company:
            raise ValueError(f"未找到公司信息: {company_id}")

        company_name = company.get('company_name', '未知公司')

        # 根据模板类型选择处理方法
        if template_type == "service_plan":
            return self._generate_service_plan_ppt(company_name)
        elif template_type == "custodianship_letter":
            return self._generate_custodianship_letter_docx(company_name)
        else:
            raise ValueError(f"不支持的模板类型: {template_type}")

    def generate_service_plan_ppt(self, company_id: str) -> tuple[bytes, str]:
        """生成定制化银行服务方案PPT（保持向后兼容）"""
        return self.generate_document(company_id, "service_plan")

    def _generate_service_plan_ppt(self, company_name: str) -> tuple[bytes, str]:
        """生成定制化银行服务方案PPT的具体实现"""

        # 模板文件路径
        template_path = os.path.join(self.templates_dir, "customer_engagement_service_plan_blueprint.pptx")

        if not os.path.exists(template_path):
            # 如果模板不存在，创建一个示例模板
            self._create_sample_service_plan_template(template_path)

        # 打开PPT模板
        prs = Presentation(template_path)

        # 替换所有幻灯片中的占位符
        placeholder_company = "贝瑞光电"  # 模板中的示例公司名

        for slide in prs.slides:
            # 替换文本框中的内容
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    if placeholder_company in shape.text:
                        shape.text = shape.text.replace(placeholder_company, company_name)

                # 处理表格中的内容
                if shape.has_table:
                    table = shape.table
                    for row in table.rows:
                        for cell in row.cells:
                            if placeholder_company in cell.text:
                                cell.text = cell.text.replace(placeholder_company, company_name)

                # 处理文本框内的段落
                if hasattr(shape, "text_frame"):
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            if placeholder_company in run.text:
                                run.text = run.text.replace(placeholder_company, company_name)

        # 保存到内存
        from io import BytesIO
        ppt_buffer = BytesIO()
        prs.save(ppt_buffer)
        ppt_buffer.seek(0)

        # 生成文件名
        filename = f"{company_name}_银行服务方案.pptx"

        logger.info(f"生成定制化服务方案PPT: {company_name}")

        return ppt_buffer.getvalue(), filename

    def _generate_custodianship_letter_docx(self, company_name: str) -> tuple[bytes, str]:
        """生成管护权确认函Word文档的具体实现"""

        # 模板文件路径
        template_path = os.path.join(self.templates_dir, "customer_engagement_custodianship_letter_blueprint.docx")

        if not os.path.exists(template_path):
            raise FileNotFoundError(f"管护权确认函模板文件不存在: {template_path}")

        # 打开Word模板
        from docx import Document
        from docx.enum.text import WD_COLOR_INDEX

        doc = Document(template_path)

        # 需要替换的占位符公司名称
        placeholder_company = "四川天舟新能科技集团有限公司"

        # 替换段落中的内容
        replacement_count = 0
        for paragraph in doc.paragraphs:
            if placeholder_company in paragraph.text:
                replacement_count += self._replace_text_in_paragraph_with_highlight(
                    paragraph, placeholder_company, company_name
                )

        # 替换表格中的内容
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if placeholder_company in paragraph.text:
                            replacement_count += self._replace_text_in_paragraph_with_highlight(
                                paragraph, placeholder_company, company_name
                            )

        # 保存到内存
        from io import BytesIO
        docx_buffer = BytesIO()
        doc.save(docx_buffer)
        docx_buffer.seek(0)

        # 生成文件名
        filename = f"{company_name}_管护权确认函.docx"

        logger.info(f"生成管护权确认函: {company_name}, 替换了 {replacement_count} 处内容")

        return docx_buffer.getvalue(), filename

    def _replace_text_in_paragraph_with_highlight(self, paragraph, old_text: str, new_text: str) -> int:
        """在段落中替换文本并添加高亮显示"""

        from docx.enum.text import WD_COLOR_INDEX

        replacement_count = 0

        # 检查段落文本是否包含要替换的内容
        if old_text not in paragraph.text:
            return 0

        # 遍历段落中的所有run
        for run in paragraph.runs:
            if old_text in run.text:
                # 保存原始格式
                original_font_name = run.font.name
                original_font_size = run.font.size
                original_bold = run.font.bold
                original_italic = run.font.italic

                # 替换文本
                run.text = run.text.replace(old_text, new_text)

                # 设置高亮
                try:
                    run.font.highlight_color = WD_COLOR_INDEX.YELLOW
                    logger.debug(f"为替换文本设置黄色高亮: {new_text}")
                except Exception as e:
                    logger.warning(f"设置高亮失败: {e}")

                # 保持原始格式
                if original_font_name:
                    run.font.name = original_font_name
                if original_font_size:
                    run.font.size = original_font_size
                if original_bold is not None:
                    run.font.bold = original_bold
                if original_italic is not None:
                    run.font.italic = original_italic

                replacement_count += 1

        return replacement_count
    
    def _create_sample_service_plan_template(self, template_path: str):
        """创建示例银行服务方案PPT模板"""
        try:
            prs = Presentation()
            
            # 第一张幻灯片：标题页
            slide_layout = prs.slide_layouts[0]  # 标题幻灯片布局
            slide = prs.slides.add_slide(slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            
            title.text = "银行服务方案"
            subtitle.text = "为贝瑞光电量身定制的金融服务解决方案"
            
            # 第二张幻灯片：公司概况
            slide_layout = prs.slide_layouts[1]  # 标题和内容布局
            slide = prs.slides.add_slide(slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]
            
            title.text = "贝瑞光电 - 公司概况"
            tf = content.text_frame
            tf.text = "贝瑞光电作为我们的重要客户，我们将为其提供全方位的金融服务支持。"
            
            # 第三张幻灯片：服务方案
            slide = prs.slides.add_slide(slide_layout)
            title = slide.shapes.title
            content = slide.placeholders[1]
            
            title.text = "为贝瑞光电定制的服务方案"
            tf = content.text_frame
            tf.text = "• 企业信贷服务\n• 现金管理服务\n• 贸易融资服务\n• 投资理财服务"
            
            prs.save(template_path)
            logger.info(f"创建示例银行服务方案PPT模板: {template_path}")
            
        except Exception as e:
            logger.error(f"创建示例PPT模板失败: {e}")
    
    def upload_company_document(self, company_id: str, file_data: BinaryIO, 
                              original_filename: str, file_size: int,
                              document_category: str = "other_documents",
                              description: str = "", uploaded_by: str = "system") -> str:
        """上传公司文档"""
        
        if not USE_REAL_DB:
            raise NotImplementedError("需要真实数据库支持")
        
        # 验证公司是否存在
        company = db_manager.get_company_detail(company_id)
        if not company:
            raise ValueError(f"未找到公司信息: {company_id}")
        
        # 生成文档ID和存储文件名
        doc_id = str(uuid.uuid4())
        file_extension = Path(original_filename).suffix
        stored_filename = f"{doc_id}{file_extension}"
        
        # 创建公司专用目录
        company_dir = os.path.join(self.upload_base_dir, company_id)
        os.makedirs(company_dir, exist_ok=True)
        
        # 文件存储路径
        file_path = os.path.join(company_dir, stored_filename)
        
        try:
            # 保存文件
            with open(file_path, 'wb') as f:
                shutil.copyfileobj(file_data, f)
            
            # 获取文件类型和MIME类型
            file_type = file_extension.lower().lstrip('.')
            mime_type = self._get_mime_type(file_type)
            
            # 保存文档信息到数据库
            db_manager.execute_update("""
                INSERT INTO company_documents (
                    id, company_id, original_filename, stored_filename, file_path,
                    file_size, file_type, mime_type, document_category, description,
                    uploaded_by, upload_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                doc_id, company_id, original_filename, stored_filename, file_path,
                file_size, file_type, mime_type, document_category, description,
                uploaded_by, datetime.now().isoformat()
            ))
            
            logger.info(f"文档上传成功: {original_filename} -> {company_id}")
            return doc_id
            
        except Exception as e:
            # 如果数据库操作失败，删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            logger.error(f"文档上传失败: {e}")
            raise
    
    def get_company_documents(self, company_id: str) -> List[Dict[str, Any]]:
        """获取公司的所有文档列表"""
        
        if not USE_REAL_DB:
            return []
        
        documents = db_manager.execute_many("""
            SELECT id, original_filename, file_size, file_type, document_category,
                   description, upload_date, uploaded_by
            FROM company_documents
            WHERE company_id = ? AND is_active = 1
            ORDER BY upload_date DESC
        """, (company_id,))
        
        return documents
    
    def _get_mime_type(self, file_type: str) -> str:
        """根据文件类型获取MIME类型"""
        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'txt': 'text/plain',
            'zip': 'application/zip',
            'rar': 'application/x-rar-compressed'
        }
        return mime_types.get(file_type, 'application/octet-stream')
    
    def get_document_categories(self) -> List[Dict[str, Any]]:
        """获取文档分类列表"""
        
        if not USE_REAL_DB:
            return []
        
        categories = db_manager.execute_many("""
            SELECT id, category_name, category_description
            FROM document_categories
            WHERE is_active = 1
            ORDER BY sort_order
        """)
        
        return categories
