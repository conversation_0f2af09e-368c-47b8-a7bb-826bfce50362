/**
 * 客户接洽与资料准备工作台管理器
 */

class CustomerEngagementManager {
    constructor() {
        this.currentCustomer = null;
        this.apiBaseUrl = 'http://localhost:5000';
        this.selectedFiles = [];
        this.documentCategories = [];
        
        this.init();
    }

    async init() {
        try {
            console.log('初始化客户接洽与资料准备工作台...');
            
            // 从URL参数获取客户信息
            this.loadCustomerFromParams();
            
            // 绑定事件
            this.bindEvents();
            
            // 加载文档分类
            await this.loadDocumentCategories();
            
            // 加载客户详细信息
            if (this.currentCustomer?.id) {
                await this.loadCustomerDetails();
                await this.loadCustomerDocuments();
            }
            
            console.log('客户接洽与资料准备工作台初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showToast('初始化失败: ' + error.message, 'error');
        }
    }

    loadCustomerFromParams() {
        const urlParams = new URLSearchParams(window.location.search);
        
        if (urlParams.get('customer_id')) {
            this.currentCustomer = {
                id: urlParams.get('customer_id'),
                company_name: urlParams.get('customer_name') || '',
                unified_social_credit_code: urlParams.get('customer_code') || ''
            };
            
            console.log('从URL参数加载客户信息:', this.currentCustomer);
        }
    }

    bindEvents() {
        // 返回驾驶舱按钮
        const backBtn = document.getElementById('back-to-cockpit');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                window.location.href = 'index_unified_cockpit.html';
            });
        }

        // 下载融资资料清单
        const downloadChecklistBtn = document.getElementById('download-checklist-btn');
        if (downloadChecklistBtn) {
            downloadChecklistBtn.addEventListener('click', () => {
                this.downloadChecklist();
            });
        }

        // 生成定制化服务方案
        const generateServicePlanBtn = document.getElementById('generate-service-plan-btn');
        if (generateServicePlanBtn) {
            generateServicePlanBtn.addEventListener('click', () => {
                this.generateServicePlan();
            });
        }

        // 生成管护权确认函
        const generateCustodianshipLetterBtn = document.getElementById('generate-custodianship-letter-btn');
        if (generateCustodianshipLetterBtn) {
            generateCustodianshipLetterBtn.addEventListener('click', () => {
                this.generateCustodianshipLetter();
            });
        }

        // 文件选择
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }

        // 文件上传
        const uploadBtn = document.getElementById('upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.uploadDocuments();
            });
        }

        // 刷新文档列表
        const refreshBtn = document.getElementById('refresh-documents-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadCustomerDocuments();
            });
        }

        // 拖拽上传
        const fileInputLabel = document.querySelector('.file-input-label');
        if (fileInputLabel) {
            fileInputLabel.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileInputLabel.style.borderColor = '#4CAF50';
                fileInputLabel.style.background = '#f0f8f0';
            });

            fileInputLabel.addEventListener('dragleave', (e) => {
                e.preventDefault();
                fileInputLabel.style.borderColor = '#dee2e6';
                fileInputLabel.style.background = 'white';
            });

            fileInputLabel.addEventListener('drop', (e) => {
                e.preventDefault();
                fileInputLabel.style.borderColor = '#dee2e6';
                fileInputLabel.style.background = 'white';
                
                const files = e.dataTransfer.files;
                this.handleFileSelection(files);
            });
        }
    }

    async loadDocumentCategories() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/document_categories`);
            if (response.ok) {
                const result = await response.json();
                this.documentCategories = result.data;
                this.updateCategorySelect();
            }
        } catch (error) {
            console.error('加载文档分类失败:', error);
        }
    }

    updateCategorySelect() {
        const categorySelect = document.getElementById('document-category');
        if (categorySelect && this.documentCategories.length > 0) {
            categorySelect.innerHTML = '';
            this.documentCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.category_name;
                categorySelect.appendChild(option);
            });
        }
    }

    async loadCustomerDetails() {
        if (!this.currentCustomer?.id) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/company/${this.currentCustomer.id}`);
            if (response.ok) {
                const result = await response.json();
                const customerData = result.data;
                
                // 更新客户信息显示
                this.updateCustomerDisplay(customerData);
                
                // 更新当前客户对象
                this.currentCustomer = { ...this.currentCustomer, ...customerData };
            }
        } catch (error) {
            console.error('加载客户详细信息失败:', error);
            this.showToast('加载客户信息失败', 'error');
        }
    }

    updateCustomerDisplay(customerData) {
        const elements = {
            'customer-name': customerData.company_name || '-',
            'customer-code': customerData.unified_social_credit_code || '-',
            'customer-address': customerData.registered_address || '-',
            'customer-legal-rep': customerData.legal_representative || '-'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    async downloadChecklist() {
        try {
            this.showLoading(true);
            
            const response = await fetch(`${this.apiBaseUrl}/api/templates/customer_engagement/checklist`);
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = '融资资料清单.docx';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                
                this.showToast('融资资料清单下载成功', 'success');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载融资资料清单失败:', error);
            this.showToast('下载失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async generateServicePlan() {
        if (!this.currentCustomer?.id) {
            this.showToast('请先选择客户', 'error');
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch(`${this.apiBaseUrl}/api/documents/generate_service_plan`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.currentCustomer.company_name}_银行服务方案.pptx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showToast('定制化服务方案生成成功', 'success');
            } else {
                throw new Error('生成失败');
            }
        } catch (error) {
            console.error('生成服务方案失败:', error);
            this.showToast('生成失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async generateCustodianshipLetter() {
        if (!this.currentCustomer?.id) {
            this.showToast('请先选择客户', 'error');
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch(`${this.apiBaseUrl}/api/documents/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id,
                    template_type: 'custodianship_letter'
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.currentCustomer.company_name}_管护权确认函.docx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                this.showToast('管护权确认函生成成功，替换内容已高亮显示', 'success');
            } else {
                throw new Error('生成失败');
            }
        } catch (error) {
            console.error('生成管护权确认函失败:', error);
            this.showToast('生成失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    handleFileSelection(files) {
        this.selectedFiles = Array.from(files);
        
        // 更新文件选择显示
        const fileInputLabel = document.querySelector('.file-input-label');
        const uploadBtn = document.getElementById('upload-btn');
        
        if (this.selectedFiles.length > 0) {
            const fileNames = this.selectedFiles.map(f => f.name).join(', ');
            fileInputLabel.querySelector('.upload-text').textContent = 
                `已选择 ${this.selectedFiles.length} 个文件: ${fileNames}`;
            uploadBtn.disabled = false;
        } else {
            fileInputLabel.querySelector('.upload-text').textContent = 
                '点击选择文件或拖拽文件到此处';
            uploadBtn.disabled = true;
        }
    }

    async uploadDocuments() {
        if (!this.currentCustomer?.id) {
            this.showToast('请先选择客户', 'error');
            return;
        }

        if (this.selectedFiles.length === 0) {
            this.showToast('请先选择文件', 'error');
            return;
        }

        try {
            this.showLoading(true);
            
            const category = document.getElementById('document-category').value;
            const description = document.getElementById('document-description').value;

            for (const file of this.selectedFiles) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('document_category', category);
                formData.append('description', description);
                formData.append('uploaded_by', 'workspace_user');

                const response = await fetch(`${this.apiBaseUrl}/api/company/${this.currentCustomer.id}/documents`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`上传文件 ${file.name} 失败`);
                }
            }

            this.showToast(`成功上传 ${this.selectedFiles.length} 个文件`, 'success');
            
            // 重置表单
            this.resetUploadForm();
            
            // 刷新文档列表
            await this.loadCustomerDocuments();
            
        } catch (error) {
            console.error('上传文档失败:', error);
            this.showToast('上传失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    resetUploadForm() {
        const fileInput = document.getElementById('file-input');
        const descriptionInput = document.getElementById('document-description');
        const uploadBtn = document.getElementById('upload-btn');
        const fileInputLabel = document.querySelector('.file-input-label');

        fileInput.value = '';
        descriptionInput.value = '';
        uploadBtn.disabled = true;
        this.selectedFiles = [];
        
        fileInputLabel.querySelector('.upload-text').textContent =
            '点击选择文件或拖拽文件到此处';
    }

    async loadCustomerDocuments() {
        if (!this.currentCustomer?.id) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/company/${this.currentCustomer.id}/documents`);
            if (response.ok) {
                const result = await response.json();
                this.updateDocumentsTable(result.data);
            }
        } catch (error) {
            console.error('加载文档列表失败:', error);
            this.showToast('加载文档列表失败', 'error');
        }
    }

    updateDocumentsTable(documents) {
        const tableBody = document.getElementById('documents-table-body');
        if (!tableBody) return;

        if (documents.length === 0) {
            tableBody.innerHTML = `
                <tr class="no-data-row">
                    <td colspan="6" class="no-data-text">暂无上传的文档</td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = documents.map(doc => `
            <tr>
                <td>${doc.original_filename}</td>
                <td><span class="category-tag">${this.getCategoryName(doc.document_category)}</span></td>
                <td><span class="file-size">${this.formatFileSize(doc.file_size)}</span></td>
                <td>${this.formatDateTime(doc.upload_date)}</td>
                <td>${doc.description || '-'}</td>
                <td>
                    <button class="doc-action-btn delete" onclick="customerEngagementManager.deleteDocument('${doc.id}')">
                        删除
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getCategoryName(categoryId) {
        const category = this.documentCategories.find(cat => cat.id === categoryId);
        return category ? category.category_name : categoryId;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    async deleteDocument(documentId) {
        if (!confirm('确定要删除这个文档吗？')) {
            return;
        }

        try {
            // 注意：这里需要实现删除API端点
            this.showToast('删除功能开发中', 'info');
        } catch (error) {
            console.error('删除文档失败:', error);
            this.showToast('删除失败: ' + error.message, 'error');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('status-toast');
        if (!toast) return;

        toast.textContent = message;
        toast.className = `status-toast ${type}`;
        toast.classList.add('show');

        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }
}

// 初始化管理器
let customerEngagementManager;
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('开始初始化客户接洽与资料准备工作台');
        customerEngagementManager = new CustomerEngagementManager();
        window.customerEngagementManager = customerEngagementManager; // 全局访问
        console.log('客户接洽与资料准备工作台初始化成功');
    } catch (error) {
        console.error('客户接洽与资料准备工作台初始化失败:', error);
    }
});
