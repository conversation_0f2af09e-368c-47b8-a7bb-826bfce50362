#!/usr/bin/env python3
"""
文档分析脚本 - 分析额度申报书和业务申报书
"""

import docx
from pathlib import Path
import sys

def extract_word_content(file_path):
    """提取Word文档内容"""
    try:
        doc = docx.Document(file_path)
        
        print(f"=== {file_path.name} 内容分析 ===")
        print(f"文档包含 {len(doc.paragraphs)} 个段落")
        print()
        
        # 提取段落内容
        content = []
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip():
                content.append(f"段落{i+1}: {paragraph.text.strip()}")
        
        # 检查表格
        if doc.tables:
            print(f"文档包含 {len(doc.tables)} 个表格")
            for table_idx, table in enumerate(doc.tables):
                print(f"\n--- 表格 {table_idx+1} ---")
                for row_idx, row in enumerate(table.rows):
                    row_data = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_data.append(cell.text.strip())
                    if row_data:
                        print(f"行{row_idx+1}: {' | '.join(row_data)}")
        
        print("\n--- 段落内容 ---")
        for line in content:
            print(line)
        print("\n" + "="*50 + "\n")
        
        return content
        
    except Exception as e:
        print(f"读取 {file_path.name} 失败: {e}")
        return []

def analyze_for_basic_info(content, filename):
    """分析内容中的基础信息"""
    print(f"=== {filename} 基础信息提取 ===")
    
    # 要查找的信息项目
    info_items = {
        "公司名称": [],
        "审批结论文号（额度）": [],
        "审批结论文号（业务）": [],
        "单户综合融资总量有效期": [],
        "金额": [],
        "担保方式": [],
        "填报日期": []
    }
    
    # 关键词匹配
    keywords = {
        "公司名称": ["公司名称", "企业名称", "客户名称", "借款人", "申请人"],
        "审批结论文号（额度）": ["审批结论文号", "额度文号", "批复文号", "审批文号"],
        "审批结论文号（业务）": ["业务文号", "业务批复", "业务审批"],
        "单户综合融资总量有效期": ["有效期", "融资总量", "综合融资", "期限"],
        "金额": ["金额", "额度", "融资金额", "贷款金额", "万元", "元"],
        "担保方式": ["担保方式", "担保", "抵押", "质押", "保证"],
        "填报日期": ["填报日期", "申报日期", "日期", "年", "月", "日"]
    }
    
    # 搜索匹配内容
    for line in content:
        line_lower = line.lower()
        for item, item_keywords in keywords.items():
            for keyword in item_keywords:
                if keyword in line:
                    info_items[item].append(line)
                    break
    
    # 输出分析结果
    for item, matches in info_items.items():
        print(f"\n{item}:")
        if matches:
            for match in matches:
                print(f"  - {match}")
        else:
            print("  - 待补充")
    
    print("\n" + "="*50 + "\n")
    return info_items

def main():
    """主函数"""
    project_root = Path('.')
    doc1_path = project_root / 'templates' / 'contract_disbursement' / '额度申报书.docx'
    doc2_path = project_root / 'templates' / 'contract_disbursement' / '业务申报书.docx'
    
    all_info = {}
    
    # 分析额度申报书
    if doc1_path.exists():
        print("正在分析额度申报书...")
        content1 = extract_word_content(doc1_path)
        all_info['额度申报书'] = analyze_for_basic_info(content1, '额度申报书')
    else:
        print(f"文件不存在: {doc1_path}")
    
    # 分析业务申报书
    if doc2_path.exists():
        print("正在分析业务申报书...")
        content2 = extract_word_content(doc2_path)
        all_info['业务申报书'] = analyze_for_basic_info(content2, '业务申报书')
    else:
        print(f"文件不存在: {doc2_path}")
    
    # 生成综合分析报告
    print("=== 综合分析报告 ===")
    print("基于《额度申报书》和《业务申报书》的信息提取结果：")
    print()
    
    # 合并信息
    final_info = {
        "公司名称": "待补充",
        "审批结论文号（额度）": "待补充",
        "审批结论文号（业务）": "待补充",
        "单户综合融资总量有效期": "待补充",
        "金额": "待补充",
        "担保方式": "待补充",
        "填报日期": "待补充"
    }
    
    # 从两个文档中提取信息
    for doc_name, doc_info in all_info.items():
        for item, matches in doc_info.items():
            if matches and matches[0] != "待补充":
                if final_info[item] == "待补充":
                    final_info[item] = f"来源：{doc_name} - {matches[0]}"
                else:
                    final_info[item] += f" | 来源：{doc_name} - {matches[0]}"
    
    print("最终基础信息清单：")
    for item, value in final_info.items():
        print(f"{item}: {value}")

if __name__ == "__main__":
    main()
