#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加四川至臻精密光学有限公司客户信息
"""

import sqlite3
import uuid
from datetime import datetime
import os

def add_zhizhen_optics_customer():
    """添加四川至臻精密光学有限公司到客户数据库"""
    
    print("🏢 添加四川至臻精密光学有限公司客户信息")
    print("=" * 60)
    
    # 数据库文件路径
    db_path = "database/enterprise_service.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 客户信息（根据实际数据库结构）
        company_data = {
            'id': str(uuid.uuid4()),
            'unified_social_credit_code': '91320582MA1MK2Q413',
            'company_name': '四川至臻精密光学有限公司',
            'registered_address': '成都市新津区普兴街道平塘东路468（工业园区）',
            'communication_address': '成都市新津区普兴街道平塘东路468（工业园区）',
            'business_description': '专用仪器制造；光学仪器制造；仪器仪表制造；工业机器人制造；光电子器件制造等',
            'legal_representative': '施春燕',
            'registration_date': '2016-04-28',
            'registered_capital': 581.6903,
            'business_scope': '专用仪器制造；光学仪器制造；仪器仪表制造；工业机器人制造；光电子器件制造；泵及真空设备销售；数控机床销售；工业自动控制系统装置销售；光学仪器销售；仪器仪表修理；玻璃、陶瓷和搪瓷制品生产专用设备制造；真空镀膜加工；光学玻璃销售；石墨及碳素制品销售；机械设备租赁；包装服务；销售代理；软件开发；软件销售；信息技术咨询服务；非居住房地产租赁；生产线管理服务；企业总部管理；技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；货物进出口；技术进出口',
            'contact_phone': '028-8259****',
            'contact_email': '<EMAIL>',
            'website': '',
            'industry_category': '光学仪器制造',
            'company_type': '有限责任公司',
            'business_status': 'active',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 检查是否已存在
        cursor.execute("""
            SELECT id, company_name FROM companies 
            WHERE unified_social_credit_code = ? OR company_name = ?
        """, (company_data['unified_social_credit_code'], company_data['company_name']))
        
        existing = cursor.fetchone()
        if existing:
            print(f"⚠️ 客户已存在:")
            print(f"   ID: {existing[0]}")
            print(f"   公司名称: {existing[1]}")
            print(f"   统一社会信用代码: {company_data['unified_social_credit_code']}")
            return existing[0]
        
        # 插入新客户（根据实际数据库结构）
        insert_sql = """
            INSERT INTO companies (
                id, unified_social_credit_code, company_name, registered_address, communication_address,
                business_description, legal_representative, registration_date, registered_capital,
                business_scope, contact_phone, contact_email, website, industry_category,
                company_type, business_status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        cursor.execute(insert_sql, (
            company_data['id'],
            company_data['unified_social_credit_code'],
            company_data['company_name'],
            company_data['registered_address'],
            company_data['communication_address'],
            company_data['business_description'],
            company_data['legal_representative'],
            company_data['registration_date'],
            company_data['registered_capital'],
            company_data['business_scope'],
            company_data['contact_phone'],
            company_data['contact_email'],
            company_data['website'],
            company_data['industry_category'],
            company_data['company_type'],
            company_data['business_status'],
            company_data['created_at'],
            company_data['updated_at']
        ))
        
        # 在这个数据库结构中，法定代表人直接存储在companies表的legal_representative字段中
        # 不需要单独的persons表和关系表
        
        # 提交事务
        conn.commit()
        
        print("✅ 客户信息添加成功!")
        print(f"   客户ID: {company_data['id']}")
        print(f"   公司名称: {company_data['company_name']}")
        print(f"   统一社会信用代码: {company_data['unified_social_credit_code']}")
        print(f"   法定代表人: {company_data['legal_representative']}")
        print(f"   注册地址: {company_data['registered_address']}")
        print(f"   联系邮箱: {company_data['contact_email']}")
        print(f"   注册资本: {company_data['registered_capital']}万元")
        
        return company_data['id']
        
    except sqlite3.IntegrityError as e:
        print(f"❌ 数据完整性错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 添加客户失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

def verify_customer_added(company_id):
    """验证客户是否成功添加"""
    
    print(f"\n🔍 验证客户添加结果...")
    
    db_path = "database/enterprise_service.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询客户信息
        cursor.execute("""
            SELECT id, company_name, unified_social_credit_code,
                   registered_address, contact_email, legal_representative,
                   registered_capital, business_status
            FROM companies
            WHERE id = ? AND business_status = 'active'
        """, (company_id,))
        
        result = cursor.fetchone()
        
        if result:
            print("✅ 客户验证成功:")
            print(f"   ID: {result[0]}")
            print(f"   公司名称: {result[1]}")
            print(f"   统一社会信用代码: {result[2]}")
            print(f"   注册地址: {result[3]}")
            print(f"   联系邮箱: {result[4]}")
            print(f"   法定代表人: {result[5]}")
            return True
        else:
            print("❌ 客户验证失败，未找到记录")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False
    finally:
        if conn:
            conn.close()

def test_api_access(company_id):
    """测试通过API访问新添加的客户"""
    
    print(f"\n🌐 测试API访问...")
    
    try:
        import requests
        
        # 测试获取客户详情
        response = requests.get(f"http://localhost:5000/api/company/{company_id}", timeout=10)
        
        if response.status_code == 200:
            company_data = response.json()["data"]
            print("✅ API访问成功:")
            print(f"   公司名称: {company_data.get('company_name')}")
            print(f"   法定代表人: {company_data.get('legal_representative')}")
            print(f"   注册地址: {company_data.get('registered_address')}")
            return True
        else:
            print(f"❌ API访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️ API测试跳过: {e}")
        print("   (这是正常的，如果API服务未运行)")
        return True

if __name__ == "__main__":
    print("🚀 开始添加四川至臻精密光学有限公司")
    
    # 添加客户
    company_id = add_zhizhen_optics_customer()
    
    if company_id:
        # 验证添加结果
        verify_success = verify_customer_added(company_id)
        
        if verify_success:
            # 测试API访问
            test_api_access(company_id)
            
            print("\n" + "=" * 60)
            print("🎉 四川至臻精密光学有限公司添加完成!")
            print("=" * 60)
            print("📊 客户信息摘要:")
            print("   • 公司名称: 四川至臻精密光学有限公司")
            print("   • 统一社会信用代码: 91320582MA1MK2Q413")
            print("   • 法定代表人: 施春燕")
            print("   • 注册地址: 成都市新津区普兴街道平塘东路468（工业园区）")
            print("   • 客户ID: " + company_id)
            print()
            print("🎯 现在可以在系统中使用此客户:")
            print("   • 统一驾驶舱选择客户")
            print("   • 生成管护权确认函")
            print("   • 生成定制化服务方案")
            print("   • 上传客户资料文档")
            print("=" * 60)
        else:
            print("❌ 客户添加验证失败")
    else:
        print("❌ 客户添加失败")
