#!/usr/bin/env python3
"""
寻找贷款条件表格
"""

import docx
from pathlib import Path

def find_loan_conditions():
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    print('=== 寻找贷款条件表格 ===\n')
    
    if not business_file.exists():
        print('业务申报书文件不存在')
        return
    
    doc = docx.Document(business_file)
    print(f'业务申报书包含 {len(doc.tables)} 个表格\n')
    
    # 寻找包含编号列表的表格（1. 2. 3. 4. 5.）
    for table_idx, table in enumerate(doc.tables):
        table_text = ""
        has_numbered_items = False
        
        for row in table.rows:
            for cell in row.cells:
                cell_text = cell.text.strip()
                table_text += cell_text + " "
                
                # 检查是否包含编号项目
                if ("1." in cell_text and "2." in cell_text and "3." in cell_text and 
                    "4." in cell_text and "5." in cell_text):
                    has_numbered_items = True
        
        # 如果包含编号项目，显示这个表格
        if has_numbered_items:
            print(f'✅ 找到可能的贷款条件表格{table_idx+1}')
            print(f'表格{table_idx+1}完整内容：')
            
            for row_idx, row in enumerate(table.rows):
                print(f'\n  行{row_idx+1}:')
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f'    列{cell_idx+1}: {cell_text}')
            
            print('\n' + '-'*60)
    
    # 也搜索包含"担保专营条件"的表格
    print('\n🔍 搜索包含"担保专营条件"的表格：')
    for table_idx, table in enumerate(doc.tables):
        table_text = ""
        for row in table.rows:
            for cell in row.cells:
                table_text += cell.text.strip() + " "
        
        if "担保专营条件" in table_text or "支付方式条件" in table_text:
            print(f'✅ 找到表格{table_idx+1}')
            print(f'表格{table_idx+1}完整内容：')
            
            for row_idx, row in enumerate(table.rows):
                print(f'\n  行{row_idx+1}:')
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f'    列{cell_idx+1}: {cell_text}')
            
            print('\n' + '-'*60)

if __name__ == "__main__":
    find_loan_conditions()
