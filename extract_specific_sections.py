#!/usr/bin/env python3
"""
精确提取落实情况表所需的三个部分内容
"""

import docx
from pathlib import Path

def extract_sections():
    # 文件路径
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    print('=== 提取落实情况表所需的三个部分 ===\n')
    
    # 第一部分：额度申报书中的使用前提条件
    print('一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况')
    print('=' * 60)
    extract_quota_preconditions(quota_file)
    
    print('\n' + '=' * 80 + '\n')
    
    # 第二部分：额度申报书中的持续条件
    print('二、单户综合融资总量方案申报书中列明的持续条件及落实情况')
    print('=' * 60)
    extract_quota_continuous_conditions(quota_file)
    
    print('\n' + '=' * 80 + '\n')
    
    # 第三部分：业务申报书中的贷款条件
    print('三、单笔业务申报书中列明的贷款条件及落实情况')
    print('=' * 60)
    extract_business_loan_conditions(business_file)

def extract_quota_preconditions(file_path):
    """提取额度申报书中的使用前提条件"""
    if not file_path.exists():
        print('额度申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    
    print('【从额度申报书中查找"使用前提条件"相关内容】\n')
    
    # 搜索关键词
    keywords = ['使用前提条件', '用信前提', '前提条件']
    
    # 在段落中搜索
    found_content = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if any(keyword in text for keyword in keywords):
            found_content.append(f'段落{i+1}: {text}')
    
    # 在表格中搜索
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if any(keyword in text for keyword in keywords):
                    found_content.append(f'表格{table_idx+1}-行{row_idx+1}-列{cell_idx+1}: {text}')
    
    if found_content:
        for content in found_content:
            print(content)
            print('-' * 40)
    else:
        print('未找到使用前提条件相关内容')

def extract_quota_continuous_conditions(file_path):
    """提取额度申报书中的持续条件"""
    if not file_path.exists():
        print('额度申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    
    print('【从额度申报书中查找"持续条件"相关内容】\n')
    
    # 搜索关键词
    keywords = ['持续条件', '用信持续', '单户信用额度使用持续条件']
    
    # 在段落中搜索
    found_content = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if any(keyword in text for keyword in keywords):
            found_content.append(f'段落{i+1}: {text}')
    
    # 在表格中搜索
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if any(keyword in text for keyword in keywords):
                    found_content.append(f'表格{table_idx+1}-行{row_idx+1}-列{cell_idx+1}: {text}')
    
    if found_content:
        for content in found_content:
            print(content)
            print('-' * 40)
    else:
        print('未找到持续条件相关内容')

def extract_business_loan_conditions(file_path):
    """提取业务申报书中的贷款条件"""
    if not file_path.exists():
        print('业务申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    
    print('【从业务申报书中查找"贷款条件"相关内容】\n')
    
    # 搜索关键词
    keywords = ['贷款条件', '放款条件', '业务条件', '条件']
    
    # 在段落中搜索
    found_content = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if any(keyword in text for keyword in keywords) and len(text) > 10:  # 过滤太短的内容
            found_content.append(f'段落{i+1}: {text}')
    
    # 在表格中搜索
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if any(keyword in text for keyword in keywords) and len(text) > 10:
                    found_content.append(f'表格{table_idx+1}-行{row_idx+1}-列{cell_idx+1}: {text}')
    
    if found_content:
        for content in found_content:
            print(content)
            print('-' * 40)
    else:
        print('未找到贷款条件相关内容')

if __name__ == "__main__":
    extract_sections()
