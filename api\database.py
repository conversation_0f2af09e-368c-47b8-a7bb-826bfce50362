"""
数据库连接和配置模块
提供PostgreSQL数据库连接池和基础操作
"""

import os
import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor
import logging
from contextlib import contextmanager
from typing import Dict, Any, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        # 加载环境变量
        from dotenv import load_dotenv
        from pathlib import Path
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            load_dotenv(env_file, encoding='utf-8')

        # 从环境变量读取数据库配置，提供默认值
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'enterprise_service')
        self.user = os.getenv('DB_USER', 'postgres')
        self.password = os.getenv('DB_PASSWORD', 'password')
        
        # 连接池配置
        self.min_connections = int(os.getenv('DB_MIN_CONN', '1'))
        self.max_connections = int(os.getenv('DB_MAX_CONN', '20'))
    
    def get_connection_string(self) -> str:
        """获取数据库连接字符串"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection_pool = None
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                self.config.min_connections,
                self.config.max_connections,
                host=self.config.host,
                port=self.config.port,
                database=self.config.database,
                user=self.config.user,
                password=self.config.password,
                cursor_factory=RealDictCursor
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    @contextmanager
    def get_cursor(self, commit=True):
        """获取数据库游标的上下文管理器"""
        with self.get_connection() as connection:
            cursor = connection.cursor()
            try:
                yield cursor
                if commit:
                    connection.commit()
            except Exception as e:
                connection.rollback()
                logger.error(f"数据库操作错误: {e}")
                raise
            finally:
                cursor.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        with self.get_cursor(commit=False) as cursor:
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_single(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行查询并返回单个结果"""
        with self.get_cursor(commit=False) as cursor:
            cursor.execute(query, params)
            # 检查是否是DDL语句（不返回结果）
            if query.strip().upper().startswith(('ALTER', 'CREATE', 'DROP', 'TRUNCATE')):
                return None
            return cursor.fetchone()

    def execute_ddl(self, query: str, params: tuple = None) -> bool:
        """执行DDL语句（ALTER, CREATE, DROP等）和DML语句（UPDATE, INSERT, DELETE等）"""
        try:
            with self.get_cursor(commit=True) as cursor:
                cursor.execute(query, params)
                return True
        except Exception as e:
            logger.error(f"SQL执行失败: {e}")
            return False
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        with self.get_cursor(commit=True) as cursor:
            cursor.execute(query, params)
            return cursor.rowcount
    
    def execute_transaction(self, operations: List[tuple]) -> bool:
        """执行事务操作"""
        with self.get_connection() as connection:
            cursor = connection.cursor()
            try:
                for query, params in operations:
                    cursor.execute(query, params)
                connection.commit()
                logger.info(f"事务执行成功，包含 {len(operations)} 个操作")
                return True
            except Exception as e:
                connection.rollback()
                logger.error(f"事务执行失败: {e}")
                raise
            finally:
                cursor.close()
    
    def close_pool(self):
        """关闭连接池"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("数据库连接池已关闭")

# 全局数据库管理器实例
db_config = DatabaseConfig()
db_manager = DatabaseManager(db_config)
