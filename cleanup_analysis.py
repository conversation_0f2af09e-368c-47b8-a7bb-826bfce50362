#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件清理分析脚本
分析项目中的文件，识别核心功能文件和可删除文件
"""

import os
from pathlib import Path

def analyze_files():
    """分析文件并分类"""
    
    # 核心功能模块 - 必须保留
    core_modules = {
        'api/': '后端API服务',
        'frontend/': '前端界面',
        'database/': '数据库脚本',
        'templates/': '业务模板',
        'deposit_services/': '协定存款业务模块',
        'contract_disbursement/': '合同放款模块',
        'services/': '核心服务',
        'utils/': '工具函数',
        'config/': '配置文件',
        'logs/': '日志目录',
        'test_output/': '测试输出',
        'uploads/': '上传文件',
        'temp/': '临时文件'
    }
    
    # 核心功能文件 - 必须保留
    core_files = {
        'main.py': '主程序入口',
        'run_api.py': 'API启动脚本',
        'start_system.py': '系统启动脚本',
        'requirements.txt': 'Python依赖',
        'DATABASE_OPERATIONS_GUIDE.md': '数据库操作指南',
        'DEPLOYMENT_GUIDE.md': '部署指南',
        'docs/': '文档目录'
    }
    
    # 可能需要删除的文件类型
    deletable_patterns = [
        # 测试和调试文件
        'test_*.py',
        'debug_*.py',
        'check_*.py',
        'verify_*.py',
        'analyze_*.py',
        
        # 临时和实验文件
        'temp_*.py',
        'simple_*.py',
        'direct_*.py',
        'interactive_*.py',
        
        # 特定功能的旧版本文件
        'enhanced_*.py',
        'expert_*.py',
        'final_*.py',
        'precise_*.py',
        'comprehensive_*.py',
        'detailed_*.py',
        'exact_*.py',
        'clean_*.py',
        'improved_*.py',
        
        # 数据处理和分析文件
        'extract_*.py',
        'insert_*.py',
        'update_*.py',
        'extend_*.py',
        'fix_*.py',
        'replace_*.py',
        'find_*.py',
        'read_*.py',
        'generate_*.py',
        'create_*.py',
        'add_*.py',
        
        # 条件和映射相关
        'condition_*.py',
        'mapping_*.py',
        'field_*.py',
        'standardized_*.py',
        
        # 特定公司数据文件
        '*shenguang*.py',
        '*zhizhen*.py',
        '*zkzr*.py',
        '*卫讯*.py'
    ]
    
    # 扫描所有文件
    all_files = []
    for root, dirs, files in os.walk('.'):
        # 跳过隐藏目录和__pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if not file.startswith('.') and not file.endswith('.pyc'):
                file_path = os.path.join(root, file).replace('\\', '/')
                all_files.append(file_path)
    
    # 分类文件
    keep_files = []
    delete_files = []
    
    for file_path in all_files:
        should_keep = False
        
        # 检查是否在核心模块目录中
        for core_dir in core_modules:
            if file_path.startswith(core_dir):
                should_keep = True
                break
        
        # 检查是否是核心文件
        file_name = os.path.basename(file_path)
        if file_name in core_files or file_path in core_files:
            should_keep = True
        
        # 检查是否匹配删除模式
        if not should_keep:
            for pattern in deletable_patterns:
                if pattern.endswith('*'):
                    if file_name.startswith(pattern[:-1]):
                        break
                elif pattern.startswith('*'):
                    if file_name.endswith(pattern[1:]):
                        break
                elif '*' in pattern:
                    prefix, suffix = pattern.split('*', 1)
                    if file_name.startswith(prefix) and file_name.endswith(suffix):
                        break
                elif file_name == pattern:
                    break
            else:
                should_keep = True  # 如果没有匹配删除模式，则保留
        
        if should_keep:
            keep_files.append(file_path)
        else:
            delete_files.append(file_path)
    
    return keep_files, delete_files

def print_analysis():
    """打印分析结果"""
    keep_files, delete_files = analyze_files()
    
    print("=" * 80)
    print("文件清理分析报告")
    print("=" * 80)
    
    print(f"\n📁 保留文件 ({len(keep_files)}个):")
    print("-" * 50)
    for file in sorted(keep_files):
        print(f"  ✅ {file}")
    
    print(f"\n🗑️ 建议删除文件 ({len(delete_files)}个):")
    print("-" * 50)
    for file in sorted(delete_files):
        print(f"  ❌ {file}")
    
    print(f"\n📊 统计:")
    print(f"  总文件数: {len(keep_files) + len(delete_files)}")
    print(f"  保留文件: {len(keep_files)}")
    print(f"  删除文件: {len(delete_files)}")
    print(f"  清理比例: {len(delete_files)/(len(keep_files) + len(delete_files))*100:.1f}%")

if __name__ == "__main__":
    print_analysis()
