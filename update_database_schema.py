#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新数据库结构，添加新字段
"""

import sqlite3
from pathlib import Path

def update_database_schema():
    """更新数据库结构"""
    project_root = Path(__file__).parent
    db_path = project_root / "database" / "enterprise_service.db"
    
    print("🔧 更新数据库结构...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 添加新字段
        new_columns = [
            ("spouse_name", "TEXT"),
            ("environmental_classification", "TEXT")
        ]
        
        for column_name, column_type in new_columns:
            try:
                cursor.execute(f"ALTER TABLE companies ADD COLUMN {column_name} {column_type}")
                print(f"   ✅ 添加字段: {column_name}")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e):
                    print(f"   ⚠️ 字段已存在: {column_name}")
                else:
                    print(f"   ❌ 添加字段失败 {column_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库结构更新完成")
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")

if __name__ == "__main__":
    update_database_schema()
