#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新成都中科卓尔智能科技集团有限公司的数据
"""

import sqlite3
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ZKZRDataUpdater:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
    def update_zkzr_data(self):
        """更新中科卓尔数据"""
        logger.info("🔧 开始更新中科卓尔数据...")
        
        if not self.db_path.exists():
            logger.error(f"❌ 数据库文件不存在: {self.db_path}")
            return False
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 中科卓尔的ID
            zkzr_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
            
            # 更新数据，添加账户号
            update_data = {
                "registration_date": "2018-06-13",
                "registered_capital": 709.5823,  # 万元
                "business_scope": "一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；光学玻璃制造【分支机构经营】；半导体器件专用设备制造【分支机构经营】等",
                "finance_manager_name": "苏宇",
                "finance_manager_phone": "***********",
                "total_assets": 13688.47,  # 万元
                "total_liabilities": 8896.80,  # 万元
                "business_description": "中科卓尔的产品和服务主要包括，精密光学仪器设备、精密光学加工服务及组件销售和光掩模基板，精密光学仪器主要解决科研院所等小批量中高端定制化的需求，精密光学加工服务主要服务于大科学装置、激光武器以及卫星的星间激光通讯等领域，光掩膜基板开始逐步小批量进入下游市场。",
                "registered_address": "中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区8楼",
                "spouse_name": "王斯颖",
                "environmental_classification": "ESG绿色",
                "company_account": "51050148850800008651"  # 统一账户号
            }
            
            # 构建更新SQL
            set_clause = ", ".join([f"{key} = ?" for key in update_data.keys()])
            values = list(update_data.values())
            values.append(zkzr_id)  # WHERE条件的值
            
            cursor.execute(f"""
                UPDATE companies 
                SET {set_clause}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, values)
            
            affected_rows = cursor.rowcount
            if affected_rows > 0:
                logger.info(f"   ✅ 成功更新中科卓尔数据，影响行数: {affected_rows}")
                
                # 验证更新结果
                self._verify_update(cursor, zkzr_id)
                
                conn.commit()
                logger.info("✅ 数据库更新完成")
                return True
            else:
                logger.warning(f"   ⚠️ 未找到中科卓尔记录，ID: {zkzr_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据库更新失败: {e}")
            return False
        finally:
            conn.close()
    
    def _verify_update(self, cursor, zkzr_id):
        """验证更新结果"""
        logger.info("🔍 验证更新结果...")
        
        cursor.execute("""
            SELECT 
                company_name, registration_date, registered_capital, business_scope,
                finance_manager_name, finance_manager_phone, total_assets, total_liabilities,
                business_description, registered_address, legal_representative, contact_phone
            FROM companies 
            WHERE id = ?
        """, (zkzr_id,))
        
        result = cursor.fetchone()
        if result:
            logger.info("   📊 中科卓尔更新后数据:")
            fields = [
                "企业名称", "注册时间", "注册资本", "经营范围",
                "财务主管", "财务主管电话", "资产总额", "负债总额",
                "主营业务描述", "注册地址", "法定代表人", "联系电话"
            ]
            for i, (field, value) in enumerate(zip(fields, result)):
                if isinstance(value, str) and len(value) > 80:
                    display_value = value[:80] + "..."
                else:
                    display_value = value
                logger.info(f"      {field}: {display_value}")
        else:
            logger.warning("   ⚠️ 验证失败：未找到更新后的数据")

def main():
    """主函数"""
    print("🚀 更新成都中科卓尔智能科技集团有限公司数据")
    print("="*60)
    
    updater = ZKZRDataUpdater()
    success = updater.update_zkzr_data()
    
    print("="*60)
    if success:
        print("✅ 中科卓尔数据更新成功！")
        print("📋 更新内容:")
        print("   - 注册时间: 2018-06-13")
        print("   - 注册资本: 709.5823万元")
        print("   - 经营范围: 技术服务、技术开发...")
        print("   - 财务主管: 苏宇 (***********)")
        print("   - 资产总额: 13688.47万元")
        print("   - 负债总额: 8896.80万元")
        print("   - 注册地址: 已更新为B区8楼")
        print("   - 主营业务描述: 已更新")
        print("\n🎉 现在可以全自动生成中科卓尔的信贷业务申请书了！")
    else:
        print("❌ 数据更新失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
