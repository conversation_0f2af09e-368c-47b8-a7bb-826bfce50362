# 字段词典配置使用指南

## 📋 概述

`field_mapping.json` 是企业服务文档生成系统的核心配置文件，定义了所有业务字段的属性、验证规则、数据来源和格式要求。

## 🏗️ 配置结构

### 1. 字段词典 (field_dictionary)

每个字段包含以下属性：

```json
{
  "field": "字段英文名",
  "label": "中文标签名", 
  "description": "字段含义说明",
  "required": true/false,
  "type": "字段类型",
  "format": "格式规则",
  "source": "数据来源",
  "placeholders": ["模板占位符列表"],
  "validation": "验证规则",
  "examples": ["示例值列表"]
}
```

### 2. 字段类型 (type)

| 类型 | 说明 | 示例 |
|------|------|------|
| `text` | 文本类型 | 公司名称、地址 |
| `money` | 金额类型 | 存款金额、贷款金额 |
| `date` | 日期类型 | 协议日期、接洽日期 |
| `number` | 数字类型 | 利率调整、贷款期限 |
| `percent` | 百分比类型 | 贷款利率 |
| `enum` | 枚举类型 | 担保方式 |

### 3. 数据来源 (source)

| 来源 | 说明 | 可靠性 |
|------|------|--------|
| `database` | 从企业数据库获取 | 高 |
| `user_input` | 用户手动输入 | 中 |
| `ai_generated` | AI智能生成 | 中 |

## 🔧 使用方法

### 在Python代码中使用

```python
import json
from pathlib import Path

# 加载字段配置
def load_field_config():
    config_path = Path("config/field_mapping.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# 获取字段信息
def get_field_info(field_name):
    config = load_field_config()
    return config['field_dictionary'].get(field_name)

# 验证字段值
def validate_field(field_name, value):
    field_info = get_field_info(field_name)
    if not field_info:
        return False, "字段不存在"
    
    validation = field_info.get('validation', {})
    
    # 检查必填字段
    if field_info.get('required') and not value:
        return False, f"{field_info['label']}为必填字段"
    
    # 检查长度
    if 'min_length' in validation and len(str(value)) < validation['min_length']:
        return False, f"{field_info['label']}长度不能少于{validation['min_length']}字符"
    
    return True, "验证通过"
```

### 字段替换示例

```python
def replace_template_fields(doc, company_data):
    config = load_field_config()
    
    for field_name, field_value in company_data.items():
        field_info = config['field_dictionary'].get(field_name)
        if field_info:
            placeholders = field_info.get('placeholders', [])
            for placeholder in placeholders:
                # 在文档中替换占位符
                replace_text_in_document(doc, placeholder, field_value)
```

## 🎯 AI集成配置

### 自动填充字段
```python
# 这些字段可以由AI自动填充
auto_fill_fields = [
    "deposit_amount",      # 根据企业规模推荐存款额度
    "interest_rate_adjustment",  # 根据企业等级推荐利率调整
    "agreement_number",    # 自动生成协议编号
    "current_date"         # 系统当前日期
]
```

### 智能建议字段
```python
# 这些字段AI可以提供建议，但需要用户确认
suggest_fields = [
    "loan_purpose",        # 根据企业行业推荐贷款用途
    "repayment_source",    # 根据企业财务状况推荐还款来源
    "service_plan"         # 根据企业需求推荐服务方案
]
```

## 📝 字段扩展指南

### 添加新字段

1. 在 `field_dictionary` 中添加字段定义
2. 在相应的 `field_categories` 中分类
3. 在 `data_sources` 中指定数据来源
4. 更新模板文件中的占位符

示例：
```json
{
  "business_license_number": {
    "field": "business_license_number",
    "label": "营业执照号",
    "description": "企业营业执照注册号",
    "required": false,
    "type": "text",
    "format": "max_length:50",
    "source": "database",
    "placeholders": ["【营业执照号】", "【执照号码】"],
    "validation": {
      "pattern": "^\\d{15}$|^\\d{18}$"
    },
    "examples": ["123456789012345", "123456789012345678"]
  }
}
```

## 🔍 常见问题

### Q: 如何处理缺失字段？
A: 根据 `template_integration.missing_field_handling` 配置：
- 必填字段缺失：抛出错误
- 可选字段缺失：显示警告
- 保留原占位符：便于后续手动填写

### Q: 如何自定义字段格式？
A: 在字段的 `format` 属性中指定：
- `currency:CNY,unit:万元` - 货币格式
- `YYYY年MM月DD日` - 日期格式
- `decimal:2,unit:%` - 百分比格式

### Q: 如何添加新的占位符模式？
A: 在 `placeholder_patterns` 中添加新模式，然后在字段的 `placeholders` 数组中使用。

## 🚀 最佳实践

1. **字段命名**: 使用英文下划线命名，保持一致性
2. **验证规则**: 为重要字段添加完整的验证规则
3. **示例值**: 提供真实的示例值，便于理解
4. **占位符**: 支持多种占位符格式，提高模板兼容性
5. **分类管理**: 按业务模块对字段进行分类
6. **版本控制**: 修改配置时更新版本号和修改日期

## 📊 字段统计

当前配置包含：
- **基础信息字段**: 8个（企业基本信息）
- **协定存款字段**: 5个（存款业务相关）
- **合同放款字段**: 6个（放款业务相关）
- **客户接洽字段**: 4个（客户服务相关）
- **系统字段**: 1个（系统生成）

总计：**24个业务字段**，覆盖企业服务的主要业务场景。
