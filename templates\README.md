# 模板档案馆 (Templates Archive)

## 概述
这是企业信息核心库系统的统一模板档案馆，用于存放所有业务模块的标准化模板、文档和资产文件。

## 设计原则
- **模块化组织**: 每个业务模块拥有独立的分馆目录
- **标准化结构**: 所有分馆遵循统一的目录结构规范
- **版本管理**: 支持模板的版本控制和历史追踪
- **可扩展性**: 为未来新增业务模块预留充足空间

## 目录结构规范

```
templates/
├── README.md                    # 档案馆总说明
├── {business_module_1}/         # 业务模块1分馆
│   ├── README.md               # 模块说明文档
│   ├── documents/              # 文档模板
│   │   ├── contracts/          # 合同模板
│   │   ├── applications/       # 申请表模板
│   │   └── reports/            # 报告模板
│   ├── forms/                  # 表单模板
│   ├── workflows/              # 业务流程模板
│   ├── configs/                # 配置文件模板
│   └── assets/                 # 静态资源
│       ├── images/             # 图片资源
│       ├── styles/             # 样式文件
│       └── scripts/            # 脚本文件
└── {business_module_2}/         # 业务模块2分馆
    └── ...
```

## 当前业务模块

### 1. 银企直联 (yingqi_zhilian)
- **路径**: `templates/yingqi_zhilian/`
- **用途**: 存放银企直联业务相关的所有模板和资产
- **状态**: 已创建，等待接收业务资产文件

## 使用指南

### 添加新业务模块
1. 在 `templates/` 目录下创建新的业务模块文件夹
2. 按照标准结构创建子目录
3. 添加模块专属的 README.md 说明文档
4. 更新本文档的业务模块列表

### 模板文件命名规范
- 使用小写字母和下划线
- 包含版本号（如适用）
- 示例：`bank_connect_application_v1.0.docx`

### 版本管理
- 重要模板变更时创建新版本
- 保留历史版本以确保向后兼容
- 在模块README中记录版本变更历史

## 最佳实践

1. **文档化**: 每个模板都应有清晰的说明文档
2. **标准化**: 遵循企业统一的格式和样式规范
3. **可维护性**: 定期审查和更新模板内容
4. **安全性**: 敏感信息使用占位符，避免硬编码

## 扩展计划

未来可能添加的业务模块分馆：
- 信贷管理 (xindai_guanli)
- 风险控制 (fengxian_kongzhi)
- 客户关系 (kehu_guanxi)
- 财务管理 (caiwu_guanli)
- 合规审计 (heguei_shenji)

## 维护说明

- **负责人**: 系统架构团队
- **更新频率**: 根据业务需求
- **审核流程**: 所有模板变更需经过业务部门确认
- **备份策略**: 定期备份重要模板文件

---

**模板档案馆** - 为企业业务模块提供标准化的模板资产管理 📚✨
