#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取条件落实情况表并识别标黄部分
支持老版Word格式(.doc)
"""

import os
from pathlib import Path
import logging
import subprocess
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConditionChecklistReader:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.file_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.doc"
        
    def read_and_analyze(self):
        """读取并分析条件落实情况表"""
        logger.info("📖 开始读取条件落实情况表...")
        
        if not self.file_path.exists():
            logger.error(f"❌ 文件不存在: {self.file_path}")
            return
        
        print("\n" + "="*80)
        print("📋 条件落实情况表内容分析")
        print("="*80)
        print(f"📁 文件路径: {self.file_path}")
        print(f"📄 文件格式: {self.file_path.suffix.upper()}")
        print(f"📊 文件大小: {self.file_path.stat().st_size:,} 字节")
        
        # 由于是.doc格式，我们需要使用不同的方法来读取
        try:
            # 方法1: 尝试使用python-docx读取（可能不支持.doc）
            content_info = self._try_read_with_docx()
            if content_info:
                self._display_content_info(content_info)
                return
            
            # 方法2: 尝试转换为文本格式读取
            text_content = self._try_convert_to_text()
            if text_content:
                self._analyze_text_content(text_content)
                return
            
            # 方法3: 提供手动分析建议
            self._provide_manual_analysis_guide()
            
        except Exception as e:
            logger.error(f"❌ 读取文件失败: {e}")
            self._provide_manual_analysis_guide()
    
    def _try_read_with_docx(self):
        """尝试使用python-docx读取"""
        try:
            import docx
            # python-docx通常不支持.doc格式，只支持.docx
            logger.info("⚠️ python-docx不支持.doc格式，需要其他方法")
            return None
        except ImportError:
            logger.info("⚠️ python-docx未安装")
            return None
        except Exception as e:
            logger.info(f"⚠️ python-docx读取失败: {e}")
            return None
    
    def _try_convert_to_text(self):
        """尝试转换为文本格式"""
        try:
            # 尝试使用antiword（如果安装了）
            result = subprocess.run(['antiword', str(self.file_path)], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                logger.info("✅ 使用antiword成功转换")
                return result.stdout
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            logger.info("⚠️ antiword不可用")
        
        try:
            # 尝试使用catdoc（如果安装了）
            result = subprocess.run(['catdoc', str(self.file_path)], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                logger.info("✅ 使用catdoc成功转换")
                return result.stdout
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            logger.info("⚠️ catdoc不可用")
        
        return None
    
    def _analyze_text_content(self, text_content):
        """分析文本内容"""
        print("\n📝 文档文本内容分析:")
        print("-" * 60)
        
        lines = text_content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        print(f"   📊 总行数: {len(lines)}")
        print(f"   📊 非空行数: {len(non_empty_lines)}")
        
        print(f"\n   📝 内容预览 (前20行):")
        for i, line in enumerate(non_empty_lines[:20], 1):
            if len(line) > 100:
                display_line = line[:100] + "..."
            else:
                display_line = line
            print(f"      {i:2d}. {display_line}")
        
        if len(non_empty_lines) > 20:
            print(f"      ... 还有 {len(non_empty_lines) - 20} 行")
        
        # 尝试识别可能的字段
        self._identify_potential_fields(non_empty_lines)
    
    def _identify_potential_fields(self, lines):
        """识别可能的字段"""
        print(f"\n   🔍 字段识别分析:")
        
        # 常见的字段关键词
        field_keywords = [
            "企业名称", "公司名称", "借款人", "申请人",
            "统一社会信用代码", "营业执照", "注册号",
            "法定代表人", "法人代表", "负责人",
            "注册资本", "资本金", "注册时间", "成立时间",
            "联系电话", "电话", "手机",
            "地址", "注册地址", "经营地址",
            "贷款金额", "借款金额", "申请金额",
            "贷款期限", "借款期限", "期限",
            "贷款用途", "借款用途", "资金用途",
            "担保方式", "抵押", "质押", "保证",
            "财务主管", "财务负责人", "会计",
            "资产", "负债", "净资产"
        ]
        
        found_fields = []
        for line in lines:
            for keyword in field_keywords:
                if keyword in line:
                    found_fields.append((keyword, line))
                    break
        
        if found_fields:
            print("      发现可能的字段:")
            for keyword, line in found_fields[:15]:  # 显示前15个
                display_line = line[:80] + "..." if len(line) > 80 else line
                print(f"         {keyword}: {display_line}")
            
            if len(found_fields) > 15:
                print(f"         ... 还有 {len(found_fields) - 15} 个字段")
        else:
            print("      未识别到明显的字段关键词")
    
    def _provide_manual_analysis_guide(self):
        """提供手动分析指南"""
        print("\n📋 手动分析指南:")
        print("-" * 60)
        print("由于.doc格式的限制，建议您：")
        print()
        print("1. 📂 在Word中打开文件:")
        print(f"   {self.file_path}")
        print()
        print("2. 🎨 查找标黄部分:")
        print("   - 查看文档中所有黄色高亮的文本")
        print("   - 记录每个标黄字段的内容和位置")
        print()
        print("3. 📝 常见的需要填充字段可能包括:")
        print("   - 企业基本信息（名称、代码、法人等）")
        print("   - 贷款信息（金额、期限、用途等）")
        print("   - 担保信息（担保方式、担保物等）")
        print("   - 财务信息（资产、负债等）")
        print("   - 日期信息（申请日期、审批日期等）")
        print()
        print("4. 🔄 转换建议:")
        print("   - 可以将.doc文件另存为.docx格式")
        print("   - 这样可以使用更好的自动化工具分析")
        print()
        print("5. 📋 请提供标黄字段清单:")
        print("   - 字段名称")
        print("   - 字段位置描述")
        print("   - 当前的示例内容")
    
    def _display_content_info(self, content_info):
        """显示内容信息"""
        print(f"\n📄 文档结构:")
        print(f"   段落数: {content_info.get('paragraphs', 0)}")
        print(f"   表格数: {content_info.get('tables', 0)}")
        
        if content_info.get('highlighted_text'):
            print(f"\n🎨 发现标黄内容:")
            for i, text in enumerate(content_info['highlighted_text'][:10], 1):
                print(f"   {i}. {text}")

def main():
    reader = ConditionChecklistReader()
    reader.read_and_analyze()

if __name__ == "__main__":
    main()
