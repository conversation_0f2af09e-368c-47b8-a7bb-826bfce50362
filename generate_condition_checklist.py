#!/usr/bin/env python3
"""
生成落实情况表
基于提取的原文内容和数据库信息生成完整的落实情况表
"""

import docx
from pathlib import Path
import sqlite3
from datetime import datetime
from docx.shared import RGBColor

class ConditionChecklistGenerator:
    def __init__(self):
        self.template_path = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
        self.db_path = Path('database/enterprise_service.db')
        
        # 从文档中提取的原文内容
        self.extracted_data = {
            'company_name': '成都中科卓尔智能科技集团有限公司',
            'credit_limit': '4000万元',
            'business_amount': '1300万元',
            'business_period': '13个月',

            # 一、用信前提条件（从额度申报书提取）
            'quota_preconditions': '本次新增贷款的最终支用日不晚于2025年9月30日。',

            # 二、持续条件（从额度申报书表格20提取的完整原文）
            'quota_continuous_conditions': '''在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合《关于印发<科技企业创新能力评价体系应用推广方案>的通知》（建总函〔2021〕758号文件管理要求的在我行科创评级"T5"及以上。若突破用信持续条件，将报有权审批机构变更方案或重检。

在我行单户综合融资总量有效期内，分阶段调整：
1. A+轮融资到账前（公司2025年最新一轮融资到账前）：不高于75%
2. A+轮融资到账后（公司2025年最新一轮融资到账后）：不高于65%。
若突破该条件，将报有权审批机构变更方案或重检。

在我行单户综合融资总量有效期内，分阶段调整：
1. A+轮融资到账前（公司2025年最新一轮融资到账前）：单一口径流动比率不低于1。
2.A+轮融资到账后（公司2025年最新一轮融资到账前后：单一口径流动比率不低于1.1。
若突破该条件，将报有权审批机构变更方案或重检。

在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。若对外担保须征得我行同意，若突破持续条件我行将报有权审批机构变更方案或重检。

在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行，若突破持续性条件，我行将报有权审批机构变更方案或重检。''',

            # 三、业务贷款条件（从业务申报书表格7提取的完整原文）
            'business_loan_conditions': '''贸易背景条件：作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。

存量压缩条件（如有）：/'''
        }
    
    def get_company_data(self):
        """从数据库获取企业基础信息"""
        if not self.db_path.exists():
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM companies WHERE company_name LIKE '%中科卓尔%'")
            company_info = cursor.fetchone()
            
            if company_info:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, company_info))
        except Exception as e:
            print(f'数据库查询错误: {e}')
        finally:
            conn.close()
        
        return None
    
    def generate_checklist(self, output_path=None):
        """生成落实情况表"""
        if not self.template_path.exists():
            print('模板文件不存在')
            return False
        
        # 获取企业数据
        company_data = self.get_company_data()
        
        # 加载模板
        doc = docx.Document(self.template_path)
        
        # 获取表格（模板中只有一个表格）
        if not doc.tables:
            print('模板中没有找到表格')
            return False
        
        table = doc.tables[0]
        
        try:
            # 填充基础信息和三个核心内容部分
            self.fill_basic_info_only(table, company_data)
            self.fill_three_core_sections(table)
            
            # 生成输出文件
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = f'test_output/落实情况表_{self.extracted_data["company_name"]}_{timestamp}.docx'

            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文档
            doc.save(output_path)
            print(f'落实情况表已生成: {output_path}')
            return True
            
        except Exception as e:
            print(f'生成失败: {e}')
            return False
    
    def fill_basic_info_only(self, table, company_data):
        """只填充确认的基础信息，其他内容保持模板原样，修改的内容用红色标记"""
        # 填报日期 (行1, 列2) - 用红色标记
        current_date = datetime.now()
        date_text = f'填报日期：{current_date.year}年{current_date.month}月{current_date.day}日'
        cell = table.rows[0].cells[1]
        cell.text = date_text
        self.set_text_color(cell, RGBColor(255, 0, 0))  # 红色

        # 单户综合融资总量 (行4, 列1) - 修改金额部分，用红色标记
        cell = table.rows[3].cells[0]
        current_text = cell.text
        new_text = current_text.replace('40,000,000.00万元', '4000万元')
        cell.text = new_text
        # 只给修改的金额部分设置红色
        self.highlight_changed_text(cell, '4000万元', RGBColor(255, 0, 0))

        # 单笔业务债项批复金额 (行5, 列1) - 修改金额部分，用红色标记
        cell = table.rows[4].cells[0]
        current_text = cell.text
        new_text = current_text.replace('4000.00万元', '1300万元')
        cell.text = new_text
        # 只给修改的金额部分设置红色
        self.highlight_changed_text(cell, '1300万元', RGBColor(255, 0, 0))

        # 期限 (行7, 列2) - 添加13个月信息，用红色标记
        cell = table.rows[6].cells[1]
        current_text = cell.text
        new_text = current_text.replace('期限：', f'期限：{self.extracted_data["business_period"]}')
        cell.text = new_text
        # 只给修改的期限部分设置红色
        self.highlight_changed_text(cell, self.extracted_data["business_period"], RGBColor(255, 0, 0))

    def set_text_color(self, cell, color):
        """设置单元格文本颜色"""
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.color.rgb = color

    def highlight_changed_text(self, cell, target_text, color):
        """高亮显示特定文本"""
        for paragraph in cell.paragraphs:
            if target_text in paragraph.text:
                # 清除现有的runs
                paragraph.clear()
                # 重新构建段落，只给目标文本设置颜色
                full_text = cell.text
                if target_text in full_text:
                    parts = full_text.split(target_text)
                    for i, part in enumerate(parts):
                        if i > 0:
                            # 添加目标文本（红色）
                            run = paragraph.add_run(target_text)
                            run.font.color.rgb = color
                        if part:
                            # 添加普通文本
                            paragraph.add_run(part)
                break

    def fill_three_core_sections(self, table):
        """填充三个核心内容部分，保持原始表格格式，用红色标记"""
        # 行9包含了三个部分的内容，需要完全替换
        cell_left = table.rows[8].cells[0]  # 行9，列1
        cell_right = table.rows[8].cells[1]  # 行9，列2

        # 清空原有内容
        cell_left.text = ""
        cell_right.text = ""

        # 在单元格中创建表格来保持原始格式
        self.insert_quota_tables(cell_left)
        self.insert_quota_tables(cell_right)

    def insert_quota_tables(self, cell):
        """在单元格中插入原始表格格式的内容"""
        # 添加标题和内容，保持表格结构

        # 一、用信前提条件
        p1 = cell.add_paragraph()
        run1 = p1.add_run("一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况")
        run1.font.color.rgb = RGBColor(255, 0, 0)
        run1.bold = True

        p1_content = cell.add_paragraph()
        run1_content = p1_content.add_run(self.extracted_data['quota_preconditions'])
        run1_content.font.color.rgb = RGBColor(255, 0, 0)

        # 二、持续条件（保持表格格式）
        p2 = cell.add_paragraph()
        run2 = p2.add_run("二、单户综合融资总量方案申报书中列明的持续条件及落实情况")
        run2.font.color.rgb = RGBColor(255, 0, 0)
        run2.bold = True

        # 添加持续条件的表格内容
        conditions = [
            '在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合《关于印发<科技企业创新能力评价体系应用推广方案>的通知》（建总函〔2021〕758号文件管理要求的在我行科创评级"T5"及以上。若突破用信持续条件，将报有权审批机构变更方案或重检。',
            '在我行单户综合融资总量有效期内，分阶段调整：\n1. A+轮融资到账前（公司2025年最新一轮融资到账前）：不高于75%\n2. A+轮融资到账后（公司2025年最新一轮融资到账后）：不高于65%。\n若突破该条件，将报有权审批机构变更方案或重检。',
            '在我行单户综合融资总量有效期内，分阶段调整：\n1. A+轮融资到账前（公司2025年最新一轮融资到账前）：单一口径流动比率不低于1。\n2.A+轮融资到账后（公司2025年最新一轮融资到账前后：单一口径流动比率不低于1.1。\n若突破该条件，将报有权审批机构变更方案或重检。',
            '在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。若对外担保须征得我行同意，若突破持续条件我行将报有权审批机构变更方案或重检。',
            '在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行，若突破持续性条件，我行将报有权审批机构变更方案或重检。'
        ]

        for condition in conditions:
            p_cond = cell.add_paragraph()
            run_cond = p_cond.add_run(f"• {condition}")
            run_cond.font.color.rgb = RGBColor(255, 0, 0)

        # 三、业务贷款条件
        p3 = cell.add_paragraph()
        run3 = p3.add_run("三、单笔业务申报书中列明的贷款条件及落实情况")
        run3.font.color.rgb = RGBColor(255, 0, 0)
        run3.bold = True

        # 添加业务条件的表格内容
        business_conditions = [
            '贸易背景条件：作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。',
            '存量压缩条件（如有）：/'
        ]

        for condition in business_conditions:
            p_bus = cell.add_paragraph()
            run_bus = p_bus.add_run(f"• {condition}")
            run_bus.font.color.rgb = RGBColor(255, 0, 0)


def main():
    generator = ConditionChecklistGenerator()
    success = generator.generate_checklist()
    
    if success:
        print('✅ 落实情况表生成成功！')
    else:
        print('❌ 落实情况表生成失败！')

if __name__ == "__main__":
    main()
