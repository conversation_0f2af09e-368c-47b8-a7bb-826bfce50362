#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从申报书中原文提取完整内容
确保与申报书中的文字完全一致，不进行任何总结或改写
"""

import docx
from pathlib import Path
import re

def extract_original_text():
    """原文提取申报书内容"""
    project_root = Path(__file__).parent
    test_output_dir = project_root / "test_output"
    
    print("📖 原文提取申报书内容")
    print("="*60)
    
    results = {}
    
    # 读取额度申报书
    quota_file = test_output_dir / "额度申报书.docx"
    if quota_file.exists():
        print(f"\n📄 分析额度申报书...")
        quota_doc = docx.Document(quota_file)
        quota_text = extract_full_document_text(quota_doc)
        
        quota_results = extract_original_conditions(quota_text, "额度申报书")
        results.update(quota_results)
    
    # 读取业务申报书
    business_file = test_output_dir / "业务申报书.docx"
    if business_file.exists():
        print(f"\n📄 分析业务申报书...")
        business_doc = docx.Document(business_file)
        business_text = extract_full_document_text(business_doc)
        
        business_results = extract_original_conditions(business_text, "业务申报书")
        results.update(business_results)
    
    # 显示原文提取结果
    display_original_results(results)
    
    return results

def extract_full_document_text(doc):
    """提取文档的完整原文，保持原始格式"""
    full_text = ""
    
    # 提取段落，保持原始换行
    for para in doc.paragraphs:
        full_text += para.text + "\n"
    
    # 提取表格，保持原始结构
    for table in doc.tables:
        for row in table.rows:
            row_texts = []
            for cell in row.cells:
                cell_text = ""
                for para in cell.paragraphs:
                    cell_text += para.text
                row_texts.append(cell_text)
            full_text += "\t".join(row_texts) + "\n"
    
    return full_text

def extract_original_conditions(text, source):
    """从文本中原文提取条件内容"""
    conditions = {}
    
    print(f"🔍 从{source}中原文提取...")
    
    # 1. 提取用信前提条件的完整原文
    print("   🔍 搜索用信前提条件...")
    prerequisite_sections = find_complete_sections(text, [
        "用信前提条件",
        "放款条件", 
        "提款条件"
    ])
    
    if prerequisite_sections:
        conditions['prerequisite_conditions_original'] = prerequisite_sections[0]
        print(f"      ✅ 找到用信前提条件原文: {len(prerequisite_sections[0])}字符")
        print(f"      📝 内容预览: {prerequisite_sections[0][:100]}...")
    
    # 2. 提取持续条件的完整原文
    print("   🔍 搜索持续条件...")
    continuous_sections = find_complete_sections(text, [
        "持续条件",
        "持续性条件",
        "用信持续条件"
    ])
    
    if continuous_sections:
        conditions['continuous_conditions_original'] = continuous_sections[0]
        print(f"      ✅ 找到持续条件原文: {len(continuous_sections[0])}字符")
        print(f"      📝 内容预览: {continuous_sections[0][:100]}...")
    
    # 3. 提取管理条件的完整原文
    print("   🔍 搜索管理条件...")
    management_sections = find_complete_sections(text, [
        "管理条件",
        "管理要求",
        "贷后管理"
    ])
    
    if management_sections:
        conditions['management_conditions_original'] = management_sections[0]
        print(f"      ✅ 找到管理条件原文: {len(management_sections[0])}字符")
        print(f"      📝 内容预览: {management_sections[0][:100]}...")
    
    # 4. 搜索包含"1.2倍"的完整原文段落
    print("   🔍 搜索1.2倍相关原文...")
    ratio_text = find_text_containing_keyword(text, "1.2倍")
    if ratio_text:
        conditions['ratio_requirement_original'] = ratio_text
        print(f"      ✅ 找到1.2倍要求原文: {len(ratio_text)}字符")
        print(f"      📝 内容: {ratio_text}")
    
    # 5. 搜索风险缓释措施的完整原文
    print("   🔍 搜索风险缓释措施...")
    risk_sections = find_complete_sections(text, [
        "风险缓释措施",
        "风险控制措施",
        "风险防控"
    ])
    
    if risk_sections:
        conditions['risk_mitigation_original'] = risk_sections[0]
        print(f"      ✅ 找到风险缓释措施原文: {len(risk_sections[0])}字符")
        print(f"      📝 内容预览: {risk_sections[0][:100]}...")
    
    # 6. 搜索担保方式的完整原文描述
    print("   🔍 搜索担保方式...")
    guarantee_text = find_text_containing_keyword(text, "担保方式")
    if guarantee_text:
        conditions['guarantee_method_original'] = guarantee_text
        print(f"      ✅ 找到担保方式原文: {len(guarantee_text)}字符")
        print(f"      📝 内容: {guarantee_text}")
    
    return conditions

def find_complete_sections(text, keywords):
    """查找包含关键词的完整段落"""
    sections = []
    lines = text.split('\n')
    
    for keyword in keywords:
        for i, line in enumerate(lines):
            if keyword in line:
                # 找到关键词所在行，提取完整段落
                section_start = i
                section_end = i + 1
                
                # 向后查找，直到遇到下一个标题或空行
                while section_end < len(lines):
                    next_line = lines[section_end].strip()
                    if not next_line:  # 空行
                        break
                    if re.match(r'^[一二三四五六七八九十\d]+[、．.]', next_line):  # 新的编号
                        break
                    if re.match(r'^[（(]\d+[）)]', next_line):  # 新的小标题
                        break
                    section_end += 1
                
                # 提取完整段落
                section_text = '\n'.join(lines[section_start:section_end]).strip()
                if len(section_text) > 20:  # 确保不是片段
                    sections.append(section_text)
                    break
    
    return sections

def find_text_containing_keyword(text, keyword):
    """查找包含特定关键词的完整句子或段落"""
    lines = text.split('\n')
    
    for line in lines:
        if keyword in line:
            # 找到包含关键词的行
            stripped_line = line.strip()
            if len(stripped_line) > 10:  # 确保不是片段
                return stripped_line
    
    return None

def display_original_results(results):
    """显示原文提取结果"""
    print(f"\n📊 原文提取结果汇总:")
    print("="*60)
    
    if not results:
        print("❌ 未提取到原文内容")
        return
    
    for key, value in results.items():
        print(f"\n🔹 {key}:")
        print("-" * 40)
        print(value)
        print("-" * 40)
        print(f"字符数: {len(value)}")

def main():
    results = extract_original_text()
    
    print(f"\n💾 保存原文提取结果...")
    
    # 保存到文件
    output_file = Path(__file__).parent / "extracted_original_text.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("申报书原文提取结果\n")
        f.write("="*60 + "\n\n")
        
        for key, value in results.items():
            f.write(f"{key}:\n")
            f.write("-" * 40 + "\n")
            f.write(value + "\n")
            f.write("-" * 40 + "\n\n")
    
    print(f"✅ 原文已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    main()
