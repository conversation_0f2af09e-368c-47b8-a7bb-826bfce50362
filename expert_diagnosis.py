#!/usr/bin/env python3
"""
专家诊断 - 分析当前生成文档的问题
"""

import docx
from pathlib import Path

def expert_diagnosis():
    print('=== 专家诊断分析 ===\n')
    
    # 1. 分析最新生成的文档
    latest_file = Path('test_output/落实情况表_真实表格版_20250801_141639.docx')
    
    if not latest_file.exists():
        print('❌ 最新文档不存在')
        return
    
    doc = docx.Document(latest_file)
    
    print('📊 当前文档结构分析:')
    print(f'  - 段落数: {len(doc.paragraphs)}')
    print(f'  - 表格数: {len(doc.tables)}')
    
    # 2. 分析主表格内容
    if len(doc.tables) > 0:
        main_table = doc.tables[0]
        print(f'\n📋 主表格分析 ({len(main_table.rows)}行 x {len(main_table.columns)}列):')
        
        # 重点检查第9行（索引8）的内容
        if len(main_table.rows) > 8:
            target_row = main_table.rows[8]
            target_cell = target_row.cells[0]
            
            print(f'\n🎯 目标单元格（第9行第1列）分析:')
            print(f'  - 段落数: {len(target_cell.paragraphs)}')
            print(f'  - 表格数: {len(target_cell.tables)}')
            
            # 显示单元格内容结构
            print(f'\n📝 单元格内容结构:')
            for i, paragraph in enumerate(target_cell.paragraphs):
                text = paragraph.text.strip()
                if text:
                    print(f'  段落{i+1}: {text[:100]}...')
            
            # 检查嵌套表格
            if target_cell.tables:
                print(f'\n📊 嵌套表格分析:')
                for i, nested_table in enumerate(target_cell.tables):
                    print(f'  表格{i+1}: {len(nested_table.rows)}行 x {len(nested_table.columns)}列')
                    
                    # 显示嵌套表格的前几行内容
                    for row_idx, row in enumerate(nested_table.rows[:3]):
                        print(f'    行{row_idx+1}: {[cell.text.strip()[:20] for cell in row.cells]}')
    
    # 3. 对比原始申报书的表格结构
    print(f'\n🔍 对比原始申报书表格:')
    compare_with_source_tables()
    
    # 4. 专家建议
    print(f'\n💡 专家诊断结果:')
    provide_expert_recommendations()

def compare_with_source_tables():
    """对比原始申报书的表格结构"""
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 检查持续条件表格
    if quota_file.exists():
        quota_doc = docx.Document(quota_file)
        continuous_table = quota_doc.tables[5]
        print(f'  原始持续条件表格: {len(continuous_table.rows)}行 x {len(continuous_table.columns)}列')
        
        # 显示原始表格结构
        print(f'  原始表头: {[cell.text.strip() for cell in continuous_table.rows[0].cells]}')
    
    # 检查贷款条件表格
    if business_file.exists():
        business_doc = docx.Document(business_file)
        loan_table = business_doc.tables[6]
        print(f'  原始贷款条件表格: {len(loan_table.rows)}行 x {len(loan_table.columns)}列')
        
        # 显示原始贷款条件结构
        print(f'  原始贷款条件前3条:')
        for i, row in enumerate(loan_table.rows[:3]):
            if len(row.cells) >= 2:
                print(f'    {row.cells[0].text.strip()}: {row.cells[1].text.strip()[:50]}...')

def provide_expert_recommendations():
    """提供专家建议"""
    print(f'  🎯 问题识别:')
    print(f'    1. 持续条件表格 - 可能插入位置不正确')
    print(f'    2. 贷款条件 - 可能需要表格格式而非文本格式')
    print(f'    3. 内容组织 - 三个部分可能需要更清晰的分隔')
    
    print(f'\n  🛠️ 解决方案:')
    print(f'    1. 检查持续条件表格是否正确嵌入到目标单元格')
    print(f'    2. 考虑将贷款条件也做成表格格式')
    print(f'    3. 确保三个部分在文档中有明确的分隔和标识')
    
    print(f'\n  ✅ 下一步行动:')
    print(f'    1. 重新检查模板中的具体替换位置')
    print(f'    2. 确认每个部分的预期格式（表格 vs 文本）')
    print(f'    3. 逐个部分进行精确替换和验证')

if __name__ == "__main__":
    expert_diagnosis()
