#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同放款业务服务
提供合同放款相关的业务逻辑处理和API接口
"""

import logging
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from contract_disbursement.generator import ContractDisbursementGenerator

logger = logging.getLogger(__name__)

class ContractDisbursementService:
    """合同放款业务服务"""
    
    def __init__(self, db_manager=None):
        """初始化服务"""
        self.db_manager = db_manager
        self.generator = ContractDisbursementGenerator()
        
    def generate_document(self, company_id: str, document_type: str, **kwargs) -> Dict[str, Any]:
        """
        生成合同放款文档
        
        Args:
            company_id: 企业ID
            document_type: 文档类型
            **kwargs: 其他参数
            
        Returns:
            Dict: 生成结果
        """
        try:
            logger.info(f"🏦 合同放款服务 - 生成文档: {document_type}, 企业: {company_id}")
            
            # 调用生成器
            output_path, summary = self.generator.generate_contract_disbursement(
                company_id=company_id,
                document_type=document_type,
                **kwargs
            )
            
            return {
                'success': True,
                'message': '文档生成成功',
                'data': {
                    'output_path': str(output_path),
                    'filename': output_path.name,
                    'summary': summary
                }
            }
            
        except Exception as e:
            logger.error(f"合同放款文档生成失败: {e}")
            return {
                'success': False,
                'message': f'文档生成失败: {str(e)}',
                'data': None
            }
    
    def get_available_templates(self) -> List[Dict[str, str]]:
        """获取可用的模板列表"""
        templates = [
            {
                'type': 'condition_checklist',
                'name': '条件落实情况表',
                'description': '放款条件落实情况检查表',
                'format': 'docx'
            },
            {
                'type': 'contract_info',
                'name': '合同信息表',
                'description': '合同基本信息管理表',
                'format': 'xlsm'
            },
            {
                'type': 'review_ledger',
                'name': '审查台账',
                'description': '放款审查记录台账',
                'format': 'xlsm'
            },
            {
                'type': 'drawdown_application',
                'name': '提款申请书',
                'description': '客户提款申请文档',
                'format': 'docx'
            },
            {
                'type': 'scale_forecast',
                'name': '规模预测表',
                'description': '业务规模预测分析表',
                'format': 'xlsm'
            }
        ]
        
        return templates
    
    def validate_company_data(self, company_id: str) -> Dict[str, Any]:
        """验证企业数据完整性"""
        try:
            company_data = self.generator._get_company_data(company_id)
            
            if not company_data:
                return {
                    'valid': False,
                    'message': '企业信息不存在',
                    'missing_fields': ['company_id']
                }
            
            # 检查必填字段
            required_fields = ['company_name', 'unified_social_credit_code', 'legal_representative']
            missing_fields = []
            
            for field in required_fields:
                if not company_data.get(field):
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    'valid': False,
                    'message': '企业信息不完整',
                    'missing_fields': missing_fields
                }
            
            return {
                'valid': True,
                'message': '企业信息完整',
                'company_data': company_data
            }
            
        except Exception as e:
            logger.error(f"企业数据验证失败: {e}")
            return {
                'valid': False,
                'message': f'数据验证失败: {str(e)}',
                'missing_fields': []
            }


def main():
    """测试服务"""
    print("🏦 合同放款业务服务测试")
    print("="*50)
    
    service = ContractDisbursementService()
    company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    
    # 1. 验证企业数据
    print("1. 验证企业数据...")
    validation_result = service.validate_company_data(company_id)
    print(f"   验证结果: {validation_result['valid']}")
    print(f"   消息: {validation_result['message']}")
    
    # 2. 获取可用模板
    print("\n2. 获取可用模板...")
    templates = service.get_available_templates()
    for template in templates:
        print(f"   - {template['name']} ({template['type']}) - {template['format']}")
    
    # 3. 生成文档
    print("\n3. 生成条件落实情况表...")
    result = service.generate_document(
        company_id=company_id,
        document_type='condition_checklist',
        loan_amount=2000
    )
    
    if result['success']:
        print(f"   ✅ 生成成功!")
        print(f"   📁 文件: {result['data']['filename']}")
        print(f"   📊 摘要: {result['data']['summary']}")
    else:
        print(f"   ❌ 生成失败: {result['message']}")

if __name__ == "__main__":
    main()
