"""
协定存款业务服务
提供协定存款相关的业务逻辑处理
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from decimal import Decimal
from pathlib import Path
from typing import List, Dict, Any, Optional
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from deposit_services.generators.agreed_deposit_generator import AgreedDepositGenerator

logger = logging.getLogger(__name__)

class DepositService:
    """协定存款业务服务"""
    
    def __init__(self, db_manager=None):
        """初始化服务"""
        self.db_manager = db_manager
        self.generator = AgreedDepositGenerator()
        self._init_database()
    
    def _init_database(self):
        """初始化协定存款相关数据库表"""
        if not self.db_manager:
            return
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建协定存款协议表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS deposit_agreements (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_id TEXT NOT NULL,
                        company_name TEXT NOT NULL,
                        agreement_number TEXT UNIQUE NOT NULL,
                        deposit_amount DECIMAL(15,2) DEFAULT 0,
                        interest_rate_adjustment INTEGER DEFAULT 45,
                        agreement_date DATE,
                        effective_date DATE,
                        maturity_date DATE,
                        status TEXT DEFAULT 'draft',
                        file_path TEXT DEFAULT '',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (company_id) REFERENCES companies(id)
                    )
                """)
                
                # 创建协定存款配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS deposit_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        config_key TEXT UNIQUE NOT NULL,
                        config_value TEXT NOT NULL,
                        description TEXT DEFAULT '',
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 插入默认配置
                self._insert_default_config(cursor)
                
                conn.commit()
                logger.info("协定存款数据库表初始化完成")
                
        except Exception as e:
            logger.error(f"协定存款数据库初始化失败: {e}")
    
    def _insert_default_config(self, cursor):
        """插入默认配置"""
        default_configs = [
            ('default_deposit_amount', '1000', '默认存款金额(万元)'),
            ('default_interest_rate_adjustment', '45', '默认利率调整(bp)'),
            ('default_agreement_period', '1', '默认协议期限(年)'),
            ('agreement_number_prefix', '建八', '协议编号前缀'),
            ('output_directory', 'test_output', '输出文件目录'),
            ('highlight_color', 'YELLOW', '高亮颜色'),
            ('font_name', '宋体', '文档字体'),
            ('font_size', '14', '文档字体大小')
        ]
        
        for key, value, desc in default_configs:
            cursor.execute("""
                INSERT OR IGNORE INTO deposit_config (config_key, config_value, description)
                VALUES (?, ?, ?)
            """, (key, value, desc))
    
    def create_agreement(self, company_id: str, **kwargs) -> Dict[str, Any]:
        """创建新协议"""
        try:
            # 获取企业信息
            company_info = self._get_company_info(company_id)
            if not company_info:
                return {"success": False, "message": f"企业不存在: {company_id}"}
            
            # 生成协议编号
            agreement_number = self._generate_agreement_number(company_info['company_name'])
            
            # 准备协议数据
            agreement_data = {
                'company_id': company_id,
                'company_name': company_info['company_name'],
                'agreement_number': agreement_number,
                'deposit_amount': kwargs.get('deposit_amount', 1000),
                'interest_rate_adjustment': kwargs.get('interest_rate_adjustment', 45),
                'agreement_date': kwargs.get('agreement_date'),
                'effective_date': kwargs.get('effective_date'),
                'maturity_date': kwargs.get('maturity_date'),
                'status': kwargs.get('status', 'draft')
            }
            
            # 保存到数据库
            agreement_id = self._save_agreement(agreement_data)
            if not agreement_id:
                return {"success": False, "message": "保存协议失败"}
            
            # 暂时跳过文档生成，先确保API正常工作
            return {
                "success": True,
                "message": "协议创建成功",
                "data": {
                    "agreement_id": agreement_id,
                    "agreement_number": agreement_number,
                    "file_path": "文档生成功能开发中..."
                }
            }
                
        except Exception as e:
            logger.error(f"创建协议失败: {e}")
            return {"success": False, "message": f"创建协议失败: {e}"}
    
    def get_company_agreements(self, company_id: str) -> Dict[str, Any]:
        """获取企业的所有协议"""
        try:
            if not self.db_manager:
                return {"success": False, "message": "数据库连接不可用"}
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM deposit_agreements 
                    WHERE company_id = ? 
                    ORDER BY created_at DESC
                """, (company_id,))
                
                rows = cursor.fetchall()
                agreements = [dict(row) for row in rows]
                
                return {
                    "success": True,
                    "data": agreements
                }
                
        except Exception as e:
            logger.error(f"获取企业协议失败: {e}")
            return {"success": False, "message": f"获取协议失败: {e}"}
    
    def get_agreement_statistics(self, company_id: str) -> Dict[str, Any]:
        """获取协议统计信息"""
        try:
            if not self.db_manager:
                return {"success": False, "message": "数据库连接不可用"}
            
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取统计数据
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_count,
                        SUM(deposit_amount) as total_amount,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
                        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
                        COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_count,
                        AVG(interest_rate_adjustment) as avg_rate
                    FROM deposit_agreements 
                    WHERE company_id = ?
                """, (company_id,))
                
                row = cursor.fetchone()
                stats = dict(row) if row else {}
                
                return {
                    "success": True,
                    "data": {
                        "total_count": stats.get('total_count', 0),
                        "total_amount": float(stats.get('total_amount', 0) or 0),
                        "active_count": stats.get('active_count', 0),
                        "draft_count": stats.get('draft_count', 0),
                        "expired_count": stats.get('expired_count', 0),
                        "average_rate": round(float(stats.get('avg_rate', 0) or 0), 1)
                    }
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"success": False, "message": f"获取统计信息失败: {e}"}
    
    def _get_company_info(self, company_id: str) -> Optional[Dict[str, Any]]:
        """获取企业信息"""
        if not self.db_manager:
            return None
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM companies WHERE id = ?", (company_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"获取企业信息失败: {e}")
            return None
    
    def _generate_agreement_number(self, company_name: str) -> str:
        """生成协议编号"""
        year = datetime.now().year
        
        # 根据公司名称生成简称
        if '中科卓尔' in company_name:
            company_short = '卓尔'
        elif '神光' in company_name:
            company_short = '神光'
        elif '至臻' in company_name:
            company_short = '至臻'
        elif '卫讯' in company_name:
            company_short = '卫讯'
        else:
            company_short = '企业'
        
        # 生成序号
        sequence = self._get_next_sequence()
        
        return f"建八{company_short}协定{year}{sequence:03d}号"
    
    def _get_next_sequence(self) -> int:
        """获取下一个序号"""
        if not self.db_manager:
            return 1
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM deposit_agreements")
                count = cursor.fetchone()[0]
                return count + 1
        except:
            return 1
    
    def _save_agreement(self, agreement_data: Dict[str, Any]) -> Optional[int]:
        """保存协议到数据库"""
        if not self.db_manager:
            return None
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO deposit_agreements (
                        company_id, company_name, agreement_number, deposit_amount,
                        interest_rate_adjustment, agreement_date, effective_date,
                        maturity_date, status
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    agreement_data['company_id'],
                    agreement_data['company_name'],
                    agreement_data['agreement_number'],
                    agreement_data['deposit_amount'],
                    agreement_data['interest_rate_adjustment'],
                    agreement_data.get('agreement_date'),
                    agreement_data.get('effective_date'),
                    agreement_data.get('maturity_date'),
                    agreement_data['status']
                ))
                
                agreement_id = cursor.lastrowid
                conn.commit()
                return agreement_id
                
        except Exception as e:
            logger.error(f"保存协议失败: {e}")
            return None
    
    def _update_agreement_file_path(self, agreement_id: int, file_path: str):
        """更新协议文件路径"""
        if not self.db_manager:
            return
            
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE deposit_agreements 
                    SET file_path = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (file_path, agreement_id))
                conn.commit()
        except Exception as e:
            logger.error(f"更新文件路径失败: {e}")
