#!/usr/bin/env python3
"""
分析落实情况表模板结构
"""

import docx
from pathlib import Path

def analyze_template():
    template_path = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    
    if not template_path.exists():
        print('模板文件不存在')
        return
    
    doc = docx.Document(template_path)
    
    print('=== 落实情况表模板分析 ===')
    print(f'文档包含 {len(doc.paragraphs)} 个段落')
    print(f'文档包含 {len(doc.tables)} 个表格')
    print()
    
    # 显示所有段落内容
    print('--- 段落内容 ---')
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip():
            print(f'段落{i+1}: {paragraph.text.strip()}')
    
    # 分析表格结构
    if doc.tables:
        print('\n--- 表格结构分析 ---')
        for table_idx, table in enumerate(doc.tables):
            print(f'\n表格 {table_idx+1}:')
            print(f'  行数: {len(table.rows)}')
            print(f'  列数: {len(table.columns) if table.rows else 0}')
            
            # 显示表格内容
            for row_idx, row in enumerate(table.rows):
                row_data = []
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        # 限制显示长度，避免过长
                        if len(cell_text) > 100:
                            cell_text = cell_text[:100] + '...'
                        row_data.append(f'列{cell_idx+1}: {cell_text}')
                
                if row_data:
                    print(f'  行{row_idx+1}:')
                    for cell_data in row_data:
                        print(f'    {cell_data}')
                    print()

if __name__ == "__main__":
    analyze_template()
