# 银企直联系统部署指南

## 🚨 当前问题总结

### 主要问题
1. **文件访问错误** - ERR_FILE_NOT_FOUND
2. **API连接失败** - Failed to fetch
3. **公司信息获取失败** - HTTP 500错误

### 根本原因
- 使用file://协议直接打开HTML文件，无法访问localhost API
- 后端服务中公司信息获取逻辑错误
- API服务未正确启动或端口冲突

## 🔧 解决方案

### 步骤1：修复后端代码

编辑 `api/services/advanced_document_service.py` 第193-198行：

```python
# 原代码（有问题）
if not self.db_manager:
    try:
        from ..services.company_service import CompanyService
        company_service = CompanyService()
        company_data = company_service.get_company(company_id)

# 修改为（正确）
if not self.db_manager:
    try:
        company_data = self._get_company_info(company_id)
```

### 步骤2：正确启动服务

1. **启动API服务**
```bash
cd enterprise_service_db
python -m api.app
```

2. **启动Web服务器**
```bash
cd enterprise_service_db/frontend  
python -m http.server 8080
```

3. **访问系统**
```
http://localhost:8080/index_cockpit.html
```

### 步骤3：验证API连接

测试API是否正常：
```bash
curl http://localhost:5000/health
curl http://localhost:5000/api/companies
```

## 📁 关键文件列表

### 前端文件
- `frontend/index_cockpit.html` - 主页面
- `frontend/css/cockpit.css` - 样式文件  
- `frontend/js/cockpit-manager-safe.js` - 主逻辑
- `frontend/js/api-client.js` - API客户端

### 后端文件
- `api/app.py` - Flask主应用
- `api/services/advanced_document_service.py` - 文档服务（需修改）
- `api/services/company_service.py` - 公司服务
- `api/database.py` - 数据库管理

### 数据库文件
- `database/enterprise_service.db` - SQLite数据库

### 模板文件
- `templates/yingqi_zhilian/yingqi_zhilian_agreement_blueprint.docx`
- `templates/yingqi_zhilian/yingqi_zhilian_oa_text_blueprint.docx`

## ⚠️ 重要提醒

1. **不要直接打开HTML文件** - 必须通过Web服务器访问
2. **确保API服务运行** - 在5000端口
3. **检查数据库文件** - 确保SQLite文件存在且有数据
4. **验证模板文件** - 确保Word模板文件存在

## 🧪 测试步骤

1. 访问 http://localhost:8080/index_cockpit.html
2. 选择客户：四川至臻精密光学有限公司
3. 选择模块：银企直联
4. 测试生成服务协议
5. 测试OA工作台功能

## 📞 技术支持

如果问题仍然存在，请检查：
- API服务日志输出
- 浏览器开发者工具Console错误
- 数据库连接状态
- 模板文件完整性
