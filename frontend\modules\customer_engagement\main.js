/**
 * 客户接洽与资料准备模块 - 独立插件
 * 符合外壳+插件架构的标准接口
 */

class CustomerEngagementModule {
    constructor() {
        this.container = null;
        this.customerData = null;
        this.apiBaseUrl = 'http://127.0.0.1:5000';
        this.selectedFiles = [];
        this.documentCategories = [];
        this.eventListeners = [];
    }

    /**
     * 标准初始化接口
     * @param {HTMLElement} containerElement - 外壳提供的容器
     * @param {Object} customerData - 外壳提供的客户数据
     */
    async init(containerElement, customerData) {
        console.log('🤝 客户接洽模块初始化...', { containerElement, customerData });

        this.container = containerElement;
        this.customerData = customerData;

        try {
            // 渲染模块界面
            this.renderInterface();

            // 绑定事件
            this.bindEvents();

            // 加载数据
            await this.loadDocumentCategories();
            await this.loadCustomerDocuments();

            console.log('✅ 客户接洽模块初始化完成');

        } catch (error) {
            console.error('❌ 客户接洽模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 标准销毁接口
     */
    async destroy() {
        console.log('🗑️ 客户接洽模块销毁...');
        
        try {
            // 清空容器
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            // 重置状态
            this.container = null;
            this.customerData = null;
            
            console.log('✅ 客户接洽模块销毁完成');
            
        } catch (error) {
            console.error('❌ 客户接洽模块销毁失败:', error);
        }
    }

    renderInterface() {
        if (!this.container) {
            throw new Error('容器未初始化');
        }

        this.container.innerHTML = `
            <div class="customer-engagement-module">
                <!-- 模块头部 -->
                <div class="module-header">
                    <div class="header-left">
                        <h2 class="module-title">🤝 客户接洽与资料准备</h2>
                        <p class="module-subtitle">为 ${this.customerData.company_name} 提供专业的客户接洽服务</p>
                    </div>
                    <div class="header-right">
                        <div class="customer-info">
                            <div class="info-item">
                                <span class="info-label">客户名称：</span>
                                <span class="info-value">${this.customerData.company_name}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">信用代码：</span>
                                <span class="info-value">${this.customerData.unified_social_credit_code || '-'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主要功能区域 -->
                <div class="module-content">
                    <!-- 左侧：功能操作区 -->
                    <div class="operations-panel">
                        <div class="panel-header">
                            <h3>📋 业务操作</h3>
                        </div>

                        <div class="operation-groups">
                            <!-- 文档生成组 -->
                            <div class="operation-group">
                                <h4 class="group-title">📄 文档生成</h4>
                                <div class="operation-buttons">
                                    <button class="operation-btn primary" id="generate-service-plan">
                                        <span class="btn-icon">📊</span>
                                        <span class="btn-text">生成服务方案</span>
                                    </button>
                                    <button class="operation-btn primary" id="generate-custodianship-letter">
                                        <span class="btn-icon">📄</span>
                                        <span class="btn-text">生成管护权确认函</span>
                                    </button>
                                    <button class="operation-btn secondary" id="download-credit-checklist">
                                        <span class="btn-icon">📋</span>
                                        <span class="btn-text">下载授信资料清单</span>
                                    </button>
                                    <button class="operation-btn primary" id="generate-credit-checklist">
                                        <span class="btn-icon">📋</span>
                                        <span class="btn-text">生成授信资料清单</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 文件管理组 -->
                            <div class="operation-group">
                                <h4 class="group-title">📁 文件管理</h4>
                                <div class="operation-buttons">
                                    <button class="operation-btn secondary" id="upload-files">
                                        <span class="btn-icon">📤</span>
                                        <span class="btn-text">上传客户资料</span>
                                    </button>
                                    <button class="operation-btn secondary" id="refresh-documents">
                                        <span class="btn-icon">🔄</span>
                                        <span class="btn-text">刷新文档列表</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：文档列表区 -->
                    <div class="documents-panel">
                        <div class="panel-header">
                            <h3>📚 客户文档</h3>
                            <div class="document-stats">
                                <span class="stats-item">总计：<span id="total-docs">0</span> 个文档</span>
                            </div>
                        </div>

                        <div class="documents-container">
                            <div class="documents-list" id="documents-list">
                                <div class="loading-placeholder">
                                    <div class="loading-spinner"></div>
                                    <p>正在加载文档列表...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="module-footer">
                    <div class="status-info">
                        <span class="status-indicator" id="status-indicator">🟢 就绪</span>
                        <span class="last-update" id="last-update">最后更新：刚刚</span>
                    </div>
                </div>

                <!-- 文件上传模态窗口 -->
                <div id="upload-modal" class="upload-modal" style="display: none;">
                    <div class="upload-modal-content">
                        <div class="upload-header">
                            <h3>📤 上传客户资料</h3>
                            <button class="close-btn" id="close-upload-modal">×</button>
                        </div>
                        <div class="upload-body">
                            <div class="upload-area" id="upload-area">
                                <div class="upload-icon">📁</div>
                                <p>点击选择文件或拖拽文件到此处</p>
                                <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
                            </div>
                            <div class="upload-options">
                                <label for="document-category">文档分类：</label>
                                <select id="document-category">
                                    <option value="">请选择分类...</option>
                                </select>
                            </div>
                        </div>
                        <div class="upload-footer">
                            <button class="btn-cancel" id="cancel-upload">取消</button>
                            <button class="btn-upload" id="confirm-upload" disabled>上传文件</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 生成服务方案
        const generateServicePlanBtn = this.container.querySelector('#generate-service-plan');
        if (generateServicePlanBtn) {
            const handler = () => this.generateServicePlan();
            generateServicePlanBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: generateServicePlanBtn, event: 'click', handler });
        }

        // 生成管护权确认函
        const generateCustodianshipBtn = this.container.querySelector('#generate-custodianship-letter');
        if (generateCustodianshipBtn) {
            const handler = () => this.generateCustodianshipLetter();
            generateCustodianshipBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: generateCustodianshipBtn, event: 'click', handler });
        }

        // 下载授信资料清单
        const downloadChecklistBtn = this.container.querySelector('#download-credit-checklist');
        if (downloadChecklistBtn) {
            const handler = () => this.downloadCreditChecklist();
            downloadChecklistBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: downloadChecklistBtn, event: 'click', handler });
        }

        // 上传文件
        const uploadFilesBtn = this.container.querySelector('#upload-files');
        if (uploadFilesBtn) {
            const handler = () => this.showUploadModal();
            uploadFilesBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: uploadFilesBtn, event: 'click', handler });
        }

        // 刷新文档
        const refreshBtn = this.container.querySelector('#refresh-documents');
        if (refreshBtn) {
            const handler = () => this.loadCustomerDocuments();
            refreshBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: refreshBtn, event: 'click', handler });
        }

        // 上传模态窗口事件
        this.bindUploadModalEvents();
    }

    bindUploadModalEvents() {
        // 关闭模态窗口
        const closeBtn = this.container.querySelector('#close-upload-modal');
        const cancelBtn = this.container.querySelector('#cancel-upload');

        [closeBtn, cancelBtn].forEach(btn => {
            if (btn) {
                const handler = () => this.hideUploadModal();
                btn.addEventListener('click', handler);
                this.eventListeners.push({ element: btn, event: 'click', handler });
            }
        });

        // 文件选择
        const fileInput = this.container.querySelector('#file-input');
        if (fileInput) {
            const handler = (e) => this.handleFileSelection(e);
            fileInput.addEventListener('change', handler);
            this.eventListeners.push({ element: fileInput, event: 'change', handler });
        }

        // 确认上传
        const confirmBtn = this.container.querySelector('#confirm-upload');
        if (confirmBtn) {
            const handler = () => this.uploadFiles();
            confirmBtn.addEventListener('click', handler);
            this.eventListeners.push({ element: confirmBtn, event: 'click', handler });
        }
    }

    async generateServicePlan() {
        this.updateStatus('正在生成服务方案...', 'processing');

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/documents/generate_service_plan`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ company_id: this.customerData.id })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }

            const blob = await response.blob();
            this.downloadFile(blob, `${this.customerData.company_name}_服务方案.docx`);

            this.updateStatus('服务方案生成成功', 'success');

        } catch (error) {
            console.error('生成服务方案失败:', error);
            this.updateStatus(`生成失败: ${error.message}`, 'error');
        }
    }

    async generateCustodianshipLetter() {
        this.updateStatus('正在生成管护权确认函...', 'processing');

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/documents/generate_custodianship_letter`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ company_id: this.customerData.id })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }

            const blob = await response.blob();
            this.downloadFile(blob, `${this.customerData.company_name}_管护权确认函.docx`);

            this.updateStatus('管护权确认函生成成功', 'success');

        } catch (error) {
            console.error('生成管护权确认函失败:', error);
            this.updateStatus(`生成失败: ${error.message}`, 'error');
        }
    }

    downloadCreditChecklist() {
        this.updateStatus('正在下载授信资料清单...', 'processing');

        try {
            // 直接下载静态模板文件
            const downloadUrl = `${this.apiBaseUrl}/api/documents/download_credit_checklist`;

            // 创建下载链接
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = '授信资料清单.docx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            this.updateStatus('授信资料清单下载成功', 'success');

        } catch (error) {
            console.error('下载授信资料清单失败:', error);
            this.updateStatus(`下载失败: ${error.message}`, 'error');
        }
    }

    async loadDocumentCategories() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/document_categories`);
            if (response.ok) {
                const result = await response.json();
                this.documentCategories = result.data || [];
                this.updateCategorySelect();
            }
        } catch (error) {
            console.error('加载文档分类失败:', error);
        }
    }

    async loadCustomerDocuments() {
        this.updateStatus('正在加载文档列表...', 'processing');

        try {
            const response = await fetch(`${this.apiBaseUrl}/api/customer_documents/${this.customerData.id}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();
            const documents = result.data || [];

            this.renderDocumentsList(documents);
            this.updateDocumentStats(documents.length);
            this.updateStatus('文档列表加载完成', 'success');

        } catch (error) {
            console.error('加载文档列表失败:', error);
            this.updateStatus(`加载失败: ${error.message}`, 'error');
            this.renderDocumentsList([]);
        }
    }

    renderDocumentsList(documents) {
        const documentsList = this.container.querySelector('#documents-list');
        if (!documentsList) return;

        if (documents.length === 0) {
            documentsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <p>暂无客户文档</p>
                    <p class="empty-hint">点击"上传客户资料"添加文档</p>
                </div>
            `;
            return;
        }

        documentsList.innerHTML = documents.map(doc => `
            <div class="document-item" data-doc-id="${doc.id}">
                <div class="doc-icon">${this.getDocumentIcon(doc.file_name)}</div>
                <div class="doc-info">
                    <div class="doc-name">${doc.file_name}</div>
                    <div class="doc-meta">
                        <span class="doc-category">${doc.category || '未分类'}</span>
                        <span class="doc-date">${this.formatDate(doc.upload_date)}</span>
                        <span class="doc-size">${this.formatFileSize(doc.file_size)}</span>
                    </div>
                </div>
                <div class="doc-actions">
                    <button class="doc-action-btn" onclick="window.open('${this.apiBaseUrl}/api/customer_documents/${doc.id}/download', '_blank')">
                        📥 下载
                    </button>
                </div>
            </div>
        `).join('');
    }

    showUploadModal() {
        const modal = this.container.querySelector('#upload-modal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideUploadModal() {
        const modal = this.container.querySelector('#upload-modal');
        if (modal) {
            modal.style.display = 'none';
        }
        this.selectedFiles = [];
        this.updateUploadButton();
    }

    handleFileSelection(event) {
        this.selectedFiles = Array.from(event.target.files);
        this.updateUploadButton();
    }

    updateUploadButton() {
        const uploadBtn = this.container.querySelector('#confirm-upload');
        if (uploadBtn) {
            uploadBtn.disabled = this.selectedFiles.length === 0;
            uploadBtn.textContent = this.selectedFiles.length > 0
                ? `上传 ${this.selectedFiles.length} 个文件`
                : '上传文件';
        }
    }

    updateCategorySelect() {
        const categorySelect = this.container.querySelector('#document-category');
        if (categorySelect) {
            categorySelect.innerHTML = '<option value="">请选择分类...</option>' +
                this.documentCategories.map(cat =>
                    `<option value="${cat.id}">${cat.name}</option>`
                ).join('');
        }
    }

    async uploadFiles() {
        if (this.selectedFiles.length === 0) return;

        const categorySelect = this.container.querySelector('#document-category');
        const categoryId = categorySelect?.value;

        this.updateStatus('正在上传文件...', 'processing');

        try {
            const formData = new FormData();
            formData.append('company_id', this.customerData.id);
            if (categoryId) {
                formData.append('category_id', categoryId);
            }

            this.selectedFiles.forEach(file => {
                formData.append('files', file);
            });

            const response = await fetch(`${this.apiBaseUrl}/api/customer_documents/upload`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }

            this.updateStatus('文件上传成功', 'success');
            this.hideUploadModal();
            await this.loadCustomerDocuments();

        } catch (error) {
            console.error('文件上传失败:', error);
            this.updateStatus(`上传失败: ${error.message}`, 'error');
        }
    }

    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    getDocumentIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊',
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️',
            'zip': '📦', 'rar': '📦'
        };
        return icons[ext] || '📄';
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }

    formatFileSize(bytes) {
        if (!bytes) return '-';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    updateDocumentStats(count) {
        const totalDocs = this.container.querySelector('#total-docs');
        if (totalDocs) {
            totalDocs.textContent = count;
        }
    }

    updateStatus(message, type = 'info') {
        const statusIndicator = this.container.querySelector('#status-indicator');
        const lastUpdate = this.container.querySelector('#last-update');

        if (statusIndicator) {
            const icons = {
                info: '🟢',
                success: '✅',
                error: '❌',
                processing: '⏳'
            };
            statusIndicator.textContent = `${icons[type] || icons.info} ${message}`;
        }

        if (lastUpdate) {
            lastUpdate.textContent = `最后更新：${new Date().toLocaleTimeString('zh-CN')}`;
        }
    }

    clearEventListeners() {
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];
    }
}

// 将模块类暴露到全局作用域
window.CustomerEngagementModule = CustomerEngagementModule;
