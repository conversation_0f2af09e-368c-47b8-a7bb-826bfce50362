#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存款服务模块 (Deposit Services)
提供各种存款产品的协议生成和管理功能

主要功能：
- 协定存款协议生成
- 定期存款协议生成（预留）
- 大额存单协议生成（预留）
- 存款利息计算
"""

__version__ = "1.0.0"
__author__ = "Enterprise Service System"
__description__ = "存款服务模块 - 协议生成和管理"

# 模块信息
MODULE_INFO = {
    "name": "deposit_services",
    "chinese_name": "存款服务",
    "version": __version__,
    "description": __description__,
    "features": [
        "协定存款协议生成",
        "存款利息计算",
        "客户信息自动填充",
        "协议模板管理"
    ]
}

# 导入主要功能
from .generators.agreed_deposit_generator import AgreedDepositGenerator

__all__ = [
    "MODULE_INFO",
    "AgreedDepositGenerator",
]
