/* 统一驾驶舱样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
}

.unified-cockpit {
    min-height: 100vh;
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    grid-template-areas: 
        "header header"
        "main sidebar";
    gap: 2rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* 页面标题 */
.cockpit-header {
    grid-area: header;
    text-align: center;
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.cockpit-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    font-weight: 400;
}

/* 主要内容区域 */
.cockpit-main {
    grid-area: main;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* 步骤区域 */
.step-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.step-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.step-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #ecf0f1;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.step-header h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 600;
}

.step-description {
    font-size: 1rem;
    color: #7f8c8d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* 客户选择器 */
.customer-selector-container {
    max-width: 400px;
}

.customer-select {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    font-size: 1.1rem;
    background: white;
    color: #2c3e50;
    transition: all 0.3s ease;
    cursor: pointer;
}

.customer-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.customer-select:hover {
    border-color: #bdc3c7;
}

/* 业务模块网格 */
.business-modules {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.business-module {
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 200px;
}

.business-module:hover {
    border-color: #3498db;
    background: #f0f8ff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.business-module.add-module {
    border-style: dashed;
    border-color: #bdc3c7;
    background: #fafafa;
}

.business-module.add-module:hover {
    border-color: #95a5a6;
    background: #f5f5f5;
}

.module-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3498db;
}

.add-module .module-icon {
    color: #95a5a6;
}

.module-content h3 {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.module-content p {
    font-size: 0.9rem;
    color: #7f8c8d;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

.module-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.module-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
}

.module-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* 侧边栏 */
.selected-customer-info,
.operation-logs {
    grid-area: sidebar;
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.operation-logs {
    margin-top: 1rem;
}

.customer-card h3,
.logs-header h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.customer-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.customer-code {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #7f8c8d;
    background: #ecf0f1;
    padding: 0.3rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
    margin-bottom: 1rem;
}

.change-customer-btn,
.clear-logs-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.6rem 1.2rem;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.change-customer-btn:hover,
.clear-logs-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.logs-container {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 3px solid #3498db;
    font-size: 0.85rem;
    line-height: 1.4;
}

.log-entry.success {
    border-left-color: #27ae60;
    background: #d5f4e6;
}

.log-entry.error {
    border-left-color: #e74c3c;
    background: #fdf2f2;
}

/* 业务工作台 */
.business-workspace {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .unified-cockpit {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "header"
            "main"
            "sidebar";
        gap: 1rem;
        padding: 1rem;
    }
    
    .selected-customer-info,
    .operation-logs {
        position: static;
    }
}

@media (max-width: 768px) {
    .cockpit-header {
        padding: 2rem 1rem;
    }
    
    .cockpit-header h1 {
        font-size: 2rem;
    }
    
    .business-modules {
        grid-template-columns: 1fr;
    }
    
    .step-section {
        padding: 1.5rem;
    }
}
