/* 银企直联业务模块 - 样式表 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-template-rows: auto 1fr;
    grid-gap: 20px;
    min-height: 100vh;
}

/* 页面标题 */
.header {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 通用卡片样式 */
section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
}

section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* 表单样式 */
.form-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group label {
    font-weight: 600;
    color: #555;
    min-width: 120px;
}

.form-control {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #7f8c8d;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 客户信息展示 */
.customer-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #3498db;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.info-item {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #e1e8ed;
}

.info-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
}

.info-value {
    color: #333;
    font-size: 1.1em;
}

/* 人员信息 */
.personnel-list {
    margin-top: 20px;
}

.personnel-item {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
}

.personnel-header {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.personnel-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    font-size: 0.9em;
}

/* 标签样式 */
.tags-container {
    margin-top: 15px;
}

.tag {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    margin: 3px;
    font-weight: 500;
}

.tag.qualification {
    background-color: #27ae60;
}

.tag.relationship {
    background-color: #f39c12;
}

/* 文档模板 */
.template-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.template-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.template-info h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.template-list {
    max-height: 300px;
    overflow-y: auto;
}

.template-category {
    margin-bottom: 20px;
}

.template-category h5 {
    color: #555;
    margin-bottom: 10px;
    font-size: 1em;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.template-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.template-item {
    padding: 8px 12px;
    margin: 5px 0;
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.template-item:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    transform: translateX(5px);
}

.template-item.word-template {
    border-left: 4px solid #2196f3;
}

.template-item.pdf-template {
    border-left: 4px solid #ff9800;
}

.template-item:active {
    transform: translateX(3px);
    background-color: #bbdefb;
}

.document-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
}

.document-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    margin-top: 15px;
    min-height: 200px;
    font-family: 'Times New Roman', serif;
    line-height: 1.8;
}

/* 银企直联业务流程追踪器 */
.business-workflow {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 20px;
}

.business-workflow h2 {
    color: white;
    border-bottom: 2px solid rgba(255,255,255,0.3);
    margin-bottom: 20px;
}

.workflow-container {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.workflow-description {
    color: rgba(255,255,255,0.9);
    margin-bottom: 25px;
    font-size: 1.1em;
    text-align: center;
}

.workflow-stage {
    margin-bottom: 30px;
}

.stage-title {
    color: #fff;
    font-size: 1.3em;
    margin-bottom: 15px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.checklist-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checklist-item {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.checklist-item:hover {
    background: rgba(255,255,255,0.15);
    transform: translateX(5px);
}

.checklist-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    width: 100%;
}

.workflow-checkbox {
    margin-right: 15px;
    transform: scale(1.2);
    accent-color: #4CAF50;
}

.item-text {
    flex: 1;
    font-weight: 500;
    color: white;
}

.action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.btn-word {
    background: linear-gradient(45deg, #2196F3, #21CBF3);
    color: white;
}

.btn-word:hover:not(:disabled) {
    background: linear-gradient(45deg, #1976D2, #0097A7);
    transform: translateY(-2px);
}

.btn-pdf {
    background: linear-gradient(45deg, #FF9800, #FF5722);
    color: white;
}

.btn-pdf:hover:not(:disabled) {
    background: linear-gradient(45deg, #F57C00, #D84315);
    transform: translateY(-2px);
}

.btn-gemini {
    background: linear-gradient(45deg, #9C27B0, #E91E63);
    position: relative;
}

.btn-gemini::after {
    content: "✨";
    margin-left: 5px;
}

.manual-action {
    color: rgba(255,255,255,0.7);
    font-size: 0.85em;
    font-style: italic;
}

.workflow-progress {
    margin-top: 30px;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.2);
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.progress-text {
    font-weight: 600;
    color: white;
}

.progress-count {
    font-size: 1.2em;
    font-weight: bold;
    color: #4CAF50;
}

.progress-bar {
    flex: 1;
    height: 10px;
    background: rgba(255,255,255,0.2);
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    border-radius: 5px;
    transition: width 0.5s ease;
}

.workflow-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.workflow-actions .btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.workflow-actions .btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

/* OA工作台样式 */
.oa-workspace-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.oa-workspace-modal.hidden {
    display: none;
}

.oa-workspace-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.oa-workspace-container {
    position: relative;
    width: 95%;
    max-width: 1400px;
    height: 90%;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.oa-workspace-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.oa-workspace-header h2 {
    margin: 0;
    font-size: 1.5em;
    font-weight: 600;
}

.close-workspace-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2em;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-workspace-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.oa-workspace-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.oa-workspace-left {
    flex: 1;
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
}

.oa-workspace-right {
    width: 400px;
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    padding: 25px;
    overflow-y: auto;
}

.document-section, .gemini-response-section, .task-package-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.document-section h3, .gemini-response-section h3, .task-package-section h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.document-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #e3f2fd;
    border-radius: 6px;
}

.company-name {
    font-weight: 600;
    color: #1976d2;
}

.document-status {
    font-size: 0.9em;
    color: #666;
    background: #fff;
    padding: 4px 8px;
    border-radius: 4px;
}

.oa-document-textarea, .gemini-response-textarea {
    width: 100%;
    min-height: 300px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.oa-document-textarea:focus, .gemini-response-textarea:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.workspace-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.materials-section, .prompt-section, .instructions-section {
    margin-bottom: 25px;
}

.materials-section h4, .prompt-section h4, .instructions-section h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1em;
    font-weight: 600;
}

.materials-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.materials-content p {
    margin: 0 0 8px 0;
    font-size: 0.9em;
    line-height: 1.5;
}

.prompt-container {
    position: relative;
}

.prompt-textarea {
    width: 100%;
    height: 200px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    background: #f8f9fa;
}

.btn-copy {
    margin-top: 10px;
    background: #17a2b8;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background 0.3s ease;
}

.btn-copy:hover {
    background: #138496;
}

.instructions-list {
    margin: 0;
    padding-left: 20px;
}

.instructions-list li {
    margin-bottom: 8px;
    font-size: 0.9em;
    line-height: 1.5;
}

.oa-workspace-footer {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-text {
    color: #495057;
    font-weight: 500;
}

.progress-steps {
    display: flex;
    gap: 20px;
}

.step {
    padding: 8px 16px;
    background: #e9ecef;
    border-radius: 20px;
    font-size: 0.9em;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step.active {
    background: #2196f3;
    color: white;
}

/* 扩展信息 */
.extended-details {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
    padding: 15px;
}

.unknown-field {
    margin-bottom: 10px;
    padding: 10px;
    background: white;
    border-radius: 3px;
    border-left: 3px solid #f39c12;
}

/* 操作日志 */
.operation-log {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e8ed;
    height: fit-content;
}

.operation-log h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.log-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.9em;
    color: #555;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-entry.error {
    color: #e74c3c;
    font-weight: 600;
}

.log-entry.success {
    color: #27ae60;
    font-weight: 600;
}

/* 加载指示器 */
.loading {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
    font-style: italic;
}

.loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2c3e50;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 0 20px 20px;
    text-align: right;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.no-selection {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 40px;
}

.info-note {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr;
    }
    
    .operation-log {
        grid-row: 3;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .form-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-group label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .template-controls {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
