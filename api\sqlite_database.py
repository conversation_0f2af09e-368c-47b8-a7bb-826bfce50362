"""
SQLite数据库连接和配置模块
提供SQLite数据库连接和基础操作
"""

import sqlite3
import logging
from contextlib import contextmanager
from typing import Dict, Any, List, Optional
import os

# 配置日志
logger = logging.getLogger(__name__)

class SQLiteDatabaseManager:
    """SQLite数据库管理器"""
    
    def __init__(self, db_path: str = "database/enterprise_service.db"):
        self.db_path = db_path
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """确保数据库文件存在"""
        if not os.path.exists(self.db_path):
            logger.warning(f"数据库文件不存在: {self.db_path}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库连接错误: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_single(self, query: str, params: tuple = ()) -> Optional[Dict[str, Any]]:
        """执行单条查询并返回单个结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                result = cursor.fetchone()
                if result:
                    return dict(result)
                return None
        except Exception as e:
            logger.error(f"执行查询失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_many(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """执行查询并返回多个结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"执行查询失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新操作并返回影响的行数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"执行更新失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def get_companies_list(self) -> List[Dict[str, Any]]:
        """获取公司列表"""
        query = """
            SELECT id, company_name, unified_social_credit_code, 
                   registered_address, legal_representative, business_description,
                   contact_phone, contact_email, industry_category, company_type,
                   created_at, updated_at
            FROM companies 
            WHERE business_status = 'active' OR business_status IS NULL
            ORDER BY created_at DESC
        """
        return self.execute_many(query)
    
    def get_company_detail(self, company_id: str) -> Optional[Dict[str, Any]]:
        """获取公司详细信息"""
        # 获取公司基本信息
        company_query = """
            SELECT id, company_name, unified_social_credit_code, 
                   registered_address, communication_address, business_description,
                   legal_representative, registration_date, registered_capital,
                   business_scope, contact_phone, contact_email, website,
                   industry_category, company_type, business_status,
                   created_at, updated_at
            FROM companies 
            WHERE id = ?
        """
        
        company = self.execute_single(company_query, (company_id,))
        if not company:
            return None
        
        # 获取人员关系
        personnel_query = """
            SELECT p.person_name, p.id_type, p.mobile_phone,
                   cpr.relationship_type, cpr.start_date, cpr.end_date
            FROM company_person_relationships cpr
            JOIN persons p ON cpr.person_id = p.id
            WHERE cpr.company_id = ? AND cpr.is_active = 1
        """
        
        personnel = self.execute_many(personnel_query, (company_id,))
        
        # 获取标签
        tags_query = """
            SELECT tag_name, tag_category
            FROM company_tags
            WHERE company_id = ?
        """
        
        tags = self.execute_many(tags_query, (company_id,))
        
        # 组装结果
        result = dict(company)
        result['personnel'] = personnel
        result['tags'] = [tag['tag_name'] for tag in tags]
        
        return result

# 创建全局数据库管理器实例
try:
    db_manager = SQLiteDatabaseManager()
    logger.info("SQLite数据库管理器初始化成功")
except Exception as e:
    logger.error(f"SQLite数据库管理器初始化失败: {e}")
    db_manager = None
