#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单表格替换器 - 专家指导版
目标：把卓尔的三段表格内容，简单直接地放到落实情况表模板对应位置
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
from datetime import datetime
import shutil
import sqlite3

class SimpleTableReplacer:
    """简单表格替换器"""
    
    def __init__(self):
        """初始化"""
        self.project_root = Path(__file__).parent
        self.template_dir = self.project_root / "templates" / "contract_disbursement"
        self.test_output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 确保输出目录存在
        self.test_output_dir.mkdir(exist_ok=True)
        
        # 绿色RGB值
        self.green_rgb = RGBColor(0, 128, 0)
    
    def get_company_data(self):
        """获取中科卓尔公司数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, company_short_name, legal_representative, 
                       registered_capital, business_scope
                FROM companies 
                WHERE id = ?
            """, ("a1b2c3d4-e5f6-7890-1234-567890abcdef",))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'company_name': result[0],
                    'company_short_name': result[1],
                    'legal_representative': result[2],
                    'registered_capital': result[3],
                    'business_scope': result[4]
                }
            else:
                # 如果数据库查询失败，使用默认值
                return {
                    'company_name': '成都中科卓尔智能科技集团有限公司',
                    'company_short_name': '卓尔',
                    'legal_representative': '杨伟',
                    'registered_capital': '1000万元',
                    'business_scope': '智能科技'
                }
                
        except Exception as e:
            print(f"⚠️ 数据库查询失败，使用默认值: {e}")
            return {
                'company_name': '成都中科卓尔智能科技集团有限公司',
                'company_short_name': '卓尔',
                'legal_representative': '杨伟',
                'registered_capital': '1000万元',
                'business_scope': '智能科技'
            }
    
    def analyze_template_structure(self):
        """分析模板结构，找到需要替换的位置"""
        print("🔍 分析落实情况表模板结构...")
        
        template_path = self.template_dir / "落实情况表.docx"
        
        if not template_path.exists():
            print(f"❌ 模板不存在: {template_path}")
            return None
        
        try:
            doc = docx.Document(template_path)
            
            print(f"📄 文档包含 {len(doc.paragraphs)} 个段落")
            print(f"📊 文档包含 {len(doc.tables)} 个表格")
            
            # 查找关键文本位置
            target_texts = [
                "单户综合融资总量方案申报书中列明的用信前提条件及落实情况",
                "单户综合融资总量方案申报书中列明的持续条件及落实情况", 
                "单笔业务申报书中列明的贷款条件及落实情况"
            ]
            
            found_positions = {}
            
            # 在表格中查找
            for table_idx, table in enumerate(doc.tables):
                for row_idx, row in enumerate(table.rows):
                    for cell_idx, cell in enumerate(row.cells):
                        cell_text = cell.text.strip()
                        for target in target_texts:
                            if target in cell_text:
                                found_positions[target] = {
                                    'type': 'table_cell',
                                    'table_idx': table_idx,
                                    'row_idx': row_idx,
                                    'cell_idx': cell_idx,
                                    'cell': cell
                                }
                                print(f"✅ 找到目标位置: {target}")
                                print(f"   位置: 表格{table_idx}, 行{row_idx}, 列{cell_idx}")
            
            return found_positions
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def create_simple_replacement(self):
        """执行简单直接的替换"""
        print("🔧 开始简单直接的表格替换...")
        print("📋 专家指导: 不复杂化，直接替换三段内容")
        
        company_data = self.get_company_data()
        
        # 分析模板结构
        positions = self.analyze_template_structure()
        if not positions:
            print("❌ 无法找到替换位置")
            return None
        
        try:
            # 复制模板
            template_path = self.template_dir / "落实情况表.docx"
            company_short = company_data.get('company_short_name', '卓尔')
            output_path = self.test_output_dir / f"落实情况表_简单替换_{company_short}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(template_path, output_path)
            
            # 打开文档
            doc = docx.Document(output_path)
            
            # 定义三段具体内容（来自之前整理的条件汇总表）
            replacement_content = {
                "单户综合融资总量方案申报书中列明的持续条件及落实情况": """
持续条件落实情况：
1. 持续的评级水平：当前公司有效评级为10级，科创评级为T5级，符合要求
2. 持续的资产负债率水平：分阶段调整，A+轮融资到账前不高于75%，到账后不高于65%
3. 持续的流动性水平：分阶段调整，A+轮融资到账前不低于1，到账后不低于1.1
4. 持续的或有负债水平：在单户综合融资总量有效期内，客户对集团外企业新增或有负债需提前告知我行
5. 持续的股权结构：在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检
6. 对盈利能力的要求：在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制
7. 对长期投资的限制：在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意
8. 对发行优先权债务的限制：在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行
9. 对账户管理要求：客户在我行开立存款账户，且将贷转存账户设置网银受控
10. 其他条件：本次新增贷款的最终支用日不晚于2025年9月30日；A+轮融资到账前的临时性条款有效期最长不超过6个月；分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元
""",
                
                "单户综合融资总量方案申报书中列明的用信前提条件及落实情况": """
前提条件落实情况：
流动资金贷款（及可串用该额度的其他业务品种）：在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。我行将在每次放款前审核落实。
""",
                
                "单笔业务申报书中列明的贷款条件及落实情况": """
担保措施落实计划：
1. 担保措施落实计划：担保方式为信用，追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施。我行将在放款前落实实控人担保情况及部分专利质押情况
2. 贸易背景条件：作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍
3. 存量压缩条件（如有）：/
4. 支付方式条件：/
5. 账户管理措施：在放款前对我行贷转存账户设置网银受控
"""
            }
            
            # 执行替换
            replacement_count = 0
            
            for target_text, new_content in replacement_content.items():
                if target_text in positions:
                    pos_info = positions[target_text]
                    if pos_info['type'] == 'table_cell':
                        cell = pos_info['cell']
                        
                        # 清空单元格
                        cell.text = ""
                        
                        # 添加新内容并标记为绿色
                        para = cell.paragraphs[0]
                        green_run = para.add_run(new_content.strip())
                        green_run.font.color.rgb = self.green_rgb
                        
                        replacement_count += 1
                        print(f"✅ 已替换: {target_text[:30]}...")
            
            # 基础信息替换
            basic_replacements = {
                '【公司名称】': company_data.get('company_name', ''),
                '【当前年份】': str(datetime.now().year),
                '【当前月份】': str(datetime.now().month),
                '【支用金额万元】': '10000000',
                '【支用金额】': '1000',
                '【合同编号】': f"建八{company_short}（2025）001号",
                '【审批文号额度】': 'PIFU510000000N202407210',
                '【审批文号业务】': 'PIFU5100000002025N00G8',
                '【贷款类型】': '流动资金贷款',
                '【合同金额】': '2000',
                '【贷款期限】': '13个月'
            }
            
            # 替换基础信息
            basic_count = 0
            for para in doc.paragraphs:
                for placeholder, value in basic_replacements.items():
                    if placeholder in para.text:
                        # 简单文本替换
                        para.text = para.text.replace(placeholder, str(value))
                        basic_count += 1
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for para in cell.paragraphs:
                            for placeholder, value in basic_replacements.items():
                                if placeholder in para.text:
                                    para.text = para.text.replace(placeholder, str(value))
                                    basic_count += 1
            
            # 保存文档
            doc.save(output_path)
            
            print(f"\n✅ 简单替换完成: {output_path.name}")
            print(f"📝 替换统计:")
            print(f"  ✅ 三段内容替换: {replacement_count} 处")
            print(f"  ✅ 基础信息替换: {basic_count} 处")
            print(f"📁 文件位置: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 替换失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    replacer = SimpleTableReplacer()
    
    print("=" * 80)
    print("📋 简单表格替换器 - 专家指导版")
    print("=" * 80)
    print("🎯 目标:")
    print("  ✅ 分析落实情况表模板结构")
    print("  ✅ 找到三段内容的替换位置")
    print("  ✅ 简单直接地替换内容")
    print("  ✅ 不复杂化，专注核心需求")
    print("=" * 80)
    
    # 先分析模板结构
    print("\n🔍 步骤1: 分析模板结构")
    positions = replacer.analyze_template_structure()
    
    if positions:
        print(f"\n✅ 找到 {len(positions)} 个替换位置")
        
        # 执行替换
        print("\n🔧 步骤2: 执行简单替换")
        result = replacer.create_simple_replacement()
        
        if result:
            print(f"\n🎉 简单替换完成！")
            print(f"📄 文件位置: {result}")
            print(f"📝 请检查:")
            print(f"  ✅ 三段内容是否正确替换")
            print(f"  ✅ 基础信息是否正确填入")
            print(f"  ✅ 格式是否保持良好")
        else:
            print(f"\n❌ 替换失败")
    else:
        print(f"\n❌ 无法找到替换位置，请检查模板结构")

if __name__ == "__main__":
    main()
