#!/usr/bin/env python3
"""
企业信息核心库 API 启动脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.app import app, logger

def load_environment():
    """加载环境变量"""
    try:
        from dotenv import load_dotenv
        env_file = project_root / '.env'
        if env_file.exists():
            load_dotenv(env_file)
            logger.info("已加载环境变量文件")
        else:
            logger.info("未找到.env文件，使用默认配置")
    except ImportError:
        logger.info("python-dotenv未安装，跳过环境变量加载")

def main():
    """主函数"""
    load_environment()
    
    # 从环境变量获取配置
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', '5000'))
    debug = os.getenv('API_DEBUG', 'True').lower() == 'true'
    
    logger.info("=" * 50)
    logger.info("企业信息核心库 API 服务")
    logger.info("=" * 50)
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"调试模式: {debug}")
    logger.info(f"健康检查: http://{host}:{port}/health")
    logger.info(f"API文档: http://{host}:{port}/api/companies")
    logger.info("=" * 50)
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug
        )
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
