#!/usr/bin/env python3
"""
专家深度诊断 - 检查具体API调用问题
"""

import requests
import json

def expert_deep_diagnosis():
    print('=== 专家深度诊断 - API调用问题 ===\n')
    
    # 1. 测试基础API
    print('🔍 1. 测试基础API...')
    test_basic_apis()
    
    # 2. 测试业务模块API
    print('\n🔍 2. 测试业务模块API...')
    test_business_apis()
    
    # 3. 模拟前端调用
    print('\n🔍 3. 模拟前端调用...')
    simulate_frontend_calls()
    
    # 4. 检查API响应格式
    print('\n🔍 4. 检查API响应格式...')
    check_response_format()

def test_basic_apis():
    """测试基础API"""
    endpoints = [
        'http://localhost:5000/',
        'http://localhost:5000/health',
        'http://localhost:5000/api/companies'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f'  ✅ {endpoint}')
            print(f'     状态: {response.status_code}')
            print(f'     响应: {response.json()}')
        except Exception as e:
            print(f'  ❌ {endpoint}: {e}')

def test_business_apis():
    """测试业务模块API"""
    business_endpoints = [
        '/api/yingqi-zhilian/generate',
        '/api/deposit-services/generate', 
        '/api/contract-disbursement/generate'
    ]
    
    base_url = 'http://localhost:5000'
    test_data = {
        'company_id': 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }
    
    for endpoint in business_endpoints:
        try:
            url = base_url + endpoint
            response = requests.post(url, json=test_data, timeout=5)
            print(f'  📡 POST {endpoint}')
            print(f'     状态: {response.status_code}')
            print(f'     响应: {response.json()}')
        except Exception as e:
            print(f'  ❌ POST {endpoint}: {e}')

def simulate_frontend_calls():
    """模拟前端调用"""
    print('  🌐 模拟前端JavaScript调用...')
    
    # 模拟获取公司列表
    try:
        response = requests.get('http://127.0.0.1:5000/api/companies', timeout=5)
        companies = response.json()
        print(f'  ✅ 获取公司列表: {len(companies.get("data", []))} 家公司')
        
        if companies.get('data'):
            # 使用第一家公司测试业务API
            first_company = companies['data'][0]
            company_id = first_company['id']
            company_name = first_company['company_name']
            
            print(f'  🏢 使用公司: {company_name} (ID: {company_id})')
            
            # 测试银企直联
            test_data = {'company_id': company_id}
            response = requests.post('http://127.0.0.1:5000/api/yingqi-zhilian/generate', 
                                   json=test_data, timeout=5)
            print(f'  📄 银企直联生成: 状态 {response.status_code}')
            if response.status_code == 200:
                print(f'     结果: {response.json().get("message", "无消息")}')
            
    except Exception as e:
        print(f'  ❌ 前端调用模拟失败: {e}')

def check_response_format():
    """检查API响应格式"""
    try:
        # 检查公司列表响应格式
        response = requests.get('http://localhost:5000/api/companies')
        data = response.json()
        
        print('  📋 公司列表API响应格式:')
        print(f'     status: {data.get("status")}')
        print(f'     message: {data.get("message")}')
        print(f'     data类型: {type(data.get("data"))}')
        
        if data.get('data'):
            first_company = data['data'][0]
            print(f'     第一家公司结构: {list(first_company.keys())}')
            
    except Exception as e:
        print(f'  ❌ 响应格式检查失败: {e}')

if __name__ == "__main__":
    expert_deep_diagnosis()
