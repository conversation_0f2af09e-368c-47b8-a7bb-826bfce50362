/**
 * 银企直联业务模块 - 主应用文件
 * 负责应用的初始化和整体协调
 */

class BankConnectApp {
    constructor() {
        this.isInitialized = false;
        this.apiClient = window.apiClient;
        this.uiManager = window.uiManager;
        this.documentGenerator = window.documentGenerator;
    }

    /**
     * 应用初始化
     */
    async initialize() {
        try {
            this.uiManager.addLog('正在初始化银企直联业务模块...');
            
            // 检查API连接
            await this.checkAPIConnection();
            
            // 加载初始数据
            await this.loadInitialData();
            
            // 设置定期刷新
            this.setupAutoRefresh();
            
            this.isInitialized = true;
            this.uiManager.addLog('银企直联业务模块初始化完成', 'success');
            
        } catch (error) {
            this.uiManager.addLog(`初始化失败: ${error.message}`, 'error');
            this.uiManager.showError(`应用初始化失败: ${error.message}`);
        }
    }

    /**
     * 检查API连接
     */
    async checkAPIConnection() {
        this.uiManager.addLog('正在检查API服务连接...');
        
        const isConnected = await this.apiClient.testConnection();
        if (!isConnected) {
            throw new Error('无法连接到API服务，请确保后端服务正在运行');
        }
        
        this.uiManager.addLog('API服务连接正常', 'success');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        this.uiManager.addLog('正在加载客户企业列表...');
        this.uiManager.showLoading();

        try {
            const companies = await this.apiClient.getCompanies();
            this.uiManager.populateCustomerList(companies);
            this.uiManager.addLog(`成功加载 ${companies.length} 个客户企业`, 'success');

            // 加载模板列表
            this.uiManager.addLog('正在加载模板列表...');
            await this.documentGenerator.loadAvailableTemplates();
            this.uiManager.displayTemplateList();
            this.uiManager.addLog('模板列表加载完成', 'success');

        } catch (error) {
            this.uiManager.addLog(`加载初始数据失败: ${error.getUserMessage()}`, 'error');
            throw error;
        } finally {
            this.uiManager.hideLoading();
        }
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每5分钟自动刷新一次客户列表
        setInterval(async () => {
            try {
                this.uiManager.addLog('自动刷新客户列表...');
                const companies = await this.apiClient.getCompanies();
                this.uiManager.populateCustomerList(companies);
                this.uiManager.addLog('自动刷新完成', 'success');
            } catch (error) {
                this.uiManager.addLog(`自动刷新失败: ${error.getUserMessage()}`, 'error');
            }
        }, 5 * 60 * 1000); // 5分钟
    }

    /**
     * 处理应用错误
     */
    handleError(error, context = '') {
        const errorMessage = error instanceof APIError ? error.getUserMessage() : error.message;
        const fullMessage = context ? `${context}: ${errorMessage}` : errorMessage;
        
        this.uiManager.addLog(fullMessage, 'error');
        this.uiManager.showError(fullMessage);
        
        console.error('应用错误:', error);
    }

    /**
     * 获取应用状态
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            apiConnected: this.apiClient ? true : false,
            currentCompany: this.uiManager.currentCompany ? this.uiManager.currentCompany.id : null
        };
    }
}

/**
 * 应用启动函数
 */
async function startApp() {
    try {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve);
            });
        }

        // 创建应用实例
        window.bankConnectApp = new BankConnectApp();
        
        // 初始化应用
        await window.bankConnectApp.initialize();
        
        console.log('银企直联业务模块启动成功');
        
    } catch (error) {
        console.error('应用启动失败:', error);
        
        // 显示启动失败的错误信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #f5c6cb;
            max-width: 500px;
            text-align: center;
            z-index: 9999;
            font-family: 'Microsoft YaHei', sans-serif;
        `;
        errorDiv.innerHTML = `
            <h3>应用启动失败</h3>
            <p>${error.message}</p>
            <p style="font-size: 0.9em; margin-top: 15px;">
                请检查：<br>
                1. 后端API服务是否正在运行<br>
                2. 数据库连接是否正常<br>
                3. 网络连接是否正常
            </p>
            <button onclick="location.reload()" style="
                margin-top: 15px;
                padding: 8px 16px;
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            ">重新加载</button>
        `;
        document.body.appendChild(errorDiv);
    }
}

/**
 * 全局错误处理
 */
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
    if (window.bankConnectApp) {
        window.bankConnectApp.handleError(event.error, '系统错误');
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    if (window.bankConnectApp) {
        window.bankConnectApp.handleError(event.reason, 'Promise错误');
    }
});

// 启动应用
startApp();
