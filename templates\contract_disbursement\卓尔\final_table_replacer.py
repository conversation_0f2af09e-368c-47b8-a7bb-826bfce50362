#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from docx import Document
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
from datetime import datetime

class FinalTableReplacer:
    """最终表格替换器 - 在正确位置插入真正的Word表格"""
    
    def __init__(self):
        self.green_rgb = RGBColor(0, 128, 0)  # 绿色标记
        
    def create_complete_document(self, output_file):
        """创建完整的落实情况表文档，包含三个真正的表格"""
        try:
            # 创建新文档
            doc = Document()
            print("✅ 创建新文档")
            
            # 添加标题
            title = doc.add_heading('合同支用条件落实情况表', level=1)
            title.alignment = 1  # 居中对齐
            
            # 添加基本信息
            doc.add_paragraph("客户名称：成都中科卓尔智能科技集团有限公司")
            doc.add_paragraph("合同编号：建八卓尔（2025）001号")
            doc.add_paragraph(f"填表日期：{datetime.now().strftime('%Y年%m月%d日')}")
            doc.add_paragraph("")  # 空行
            
            # 1. 添加前提条件部分
            print("✅ 添加前提条件部分...")
            doc.add_heading('一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况', level=2)
            self.create_precondition_table(doc)
            doc.add_paragraph("")  # 空行
            
            # 2. 添加持续条件部分
            print("✅ 添加持续条件部分...")
            doc.add_heading('二、单户综合融资总量方案申报书中列明的持续条件及落实情况', level=2)
            self.create_continuous_condition_table(doc)
            doc.add_paragraph("")  # 空行
            
            # 3. 添加业务申报书条件部分
            print("✅ 添加业务申报书条件部分...")
            doc.add_heading('三、单笔业务申报书中列明的贷款条件及落实情况', level=2)
            self.create_business_condition_table(doc)
            
            # 保存文档
            doc.save(output_file)
            print(f"✅ 成功保存文档: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 创建文档时出错: {str(e)}")
            return False
    
    def create_precondition_table(self, doc):
        """创建前提条件表格"""
        # 创建表格 (2行4列)
        table = doc.add_table(rows=2, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充数据行
        data_row = table.rows[1]
        data_content = [
            "流动资金贷款（及可串用该额度的其他业务品种）",
            "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
            "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
            "我行将在每次放款前审核落实"
        ]
        
        for i, content in enumerate(data_content):
            data_row.cells[i].text = content
            # 设置为绿色
            for para in data_row.cells[i].paragraphs:
                for run in para.runs:
                    run.font.color.rgb = self.green_rgb
        
        print("✅ 前提条件表格创建完成")
        return table
    
    def create_continuous_condition_table(self, doc):
        """创建持续条件表格"""
        # 创建表格 (13行4列)
        table = doc.add_table(rows=13, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充持续条件数据
        conditions_data = [
            ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
            ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
            ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
            ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
            ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "根据公司章程及股东会决议，公司股权结构稳定。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
            ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "根据公司财务报表，公司营收持续增长。", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
            ["对长期投资的限制", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "根据公司投资决策制度，重大投资需董事会审议。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
            ["对发行优先权债务的限制", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。", "根据公司融资政策，将优先保障我行债权。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
            ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "公司已在我行开立基本存款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
            ["其他条件1", "本次新增贷款的最终支用日不晚于2025年9月30日。", "根据公司资金使用计划，将在有效期内完成支用。", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
            ["其他条件2", "A+轮融资到账前的临时性条款有效期最长不超过6个月。", "公司A+轮融资正在推进中，预计6个月内完成。", "A+轮融资到账前的临时性条款有效期最长不超过6个月。"],
            ["其他条件3", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。", "根据公司财务规划，将严格控制有息负债规模。", "分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
        ]
        
        for i, condition in enumerate(conditions_data):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb
        
        print("✅ 持续条件表格创建完成")
        return table

    def create_business_condition_table(self, doc):
        """创建业务申报书条件表格"""
        # 创建表格 (6行2列) - 包含表头
        table = doc.add_table(rows=6, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头
        headers = ['序号', '贷款条件内容']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充业务申报书条件数据
        business_conditions = [
            ["1", "借款人实际控制人提供连带责任保证担保"],
            ["2", "借款人以其拥有的部分专利产权提供质押担保"],
            ["3", "借款人承诺将A+轮股权融资资金指定我行为唯一收款账户"],
            ["4", "借款人承诺贷转存账户设置网银受控"],
            ["5", "其他约定条件按照合同执行"]
        ]

        for i, condition in enumerate(business_conditions):
            row = table.rows[i + 1]  # 跳过表头
            for j, content in enumerate(condition):
                row.cells[j].text = content
                # 设置为绿色
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb

        print("✅ 业务申报书条件表格创建完成")
        return table

def main():
    """主函数 - 创建完整的落实情况表"""
    replacer = FinalTableReplacer()

    # 输出文件路径
    output_file = f"落实情况表_完整版_卓尔_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"

    print("🚀 开始创建完整的落实情况表...")
    print(f"📄 输出文件: {output_file}")

    # 创建完整文档
    success = replacer.create_complete_document(output_file)

    if success:
        print("🎉 落实情况表创建完成！")
        print(f"📁 输出文件: {output_file}")
        print("\n📋 包含内容:")
        print("1. ✅ 前提条件表格 (2行4列) - 真正的Word表格")
        print("2. ✅ 持续条件表格 (13行4列) - 真正的Word表格")
        print("3. ✅ 业务申报书条件表格 (6行2列) - 真正的Word表格")
        print("\n🎨 格式特点:")
        print("- 表头：黑色加粗")
        print("- 数据：绿色标记")
        print("- 表格：标准网格样式")
        print("- 结构：清晰的三部分布局")
    else:
        print("❌ 落实情况表创建失败！")

if __name__ == "__main__":
    main()
