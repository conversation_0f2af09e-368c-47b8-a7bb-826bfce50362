#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断颜色标记问题
检查生成的文档中的颜色标记是否正确应用
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from pathlib import Path

def diagnose_color_highlighting():
    """诊断颜色标记问题"""
    project_root = Path(__file__).parent
    generated_file = project_root / "test_output" / "最终版条件落实情况表_成都中科卓尔智能科技集团有限公司.docx"
    
    print("🔍 诊断颜色标记问题")
    print("="*60)
    
    if not generated_file.exists():
        print(f"❌ 文件不存在: {generated_file}")
        return
    
    try:
        doc = docx.Document(generated_file)
        
        print(f"📄 文件: {generated_file.name}")
        
        # 检查颜色标记的详细信息
        print(f"\n🎨 颜色标记详细诊断:")
        print("-" * 60)
        
        total_runs = 0
        highlighted_runs = 0
        green_runs = 0
        yellow_runs = 0
        other_color_runs = 0
        
        # 遍历所有文本运行
        for paragraph in doc.paragraphs:
            for run in paragraph.runs:
                total_runs += 1
                if run.font.highlight_color:
                    highlighted_runs += 1
                    color = run.font.highlight_color
                    
                    if color == WD_COLOR_INDEX.BRIGHT_GREEN:
                        green_runs += 1
                        print(f"   🟢 绿色高亮: '{run.text}' (长度: {len(run.text)})")
                    elif color == WD_COLOR_INDEX.YELLOW:
                        yellow_runs += 1
                        print(f"   🟡 黄色高亮: '{run.text}' (长度: {len(run.text)})")
                    else:
                        other_color_runs += 1
                        print(f"   🔵 其他颜色({color}): '{run.text}' (长度: {len(run.text)})")
        
        # 检查表格中的颜色标记
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            total_runs += 1
                            if run.font.highlight_color:
                                highlighted_runs += 1
                                color = run.font.highlight_color
                                
                                if color == WD_COLOR_INDEX.BRIGHT_GREEN:
                                    green_runs += 1
                                    print(f"   🟢 绿色高亮(表格): '{run.text}' (长度: {len(run.text)})")
                                elif color == WD_COLOR_INDEX.YELLOW:
                                    yellow_runs += 1
                                    print(f"   🟡 黄色高亮(表格): '{run.text}' (长度: {len(run.text)})")
                                else:
                                    other_color_runs += 1
                                    print(f"   🔵 其他颜色(表格)({color}): '{run.text}' (长度: {len(run.text)})")
        
        # 统计信息
        print(f"\n📊 颜色标记统计:")
        print("-" * 60)
        print(f"   📝 总文本运行数: {total_runs}")
        print(f"   🎨 有颜色标记的运行数: {highlighted_runs}")
        print(f"   🟢 绿色标记数: {green_runs}")
        print(f"   🟡 黄色标记数: {yellow_runs}")
        print(f"   🔵 其他颜色标记数: {other_color_runs}")
        
        # 检查我们期望的替换内容是否被标记
        print(f"\n🔍 检查期望的替换内容:")
        print("-" * 60)
        
        expected_replacements = [
            'KHED510488500202522805',
            '1300万元',
            'ESG绿色',
            '2025年07月'
        ]
        
        all_text = ""
        for paragraph in doc.paragraphs:
            all_text += paragraph.text + "\n"
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    all_text += cell.text + " "
        
        for expected in expected_replacements:
            if expected in all_text:
                print(f"   ✅ 找到替换内容: {expected}")
                # 检查是否被标记为绿色
                found_green = False
                for paragraph in doc.paragraphs:
                    for run in paragraph.runs:
                        if expected in run.text and run.font.highlight_color == WD_COLOR_INDEX.BRIGHT_GREEN:
                            found_green = True
                            break
                    if found_green:
                        break
                
                if not found_green:
                    # 在表格中查找
                    for table in doc.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                for paragraph in cell.paragraphs:
                                    for run in paragraph.runs:
                                        if expected in run.text and run.font.highlight_color == WD_COLOR_INDEX.BRIGHT_GREEN:
                                            found_green = True
                                            break
                                    if found_green:
                                        break
                                if found_green:
                                    break
                            if found_green:
                                break
                        if found_green:
                            break
                
                if found_green:
                    print(f"      🟢 已标记为绿色")
                else:
                    print(f"      ❌ 未标记为绿色")
            else:
                print(f"   ❌ 未找到替换内容: {expected}")
        
        # 诊断结论
        print(f"\n💡 诊断结论:")
        print("-" * 60)
        
        if highlighted_runs == 0:
            print("   ❌ 问题：没有任何颜色标记")
            print("   🔧 可能原因：")
            print("      - 颜色标记代码没有正确执行")
            print("      - Word文档格式问题")
            print("      - python-docx库版本问题")
        elif green_runs == 0:
            print("   ❌ 问题：没有绿色标记")
            print("   🔧 可能原因：")
            print("      - 绿色颜色常量设置错误")
            print("      - 替换逻辑没有正确标记")
        elif green_runs > 0 and green_runs < 4:
            print("   ⚠️ 问题：绿色标记不完整")
            print("   🔧 建议：检查替换逻辑")
        else:
            print("   ✅ 颜色标记正常工作")
            if green_runs > 20:
                print("   ⚠️ 但是标记过多，需要优化精确性")
        
        # 提供修复建议
        print(f"\n🔧 修复建议:")
        print("-" * 60)
        
        if highlighted_runs == 0:
            print("   1. 检查python-docx版本是否支持颜色标记")
            print("   2. 尝试使用不同的颜色设置方法")
            print("   3. 检查Word文档是否正确保存")
        else:
            print("   1. 颜色标记功能正常")
            print("   2. 需要优化标记的精确性")
            print("   3. 只标记实际替换的关键内容")
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")

def test_color_marking():
    """测试颜色标记功能"""
    print(f"\n🧪 测试颜色标记功能:")
    print("-" * 60)
    
    try:
        # 创建一个简单的测试文档
        doc = docx.Document()
        paragraph = doc.add_paragraph("这是一个测试段落。")
        
        # 添加一个带颜色的run
        run = paragraph.add_run("这是绿色文字")
        run.font.highlight_color = WD_COLOR_INDEX.BRIGHT_GREEN
        
        # 添加一个带黄色的run
        run2 = paragraph.add_run("这是黄色文字")
        run2.font.highlight_color = WD_COLOR_INDEX.YELLOW
        
        # 保存测试文档
        test_file = Path(__file__).parent / "test_output" / "颜色测试.docx"
        doc.save(test_file)
        
        print(f"   ✅ 创建测试文档: {test_file}")
        
        # 读取并验证
        test_doc = docx.Document(test_file)
        green_found = False
        yellow_found = False
        
        for para in test_doc.paragraphs:
            for run in para.runs:
                if run.font.highlight_color == WD_COLOR_INDEX.BRIGHT_GREEN:
                    green_found = True
                    print(f"   🟢 找到绿色文字: '{run.text}'")
                elif run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                    yellow_found = True
                    print(f"   🟡 找到黄色文字: '{run.text}'")
        
        if green_found and yellow_found:
            print(f"   ✅ 颜色标记功能正常")
        else:
            print(f"   ❌ 颜色标记功能异常")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def main():
    diagnose_color_highlighting()
    test_color_marking()

if __name__ == "__main__":
    main()
