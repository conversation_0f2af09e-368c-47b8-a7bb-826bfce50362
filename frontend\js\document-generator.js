/**
 * 文档生成器模块
 * 负责根据客户数据生成各种业务文档
 */

class DocumentGenerator {
    constructor() {
        this.templates = {
            bankConnect: this.getBankConnectTemplate(),
            creditApplication: this.getCreditApplicationTemplate(),
            companyProfile: this.getCompanyProfileTemplate()
        };
        this.availableTemplates = null;
        this.loadAvailableTemplates();
    }

    /**
     * 加载可用的模板列表
     */
    async loadAvailableTemplates() {
        try {
            const response = await apiClient.get('/api/templates');
            this.availableTemplates = response.data;
        } catch (error) {
            console.error('加载模板列表失败:', error);
        }
    }

    /**
     * 生成银企直联业务申请文档
     */
    generateBankConnectDocument(companyData) {
        if (!companyData) {
            throw new Error('公司数据不能为空');
        }

        const template = this.templates.bankConnect;
        const currentDate = new Date().toLocaleDateString('zh-CN');
        
        // 获取法定代表人信息
        const legalRep = this.getLegalRepresentative(companyData.personnel || []);
        
        // 获取企业资质标签
        const qualifications = this.getQualificationTags(companyData.tags || []);
        
        // 替换模板变量
        return template
            .replace(/\{company_name\}/g, companyData.company_name || '未知企业')
            .replace(/\{credit_code\}/g, companyData.unified_social_credit_code || '未知')
            .replace(/\{legal_rep_name\}/g, legalRep.name || '未知')
            .replace(/\{legal_rep_id\}/g, legalRep.idNumber || '未知')
            .replace(/\{legal_rep_id_type\}/g, legalRep.idType || '身份证')
            .replace(/\{qualifications\}/g, qualifications)
            .replace(/\{current_date\}/g, currentDate)
            .replace(/\{company_id\}/g, companyData.id || '未知');
    }

    /**
     * 生成企业档案文档
     */
    generateCompanyProfile(companyData) {
        if (!companyData) {
            throw new Error('公司数据不能为空');
        }

        const template = this.templates.companyProfile;
        const currentDate = new Date().toLocaleDateString('zh-CN');

        // 生成人员列表
        const personnelList = this.generatePersonnelList(companyData.personnel || []);

        // 生成标签列表
        const tagsList = this.generateTagsList(companyData.tags || []);

        return template
            .replace(/\{company_name\}/g, companyData.company_name || '未知企业')
            .replace(/\{credit_code\}/g, companyData.unified_social_credit_code || '未知')
            .replace(/\{personnel_list\}/g, personnelList)
            .replace(/\{tags_list\}/g, tagsList)
            .replace(/\{current_date\}/g, currentDate)
            .replace(/\{company_id\}/g, companyData.id || '未知');
    }

    /**
     * 下载Word文档模板
     */
    async downloadWordDocument(companyId, templateName) {
        if (!companyId) {
            throw new Error('公司ID不能为空');
        }

        try {
            const url = `/api/company/${companyId}/document/word/${templateName}`;

            // 创建下载链接
            const response = await fetch(`${apiClient.baseURL}${url}`, {
                method: 'GET',
                headers: apiClient.defaultHeaders
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '文档生成失败');
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = `document-${Date.now()}.docx`;
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (filenameMatch) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                }
            }

            // 下载文件
            const blob = await response.blob();
            this.downloadBlob(blob, filename);

            return filename;

        } catch (error) {
            console.error('Word文档下载失败:', error);
            throw error;
        }
    }

    /**
     * 下载PDF文档模板
     */
    async downloadPdfDocument(templateName) {
        try {
            const url = `/api/document/pdf/${templateName}`;

            // 创建下载链接
            const response = await fetch(`${apiClient.baseURL}${url}`, {
                method: 'GET',
                headers: apiClient.defaultHeaders
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'PDF生成失败');
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = `document-${Date.now()}.pdf`;
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (filenameMatch) {
                    filename = filenameMatch[1].replace(/['"]/g, '');
                }
            }

            // 下载文件
            const blob = await response.blob();
            this.downloadBlob(blob, filename);

            return filename;

        } catch (error) {
            console.error('PDF文档下载失败:', error);
            throw error;
        }
    }

    /**
     * 下载Blob文件
     */
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * 获取可用的模板列表
     */
    getAvailableTemplates() {
        return this.availableTemplates;
    }

    /**
     * 生成所有可用的文档
     */
    async generateAllDocuments(companyData) {
        if (!companyData || !companyData.id) {
            throw new Error('公司数据不完整');
        }

        const results = [];

        try {
            // 生成Word文档
            if (this.availableTemplates && this.availableTemplates.word) {
                for (const template of this.availableTemplates.word) {
                    try {
                        const filename = await this.downloadWordDocument(companyData.id, template);
                        results.push({ type: 'Word', template, filename, status: 'success' });
                    } catch (error) {
                        results.push({ type: 'Word', template, error: error.message, status: 'error' });
                    }
                }
            }

            // 生成PDF文档
            if (this.availableTemplates && this.availableTemplates.pdf) {
                for (const template of this.availableTemplates.pdf) {
                    try {
                        const filename = await this.downloadPdfDocument(template);
                        results.push({ type: 'PDF', template, filename, status: 'success' });
                    } catch (error) {
                        results.push({ type: 'PDF', template, error: error.message, status: 'error' });
                    }
                }
            }

            return results;

        } catch (error) {
            console.error('批量生成文档失败:', error);
            throw error;
        }
    }

    /**
     * 获取法定代表人信息
     */
    getLegalRepresentative(personnel) {
        const legalRep = personnel.find(person => person.role === '法定代表人');
        
        if (legalRep) {
            return {
                name: legalRep.person_name,
                idNumber: legalRep.id_number,
                idType: legalRep.id_type
            };
        }
        
        return {
            name: '未指定',
            idNumber: '未知',
            idType: '身份证'
        };
    }

    /**
     * 获取企业资质标签
     */
    getQualificationTags(tags) {
        const qualificationTags = tags.filter(tag => tag.tag_category === '企业资质');
        
        if (qualificationTags.length === 0) {
            return '暂无特殊资质';
        }
        
        return qualificationTags.map(tag => tag.tag_name).join('、');
    }

    /**
     * 生成人员列表HTML
     */
    generatePersonnelList(personnel) {
        if (!personnel || personnel.length === 0) {
            return '<tr><td colspan="4" style="text-align: center; color: #999;">暂无人员信息</td></tr>';
        }

        return personnel.map((person, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>${person.person_name || '未知'}</td>
                <td>${person.role || '未知'}</td>
                <td>${person.id_type || '身份证'}: ${person.id_number || '未知'}</td>
            </tr>
        `).join('');
    }

    /**
     * 生成标签列表HTML
     */
    generateTagsList(tags) {
        if (!tags || tags.length === 0) {
            return '<li style="color: #999;">暂无标签信息</li>';
        }

        // 按类别分组
        const tagsByCategory = {};
        tags.forEach(tag => {
            if (!tagsByCategory[tag.tag_category]) {
                tagsByCategory[tag.tag_category] = [];
            }
            tagsByCategory[tag.tag_category].push(tag.tag_name);
        });

        return Object.entries(tagsByCategory).map(([category, tagNames]) => `
            <li><strong>${category}:</strong> ${tagNames.join('、')}</li>
        `).join('');
    }

    /**
     * 银企直联申请文档模板
     */
    getBankConnectTemplate() {
        return `
            <div style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8; max-width: 800px; margin: 0 auto;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">银企直联业务申请表</h2>
                    <p style="color: #666; margin-top: 10px;">申请日期: {current_date}</p>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">企业基本信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold; width: 150px;">企业名称</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{company_name}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">统一社会信用代码</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{credit_code}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">企业ID</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{company_id}</td>
                        </tr>
                    </table>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">法定代表人信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold; width: 150px;">姓名</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{legal_rep_name}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">证件类型</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{legal_rep_id_type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">证件号码</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{legal_rep_id}</td>
                        </tr>
                    </table>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">企业资质</h3>
                    <p style="padding: 10px; border: 1px solid #ddd; background: white; border-radius: 4px;">{qualifications}</p>
                </div>

                <div style="background: #e8f4fd; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">申请说明</h3>
                    <p>本企业申请开通银企直联业务，用于提高资金管理效率和财务操作便利性。我们承诺提供的所有信息真实有效，并愿意承担相应的法律责任。</p>
                    <br>
                    <p><strong>申请企业:</strong> {company_name}</p>
                    <p><strong>申请日期:</strong> {current_date}</p>
                </div>

                <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9em;">
                    <p>此文档由企业信息核心库系统自动生成</p>
                    <p>生成时间: {current_date}</p>
                </div>
            </div>
        `;
    }

    /**
     * 企业档案文档模板
     */
    getCompanyProfileTemplate() {
        return `
            <div style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8; max-width: 800px; margin: 0 auto;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #27ae60; padding-bottom: 10px;">企业档案</h2>
                    <p style="color: #666; margin-top: 10px;">档案生成日期: {current_date}</p>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">企业基本信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold; width: 150px;">企业名称</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{company_name}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">统一社会信用代码</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{credit_code}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f1f2f6; font-weight: bold;">企业ID</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">{company_id}</td>
                        </tr>
                    </table>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">人员信息</h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #3498db; color: white;">
                                <th style="padding: 10px; border: 1px solid #ddd;">序号</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">姓名</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">职务</th>
                                <th style="padding: 10px; border: 1px solid #ddd;">证件信息</th>
                            </tr>
                        </thead>
                        <tbody>
                            {personnel_list}
                        </tbody>
                    </table>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">企业标签</h3>
                    <ul style="list-style-type: none; padding: 0;">
                        {tags_list}
                    </ul>
                </div>

                <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9em;">
                    <p>此档案由企业信息核心库系统自动生成</p>
                    <p>生成时间: {current_date}</p>
                </div>
            </div>
        `;
    }

    /**
     * 信贷申请文档模板
     */
    getCreditApplicationTemplate() {
        return `
            <div style="font-family: 'Microsoft YaHei', sans-serif; line-height: 1.8; max-width: 800px; margin: 0 auto;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h2 style="color: #2c3e50; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">企业信贷申请表</h2>
                    <p style="color: #666; margin-top: 10px;">申请日期: {current_date}</p>
                </div>
                <!-- 更多模板内容... -->
            </div>
        `;
    }

    /**
     * 导出文档为PDF（需要额外的库支持）
     */
    exportToPDF(documentHTML, filename) {
        // 这里可以集成jsPDF或其他PDF生成库
        // 临时解决方案：打开打印对话框
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>${filename}</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${documentHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// 创建全局文档生成器实例
const documentGenerator = new DocumentGenerator();

// 导出供其他模块使用
window.DocumentGenerator = DocumentGenerator;
window.documentGenerator = documentGenerator;
