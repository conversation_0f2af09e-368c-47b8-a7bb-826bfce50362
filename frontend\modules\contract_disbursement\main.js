/**
 * 合同支用业务模块
 * 提供合同支用与放款管理功能
 */

class ContractDisbursementModule {
    constructor() {
        this.currentCompany = null;
        this.documents = [];
        this.statistics = {};
    }

    /**
     * 模块初始化
     */
    async init(companyData) {
        console.log('初始化合同支用模块', companyData);
        this.currentCompany = companyData;
        
        // 渲染模块界面
        this.render();
        
        // 加载数据
        await this.loadData();
    }

    /**
     * 渲染模块界面
     */
    render() {
        const container = document.getElementById('module-content');
        if (!container) {
            console.error('找不到模块容器');
            return;
        }

        container.innerHTML = `
            <div class="contract-disbursement-module">
                <!-- 模块标题 -->
                <div class="module-header">
                    <h2>📋 合同支用业务</h2>
                    <p class="module-subtitle">为 ${this.currentCompany.company_name} 提供合同支用与放款管理服务</p>
                </div>

                <!-- 业务文档生成区 -->
                <div class="document-generator">
                    <h3>📄 业务文档生成</h3>
                    <div class="document-grid">
                        <!-- Excel文档 -->
                        <div class="document-card excel-doc">
                            <div class="doc-icon">📊</div>
                            <h4>放款审核台账</h4>
                            <p>放款审核流程记录和跟踪</p>
                            <button class="generate-btn" data-template="disbursement_review_ledger_blueprint.xlsm">
                                生成Excel文档
                            </button>
                        </div>

                        <div class="document-card excel-doc">
                            <div class="doc-icon">📈</div>
                            <h4>贷款规模汇总预测表</h4>
                            <p>贷款规模统计和预测分析</p>
                            <button class="generate-btn" data-template="disbursement_scale_forecast_blueprint.xlsm">
                                生成Excel文档
                            </button>
                        </div>

                        <div class="document-card excel-doc">
                            <div class="doc-icon">📋</div>
                            <h4>合同制作业务台账</h4>
                            <p>合同制作流程管理台账</p>
                            <button class="generate-btn" data-template="disbursement_contract_ledger_blueprint.xlsm">
                                生成Excel文档
                            </button>
                        </div>

                        <div class="document-card excel-doc">
                            <div class="doc-icon">📝</div>
                            <h4>合同制作信息清单</h4>
                            <p>合同制作所需信息汇总</p>
                            <button class="generate-btn" data-template="disbursement_contract_info_blueprint.xlsm">
                                生成Excel文档
                            </button>
                        </div>

                        <!-- Word文档 -->
                        <div class="document-card word-doc">
                            <div class="doc-icon">📄</div>
                            <h4>提款申请书</h4>
                            <p>客户提款申请标准格式</p>
                            <button class="generate-btn" data-template="disbursement_drawdown_application_blueprint.docx">
                                生成Word文档
                            </button>
                        </div>

                        <div class="document-card word-doc">
                            <div class="doc-icon">✅</div>
                            <h4>条件落实情况表</h4>
                            <p>授信条件落实情况记录</p>
                            <button class="generate-btn" data-template="disbursement_condition_checklist_blueprint.docx">
                                生成Word文档
                            </button>
                        </div>

                        <div class="document-card excel-doc">
                            <div class="doc-icon">💼</div>
                            <h4>信贷业务申请书</h4>
                            <p>信贷业务申请标准格式</p>
                            <button class="generate-btn" data-template="信贷业务申请书.xlsx">
                                生成Excel文档
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 生成结果区 -->
                <div id="generation-result" class="generation-result" style="display: none;">
                    <div class="result-card">
                        <div class="result-header">
                            <span class="result-icon">✅</span>
                            <h4>文档生成完成</h4>
                        </div>
                        <div class="result-content">
                            <p class="result-info">文档已成功生成</p>
                            <div class="download-section">
                                <button id="download-document-btn" class="btn btn-success">
                                    <span class="btn-icon">📥</span>
                                    <span class="btn-text">下载文档</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="generation-loading" class="generation-loading" style="display: none;">
                    <div class="loading-card">
                        <div class="loading-spinner"></div>
                        <p class="loading-text">正在生成文档，请稍候...</p>
                    </div>
                </div>
            </div>
        `;

        // 绑定事件
        this.bindEvents();
    }

    /**
     * 绑定事件处理
     */
    bindEvents() {
        // 生成文档按钮
        const generateBtns = document.querySelectorAll('.generate-btn');
        generateBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.getAttribute('data-template');
                this.generateDocument(template);
            });
        });

        // 下载文档按钮
        const downloadBtn = document.getElementById('download-document-btn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadDocument());
        }
    }

    /**
     * 生成文档
     */
    async generateDocument(template) {
        console.log('生成文档:', template);
        
        // 显示加载状态
        this.showLoading();

        try {
            const response = await fetch('http://127.0.0.1:5000/api/contract-disbursement/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: this.currentCompany.id,
                    template: template
                })
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.showResult(result.data);
            } else {
                throw new Error(result.message || '生成文档失败');
            }

        } catch (error) {
            console.error('生成文档失败:', error);
            alert('生成文档失败: ' + error.message);
            this.hideLoading();
        }
    }

    /**
     * 下载文档
     */
    async downloadDocument() {
        if (!this.generatedFile) {
            alert('没有可下载的文件');
            return;
        }

        try {
            const response = await fetch(`http://127.0.0.1:5000/api/contract-disbursement/download/${this.generatedFile.file_id}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = this.generatedFile.filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                throw new Error('下载失败');
            }

        } catch (error) {
            console.error('下载失败:', error);
            alert('下载失败: ' + error.message);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        document.getElementById('generation-loading').style.display = 'block';
        document.getElementById('generation-result').style.display = 'none';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        document.getElementById('generation-loading').style.display = 'none';
    }

    /**
     * 显示生成结果
     */
    showResult(data) {
        this.generatedFile = data;
        document.getElementById('generation-loading').style.display = 'none';
        document.getElementById('generation-result').style.display = 'block';
        
        // 更新结果信息
        const resultInfo = document.querySelector('.result-info');
        if (resultInfo) {
            resultInfo.textContent = `文档文件：${data.filename}`;
        }
    }

    /**
     * 加载数据（简化版）
     */
    async loadData() {
        console.log('合同支用模块数据加载完成');
    }
}

// 创建全局实例
window.contractDisbursementModule = new ContractDisbursementModule();
