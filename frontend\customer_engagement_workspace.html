<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户接洽与资料准备 - 企业服务系统</title>
    <link rel="stylesheet" href="css/customer-engagement.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="workspace-container">
        <!-- 顶部导航栏 -->
        <header class="workspace-header">
            <div class="header-left">
                <button id="back-to-cockpit" class="back-btn">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">返回驾驶舱</span>
                </button>
                <div class="module-title">
                    <h1>📋 客户接洽与资料准备</h1>
                    <p class="module-subtitle">资源与信息中心工作台</p>
                </div>
            </div>
            <div class="header-right">
                <div class="workspace-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">工作台已就绪</span>
                </div>
            </div>
        </header>

        <!-- 主工作区 -->
        <main class="workspace-main">
            <!-- 客户信息区 -->
            <section class="customer-info-section">
                <div class="section-header">
                    <h2>👤 当前服务客户</h2>
                    <div class="customer-lock-status">
                        <span class="lock-icon">🔒</span>
                        <span class="lock-text">客户已锁定</span>
                    </div>
                </div>
                <div class="customer-info-card">
                    <div class="customer-basic-info">
                        <div class="info-item">
                            <label>公司名称：</label>
                            <span id="customer-name" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>统一社会信用代码：</label>
                            <span id="customer-code" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>注册地址：</label>
                            <span id="customer-address" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>法定代表人：</label>
                            <span id="customer-legal-rep" class="info-value">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 标准化工具分发区 -->
            <section class="tools-distribution-section">
                <div class="section-header">
                    <h2>🛠️ 标准化工具分发</h2>
                    <p class="section-description">为客户提供标准化的业务工具和模板</p>
                </div>
                <div class="tools-grid">
                    <!-- 融资资料清单下载 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">📋</div>
                            <h3>融资资料清单</h3>
                        </div>
                        <div class="tool-description">
                            <p>标准化的融资申请所需资料清单，帮助客户准备完整的申请材料</p>
                        </div>
                        <div class="tool-actions">
                            <button id="download-checklist-btn" class="tool-btn primary">
                                <span class="btn-icon">⬇️</span>
                                <span class="btn-text">下载清单</span>
                            </button>
                        </div>
                    </div>

                    <!-- 定制化服务方案生成 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">📊</div>
                            <h3>定制化服务方案</h3>
                        </div>
                        <div class="tool-description">
                            <p>根据客户信息自动生成个性化的银行服务方案PPT</p>
                        </div>
                        <div class="tool-actions">
                            <button id="generate-service-plan-btn" class="tool-btn primary">
                                <span class="btn-icon">🎯</span>
                                <span class="btn-text">生成方案</span>
                            </button>
                        </div>
                    </div>

                    <!-- 管护权确认函生成 -->
                    <div class="tool-card">
                        <div class="tool-header">
                            <div class="tool-icon">📝</div>
                            <h3>管护权确认函</h3>
                        </div>
                        <div class="tool-description">
                            <p>生成客户在建行系统内管护权确认的正式函件，自动填充客户信息并高亮显示</p>
                        </div>
                        <div class="tool-actions">
                            <button id="generate-custodianship-letter-btn" class="tool-btn primary">
                                <span class="btn-icon">📄</span>
                                <span class="btn-text">生成确认函</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 客户资料上传与展示区 -->
            <section class="documents-management-section">
                <div class="section-header">
                    <h2>📁 客户资料管理</h2>
                    <p class="section-description">上传、管理和查看客户提供的各类资料文档</p>
                </div>

                <!-- 文件上传区 -->
                <div class="upload-area">
                    <div class="upload-header">
                        <h3>📤 资料上传</h3>
                    </div>
                    <div class="upload-form">
                        <div class="file-input-wrapper">
                            <input type="file" id="file-input" class="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt,.zip,.rar">
                            <label for="file-input" class="file-input-label">
                                <span class="upload-icon">📎</span>
                                <span class="upload-text">点击选择文件或拖拽文件到此处</span>
                                <span class="upload-hint">支持 PDF、Word、Excel、图片等格式</span>
                            </label>
                        </div>
                        <div class="upload-options">
                            <div class="form-group">
                                <label for="document-category">文档分类：</label>
                                <select id="document-category" class="form-select">
                                    <option value="other_documents">其他资料</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="document-description">文档描述：</label>
                                <input type="text" id="document-description" class="form-input" placeholder="请简要描述文档内容（可选）">
                            </div>
                            <button id="upload-btn" class="upload-btn" disabled>
                                <span class="btn-icon">⬆️</span>
                                <span class="btn-text">上传文档</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 文档列表区 -->
                <div class="documents-list-area">
                    <div class="list-header">
                        <h3>📄 已上传文档</h3>
                        <button id="refresh-documents-btn" class="refresh-btn">
                            <span class="btn-icon">🔄</span>
                            <span class="btn-text">刷新</span>
                        </button>
                    </div>
                    <div class="documents-table-wrapper">
                        <table id="documents-table" class="documents-table">
                            <thead>
                                <tr>
                                    <th>文档名称</th>
                                    <th>文档分类</th>
                                    <th>文件大小</th>
                                    <th>上传时间</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="documents-table-body">
                                <tr class="no-data-row">
                                    <td colspan="6" class="no-data-text">暂无上传的文档</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </main>

        <!-- 状态提示区 -->
        <div id="status-toast" class="status-toast"></div>

        <!-- 加载遮罩 -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner"></div>
            <div class="loading-text">处理中...</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/customer-engagement-manager.js"></script>
</body>
</html>
