#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专家文件分析器 - 诊断生成文件的问题
"""

from docx import Document
from pathlib import Path
import sqlite3

class ExpertFileAnalyzer:
    """专家文件分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 找到最新生成的文件
        self.latest_file = self.test_output_dir / "落实情况表_完整版_中科卓尔_20250804_183830.docx"
        self.template_file = self.project_root / "templates" / "contract_disbursement" / "落实情况表.docx"
    
    def analyze_generated_file(self):
        """分析生成的文件"""
        print("🔍 专家分析 - 生成文件问题诊断")
        print("=" * 80)
        
        if not self.latest_file.exists():
            print(f"❌ 生成文件不存在: {self.latest_file}")
            return
        
        print(f"📄 分析文件: {self.latest_file.name}")
        
        try:
            doc = Document(self.latest_file)
            
            # 1. 分析文档结构
            print(f"\n📋 文档结构分析:")
            print(f"  - 总段落数: {len(doc.paragraphs)}")
            print(f"  - 总表格数: {len(doc.tables)}")
            
            # 2. 检查基础信息替换
            print(f"\n🔍 基础信息替换检查:")
            placeholders_found = []
            for para in doc.paragraphs:
                if '【' in para.text and '】' in para.text:
                    placeholders_found.append(para.text)
            
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if '【' in cell.text and '】' in cell.text:
                            placeholders_found.append(cell.text)
            
            if placeholders_found:
                print(f"  ❌ 发现未替换的占位符:")
                for placeholder in placeholders_found[:5]:  # 只显示前5个
                    print(f"    - {placeholder[:100]}...")
            else:
                print(f"  ✅ 所有占位符已替换")
            
            # 3. 检查三个标题部分
            print(f"\n🎯 三个标题部分检查:")
            target_titles = [
                "一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况",
                "二、单户综合融资总量方案申报书中列明的持续条件及落实情况", 
                "三、单笔业务申报书中列明的贷款条件及落实情况"
            ]
            
            titles_found = []
            for para in doc.paragraphs:
                for title in target_titles:
                    if title in para.text:
                        titles_found.append(title)
                        print(f"  ✅ 找到标题: {title}")
            
            missing_titles = [t for t in target_titles if t not in titles_found]
            if missing_titles:
                print(f"  ❌ 缺失标题:")
                for title in missing_titles:
                    print(f"    - {title}")
            
            # 4. 检查表格插入情况
            print(f"\n📊 表格插入情况检查:")
            if len(doc.tables) > 1:  # 原模板有1个表格
                print(f"  ✅ 检测到新增表格: {len(doc.tables) - 1} 个")
                
                # 分析每个表格
                for i, table in enumerate(doc.tables[1:], 1):  # 跳过原始表格
                    print(f"    表格{i}: {len(table.rows)}行 x {len(table.columns)}列")
                    
                    # 显示表格内容预览
                    if table.rows:
                        first_row = []
                        for cell in table.rows[0].cells:
                            first_row.append(cell.text[:20])
                        print(f"      表头: {' | '.join(first_row)}")
            else:
                print(f"  ❌ 没有检测到新增表格")
            
            # 5. 检查数据库存储情况
            print(f"\n💾 数据库存储检查:")
            self.check_database_content()
            
        except Exception as e:
            print(f"❌ 分析文件失败: {e}")
            import traceback
            traceback.print_exc()
    
    def check_database_content(self):
        """检查数据库内容"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查表格数量
            cursor.execute("SELECT COUNT(*) FROM condition_tables WHERE company_name = ?", 
                          ("成都中科卓尔智能科技集团有限公司",))
            table_count = cursor.fetchone()[0]
            print(f"  - 数据库中存储的表格数: {table_count}")
            
            # 检查表格类型
            cursor.execute("SELECT table_type, table_name, rows_count, columns_count FROM condition_tables WHERE company_name = ?", 
                          ("成都中科卓尔智能科技集团有限公司",))
            
            for row in cursor.fetchall():
                table_type, table_name, rows_count, columns_count = row
                print(f"    - {table_type}: {table_name} ({rows_count}行 x {columns_count}列)")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 检查数据库失败: {e}")
    
    def compare_with_template(self):
        """与原始模板对比"""
        print(f"\n📋 与原始模板对比:")
        
        try:
            # 分析原始模板
            template_doc = Document(self.template_file)
            print(f"  原始模板: {len(template_doc.paragraphs)}段落, {len(template_doc.tables)}表格")
            
            # 分析生成文件
            generated_doc = Document(self.latest_file)
            print(f"  生成文件: {len(generated_doc.paragraphs)}段落, {len(generated_doc.tables)}表格")
            
            # 检查是否有新增内容
            if len(generated_doc.paragraphs) > len(template_doc.paragraphs):
                print(f"  ✅ 新增段落: {len(generated_doc.paragraphs) - len(template_doc.paragraphs)} 个")
            else:
                print(f"  ❌ 没有新增段落")
            
            if len(generated_doc.tables) > len(template_doc.tables):
                print(f"  ✅ 新增表格: {len(generated_doc.tables) - len(template_doc.tables)} 个")
            else:
                print(f"  ❌ 没有新增表格")
                
        except Exception as e:
            print(f"❌ 对比失败: {e}")
    
    def identify_problems(self):
        """识别具体问题"""
        print(f"\n🚨 问题识别:")
        print("=" * 60)
        
        problems = []
        
        # 检查文件是否存在
        if not self.latest_file.exists():
            problems.append("生成的文件不存在")
        
        try:
            doc = Document(self.latest_file)
            
            # 问题1: 检查是否有占位符未替换
            placeholders_found = False
            for para in doc.paragraphs:
                if '【' in para.text and '】' in para.text:
                    placeholders_found = True
                    break
            
            if placeholders_found:
                problems.append("基础信息占位符未完全替换")
            
            # 问题2: 检查三个标题是否存在
            target_titles = [
                "一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况",
                "二、单户综合融资总量方案申报书中列明的持续条件及落实情况", 
                "三、单笔业务申报书中列明的贷款条件及落实情况"
            ]
            
            titles_found = 0
            for para in doc.paragraphs:
                for title in target_titles:
                    if title in para.text:
                        titles_found += 1
                        break
            
            if titles_found < 3:
                problems.append(f"三个标题部分不完整，只找到 {titles_found}/3 个")
            
            # 问题3: 检查表格插入
            if len(doc.tables) <= 1:  # 原模板有1个表格
                problems.append("没有插入任何新表格到文档中")
            
            # 问题4: 检查表格内容
            if len(doc.tables) > 1:
                for i, table in enumerate(doc.tables[1:], 1):
                    if len(table.rows) <= 1:  # 只有表头
                        problems.append(f"表格{i}没有数据行，只有表头")
            
        except Exception as e:
            problems.append(f"无法读取生成的文件: {e}")
        
        # 输出问题
        if problems:
            for i, problem in enumerate(problems, 1):
                print(f"  {i}. ❌ {problem}")
        else:
            print(f"  ✅ 没有发现明显问题")
        
        return problems
    
    def suggest_solutions(self, problems):
        """提出解决方案"""
        print(f"\n💡 解决方案建议:")
        print("=" * 60)
        
        if not problems:
            print("  ✅ 文件生成正常，无需修复")
            return
        
        solutions = {
            "基础信息占位符未完全替换": [
                "检查占位符定义是否完整",
                "确认公司信息数据是否正确",
                "验证替换逻辑是否正确执行"
            ],
            "三个标题部分不完整": [
                "检查落实情况表模板是否包含正确的标题",
                "确认标题文本匹配逻辑是否准确",
                "验证文档结构是否符合预期"
            ],
            "没有插入任何新表格": [
                "检查表格插入逻辑是否正确",
                "确认数据库中的表格数据是否完整",
                "验证表格创建和插入流程"
            ],
            "表格没有数据行": [
                "检查数据库中的表格数据是否正确存储",
                "确认表格数据读取逻辑",
                "验证表格行创建过程"
            ]
        }
        
        for problem in problems:
            for key, solution_list in solutions.items():
                if key in problem:
                    print(f"\n  🔧 针对 '{problem}':")
                    for solution in solution_list:
                        print(f"    - {solution}")
                    break
            else:
                print(f"\n  ⚠️ 未知问题: {problem}")
                print(f"    - 需要进一步调试分析")

def main():
    """主函数"""
    analyzer = ExpertFileAnalyzer()
    
    # 分析生成的文件
    analyzer.analyze_generated_file()
    
    # 与模板对比
    analyzer.compare_with_template()
    
    # 识别问题
    problems = analyzer.identify_problems()
    
    # 提出解决方案
    analyzer.suggest_solutions(problems)
    
    print(f"\n📋 专家总结:")
    print("=" * 60)
    if problems:
        print(f"❌ 发现 {len(problems)} 个问题，需要修复")
        print(f"🔧 建议按照上述解决方案逐一排查和修复")
    else:
        print(f"✅ 文件生成正常，符合预期要求")

if __name__ == "__main__":
    main()
