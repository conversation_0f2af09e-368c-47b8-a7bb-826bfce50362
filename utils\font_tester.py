#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字体格式保持测试工具
验证文档生成过程中字体格式是否完整保持
"""

import docx
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from .logger import SystemLogger


@dataclass
class FontInfo:
    """字体信息数据类"""
    name: str
    size: Optional[int]
    bold: Optional[bool]
    italic: Optional[bool]
    underline: Optional[bool]
    color: Optional[str]
    highlight: Optional[str]


@dataclass
class FontTestResult:
    """字体测试结果数据类"""
    passed: bool
    preservation_rate: float
    total_elements: int
    preserved_elements: int
    changed_elements: int
    highlighted_elements: int
    details: List[Dict[str, Any]]
    summary: str


class FontPreservationTester:
    """字体格式保持测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.logger = SystemLogger("font_tester")
        self.test_results = []
    
    def test_document(self, document_path: str, 
                     original_path: Optional[str] = None) -> FontTestResult:
        """测试文档的字体格式保持情况"""
        try:
            self.logger.info(f"开始字体格式测试: {document_path}")
            
            doc_path = Path(document_path)
            if not doc_path.exists():
                raise FileNotFoundError(f"文档文件不存在: {document_path}")
            
            # 分析文档字体
            font_analysis = self._analyze_document_fonts(document_path)
            
            # 如果有原始文档，进行对比
            if original_path and Path(original_path).exists():
                comparison_result = self._compare_with_original(
                    document_path, original_path
                )
                font_analysis.update(comparison_result)
            
            # 生成测试结果
            result = self._generate_test_result(font_analysis)
            
            # 记录测试结果
            self.test_results.append(result)
            self.logger.info(f"字体格式测试完成: 保持率 {result.preservation_rate:.1f}%")
            
            return result
            
        except Exception as e:
            self.logger.error(f"字体格式测试失败: {e}")
            return FontTestResult(
                passed=False,
                preservation_rate=0.0,
                total_elements=0,
                preserved_elements=0,
                changed_elements=0,
                highlighted_elements=0,
                details=[],
                summary=f"测试失败: {e}"
            )
    
    def _analyze_document_fonts(self, document_path: str) -> Dict[str, Any]:
        """分析文档字体信息"""
        try:
            doc = docx.Document(document_path)
            
            font_elements = []
            total_elements = 0
            highlighted_count = 0
            font_consistency = {}
            
            # 分析段落中的字体
            for para_idx, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():
                    for run_idx, run in enumerate(paragraph.runs):
                        if run.text.strip():
                            font_info = self._extract_font_info(run)
                            element_info = {
                                'type': 'paragraph',
                                'location': f"段落{para_idx}_run{run_idx}",
                                'text_preview': run.text[:20] + "..." if len(run.text) > 20 else run.text,
                                'font_info': font_info
                            }
                            
                            font_elements.append(element_info)
                            total_elements += 1
                            
                            # 统计高亮元素
                            if font_info.highlight:
                                highlighted_count += 1
                            
                            # 统计字体一致性
                            font_key = f"{font_info.name}_{font_info.size}"
                            font_consistency[font_key] = font_consistency.get(font_key, 0) + 1
            
            # 分析表格中的字体
            for table_idx, table in enumerate(doc.tables):
                for row_idx, row in enumerate(table.rows):
                    for cell_idx, cell in enumerate(row.cells):
                        for para_idx, paragraph in enumerate(cell.paragraphs):
                            if paragraph.text.strip():
                                for run_idx, run in enumerate(paragraph.runs):
                                    if run.text.strip():
                                        font_info = self._extract_font_info(run)
                                        element_info = {
                                            'type': 'table',
                                            'location': f"表格{table_idx}_行{row_idx}_列{cell_idx}_段落{para_idx}_run{run_idx}",
                                            'text_preview': run.text[:20] + "..." if len(run.text) > 20 else run.text,
                                            'font_info': font_info
                                        }
                                        
                                        font_elements.append(element_info)
                                        total_elements += 1
                                        
                                        if font_info.highlight:
                                            highlighted_count += 1
                                        
                                        font_key = f"{font_info.name}_{font_info.size}"
                                        font_consistency[font_key] = font_consistency.get(font_key, 0) + 1
            
            return {
                'font_elements': font_elements,
                'total_elements': total_elements,
                'highlighted_count': highlighted_count,
                'font_consistency': font_consistency,
                'most_common_font': max(font_consistency.items(), key=lambda x: x[1])[0] if font_consistency else None
            }
            
        except Exception as e:
            self.logger.error(f"分析文档字体失败: {e}")
            return {
                'font_elements': [],
                'total_elements': 0,
                'highlighted_count': 0,
                'font_consistency': {},
                'most_common_font': None
            }
    
    def _extract_font_info(self, run) -> FontInfo:
        """提取run的字体信息"""
        try:
            return FontInfo(
                name=run.font.name or "未知",
                size=run.font.size.pt if run.font.size else None,
                bold=run.font.bold,
                italic=run.font.italic,
                underline=run.font.underline,
                color=str(run.font.color.rgb) if run.font.color and run.font.color.rgb else None,
                highlight=str(run.font.highlight_color) if run.font.highlight_color else None
            )
        except Exception as e:
            self.logger.warning(f"提取字体信息失败: {e}")
            return FontInfo(
                name="未知",
                size=None,
                bold=None,
                italic=None,
                underline=None,
                color=None,
                highlight=None
            )
    
    def _compare_with_original(self, document_path: str, original_path: str) -> Dict[str, Any]:
        """与原始文档对比"""
        try:
            self.logger.info("开始与原始文档对比")
            
            # 分析原始文档
            original_analysis = self._analyze_document_fonts(original_path)
            current_analysis = self._analyze_document_fonts(document_path)
            
            # 对比结果
            preserved_count = 0
            changed_count = 0
            comparison_details = []
            
            # 简化对比：比较相同位置的元素
            min_elements = min(len(original_analysis['font_elements']), 
                             len(current_analysis['font_elements']))
            
            for i in range(min_elements):
                original_element = original_analysis['font_elements'][i]
                current_element = current_analysis['font_elements'][i]
                
                original_font = original_element['font_info']
                current_font = current_element['font_info']
                
                # 比较关键字体属性
                is_preserved = (
                    original_font.name == current_font.name and
                    original_font.size == current_font.size and
                    original_font.bold == current_font.bold and
                    original_font.italic == current_font.italic
                )
                
                if is_preserved:
                    preserved_count += 1
                else:
                    changed_count += 1
                    comparison_details.append({
                        'location': current_element['location'],
                        'original_font': f"{original_font.name} {original_font.size}pt",
                        'current_font': f"{current_font.name} {current_font.size}pt",
                        'text_preview': current_element['text_preview']
                    })
            
            return {
                'comparison_performed': True,
                'preserved_count': preserved_count,
                'changed_count': changed_count,
                'comparison_details': comparison_details[:10]  # 只保留前10个差异
            }
            
        except Exception as e:
            self.logger.error(f"文档对比失败: {e}")
            return {
                'comparison_performed': False,
                'preserved_count': 0,
                'changed_count': 0,
                'comparison_details': []
            }
    
    def _generate_test_result(self, analysis: Dict[str, Any]) -> FontTestResult:
        """生成测试结果"""
        try:
            total_elements = analysis['total_elements']
            highlighted_count = analysis['highlighted_count']
            
            # 如果进行了对比
            if analysis.get('comparison_performed', False):
                preserved_count = analysis['preserved_count']
                changed_count = analysis['changed_count']
                preservation_rate = (preserved_count / max(total_elements, 1)) * 100
            else:
                # 没有原始文档对比，基于字体一致性评估
                font_consistency = analysis['font_consistency']
                if font_consistency:
                    most_common_count = max(font_consistency.values())
                    preservation_rate = (most_common_count / max(total_elements, 1)) * 100
                    preserved_count = most_common_count
                    changed_count = total_elements - most_common_count
                else:
                    preservation_rate = 0.0
                    preserved_count = 0
                    changed_count = total_elements
            
            # 判断是否通过测试
            passed = preservation_rate >= 80.0  # 80%以上认为通过
            
            # 生成详细信息
            details = []
            for element in analysis['font_elements'][:5]:  # 只显示前5个元素
                details.append({
                    'location': element['location'],
                    'text': element['text_preview'],
                    'font': f"{element['font_info'].name} {element['font_info'].size}pt",
                    'highlighted': bool(element['font_info'].highlight)
                })
            
            # 生成摘要
            if passed:
                summary = f"✅ 字体格式保持良好 (保持率: {preservation_rate:.1f}%)"
            else:
                summary = f"⚠️ 字体格式保持需要改进 (保持率: {preservation_rate:.1f}%)"
            
            if highlighted_count > 0:
                summary += f"，{highlighted_count}个元素已标记高亮"
            
            return FontTestResult(
                passed=passed,
                preservation_rate=preservation_rate,
                total_elements=total_elements,
                preserved_elements=preserved_count,
                changed_elements=changed_count,
                highlighted_elements=highlighted_count,
                details=details,
                summary=summary
            )
            
        except Exception as e:
            self.logger.error(f"生成测试结果失败: {e}")
            return FontTestResult(
                passed=False,
                preservation_rate=0.0,
                total_elements=0,
                preserved_elements=0,
                changed_elements=0,
                highlighted_elements=0,
                details=[],
                summary=f"结果生成失败: {e}"
            )
    
    def batch_test(self, document_paths: List[str]) -> List[FontTestResult]:
        """批量测试多个文档"""
        try:
            self.logger.info(f"开始批量字体测试: {len(document_paths)}个文档")
            
            results = []
            for doc_path in document_paths:
                result = self.test_document(doc_path)
                results.append(result)
            
            # 统计批量测试结果
            passed_count = sum(1 for r in results if r.passed)
            avg_preservation_rate = sum(r.preservation_rate for r in results) / len(results) if results else 0
            
            self.logger.info(f"批量测试完成: {passed_count}/{len(results)}通过，平均保持率: {avg_preservation_rate:.1f}%")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量字体测试失败: {e}")
            return []
    
    def get_test_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        try:
            if not self.test_results:
                return {
                    'total_tests': 0,
                    'passed_tests': 0,
                    'average_preservation_rate': 0.0,
                    'summary': '暂无测试结果'
                }
            
            total_tests = len(self.test_results)
            passed_tests = sum(1 for r in self.test_results if r.passed)
            avg_rate = sum(r.preservation_rate for r in self.test_results) / total_tests
            
            return {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'pass_rate': f"{(passed_tests/total_tests)*100:.1f}%",
                'average_preservation_rate': f"{avg_rate:.1f}%",
                'summary': f"共测试{total_tests}个文档，{passed_tests}个通过，平均字体保持率{avg_rate:.1f}%"
            }
            
        except Exception as e:
            self.logger.error(f"获取测试摘要失败: {e}")
            return {
                'total_tests': 0,
                'passed_tests': 0,
                'average_preservation_rate': 0.0,
                'summary': f'获取摘要失败: {e}'
            }
