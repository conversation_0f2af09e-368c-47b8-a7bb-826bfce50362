<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银企直联业务模块 - 企业信息管理系统</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header class="header">
            <h1>银企直联业务模块</h1>
            <p class="subtitle">企业信息核心库 - 交互式管理平台</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 客户选择区域 -->
            <section class="customer-selection">
                <h2>客户选择</h2>
                <div class="form-group">
                    <label for="customerSelect">选择客户企业：</label>
                    <select id="customerSelect" class="form-control">
                        <option value="">请选择客户...</option>
                    </select>
                    <button id="refreshBtn" class="btn btn-secondary">刷新列表</button>
                </div>
                <div id="loadingIndicator" class="loading hidden">
                    <span>正在加载客户列表...</span>
                </div>
            </section>

            <!-- 客户信息展示区域 -->
            <section class="customer-info" id="customerInfoSection">
                <h2>客户信息核心库</h2>
                <div id="customerDetails" class="customer-details">
                    <p class="no-selection">请先选择一个客户企业</p>
                </div>
            </section>

            <!-- 文档模板区域 -->
            <section class="document-template" id="documentSection">
                <h2>业务文档模板</h2>
                <div class="template-controls">
                    <button id="generateDocBtn" class="btn btn-primary" disabled>生成业务文档</button>
                    <button id="exportDataBtn" class="btn btn-secondary" disabled>导出客户数据</button>
                    <button id="refreshTemplatesBtn" class="btn btn-secondary">刷新模板</button>
                </div>
                <div class="template-info" id="templateInfo">
                    <h4>可用模板</h4>
                    <div id="templateList" class="template-list">
                        <p>正在加载模板列表...</p>
                    </div>
                </div>
                <div id="documentPreview" class="document-preview">
                    <h3>银企直联业务申请表</h3>
                    <div id="documentContent" class="document-content">
                        <p>请先选择客户企业以生成文档模板</p>
                    </div>
                </div>
            </section>

            <!-- 银企直联业务流程追踪器 -->
            <section class="business-workflow" id="businessWorkflowSection">
                <h2>银企直联业务办理清单</h2>
                <div class="workflow-container">
                    <div class="workflow-header">
                        <p class="workflow-description">请按照以下流程完成银企直联业务办理，勾选已完成的步骤：</p>
                    </div>

                    <div class="workflow-checklist">
                        <!-- 文档生成阶段 -->
                        <div class="workflow-stage">
                            <h3 class="stage-title">📄 文档生成阶段</h3>
                            <div class="checklist-items">
                                <div class="checklist-item">
                                    <input type="checkbox" id="serviceAgreement" class="workflow-checkbox">
                                    <label for="serviceAgreement" class="checklist-label">
                                        <span class="item-text">生成《服务协议》</span>
                                        <button class="action-btn btn-word" data-template="yingqi_zhilian_agreement_blueprint.docx" disabled>生成Word文档</button>
                                    </label>
                                </div>

                                <div class="checklist-item">
                                    <input type="checkbox" id="oaDocument" class="workflow-checkbox">
                                    <label for="oaDocument" class="checklist-label">
                                        <span class="item-text">生成《OA正文》</span>
                                        <button class="action-btn btn-word btn-gemini" data-template="yingqi_zhilian_oa_text_blueprint.docx" disabled>生成Word文档 + Gemini助手</button>
                                    </label>
                                </div>

                                <div class="checklist-item">
                                    <input type="checkbox" id="authorizationForm" class="workflow-checkbox" checked>
                                    <label for="authorizationForm" class="checklist-label">
                                        <span class="item-text">下载《对公客户授权及承诺书》（已预先勾选）</span>
                                        <button class="action-btn btn-pdf" data-template="yingqi_zhilian_authorization_form_template.pdf">下载PDF表单</button>
                                    </label>
                                </div>

                                <div class="checklist-item">
                                    <input type="checkbox" id="applicationForm" class="workflow-checkbox" checked>
                                    <label for="applicationForm" class="checklist-label">
                                        <span class="item-text">下载《对公综合服务申请书》（已预先勾选）</span>
                                        <button class="action-btn btn-pdf" data-template="yingqi_zhilian_application_form_template.pdf">下载PDF表单</button>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 业务处理阶段 -->
                        <div class="workflow-stage">
                            <h3 class="stage-title">🔄 业务处理阶段</h3>
                            <div class="checklist-items">
                                <div class="checklist-item">
                                    <input type="checkbox" id="documentsCollected" class="workflow-checkbox">
                                    <label for="documentsCollected" class="checklist-label">
                                        <span class="item-text">已收集客户用印文件返回</span>
                                        <span class="manual-action">（手动确认）</span>
                                    </label>
                                </div>

                                <div class="checklist-item">
                                    <input type="checkbox" id="systemSubmitted" class="workflow-checkbox">
                                    <label for="systemSubmitted" class="checklist-label">
                                        <span class="item-text">已提交后台系统审批</span>
                                        <span class="manual-action">（手动确认）</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 进度统计 -->
                    <div class="workflow-progress">
                        <div class="progress-info">
                            <span class="progress-text">完成进度: </span>
                            <span id="progressCount" class="progress-count">2/6</span>
                            <div class="progress-bar">
                                <div id="progressFill" class="progress-fill" style="width: 33.33%"></div>
                            </div>
                        </div>
                        <div class="workflow-actions">
                            <button id="resetWorkflowBtn" class="btn btn-secondary">重置流程</button>
                            <button id="exportWorkflowBtn" class="btn btn-primary">导出进度报告</button>
                        </div>
                    </div>
                </div>
            </section>



            <!-- 扩展信息区域 -->
            <section class="extended-info" id="extendedInfoSection">
                <h2>扩展信息</h2>
                <div id="extendedDetails" class="extended-details">
                    <p class="info-note">此区域将显示数据库中新增但前端尚未显式支持的字段</p>
                </div>
            </section>
        </main>

        <!-- 操作日志区域 -->
        <aside class="operation-log">
            <h3>操作日志</h3>
            <div id="logContainer" class="log-container">
                <p class="log-entry">系统已启动，等待用户操作...</p>
            </div>
            <button id="clearLogBtn" class="btn btn-small">清空日志</button>
        </aside>

        <!-- 错误提示模态框 -->
        <div id="errorModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>错误提示</h3>
                    <button id="closeErrorModal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button id="confirmErrorBtn" class="btn btn-primary">确定</button>
                </div>
            </div>
        </div>

        <!-- 成功提示模态框 -->
        <div id="successModal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>操作成功</h3>
                    <button id="closeSuccessModal" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="successMessage"></p>
                </div>
                <div class="modal-footer">
                    <button id="confirmSuccessBtn" class="btn btn-primary">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/api-client.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/document-generator.js"></script>
    <script src="js/oa-workspace.js"></script>
    <script src="js/workflow-manager.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
