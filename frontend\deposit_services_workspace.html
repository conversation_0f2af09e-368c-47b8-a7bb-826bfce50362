<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协定存款业务 - 企业服务系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="modules/deposit_services/styles.css">
</head>
<body>
    <div class="workspace-container">
        <!-- 顶部导航栏 -->
        <header class="workspace-header">
            <div class="header-left">
                <button id="back-to-cockpit" class="back-btn">
                    <span class="btn-icon">←</span>
                    <span class="btn-text">返回驾驶舱</span>
                </button>
                <div class="module-title">
                    <h1>💰 协定存款业务</h1>
                    <p class="module-subtitle">协定存款协议生成与管理工作台</p>
                </div>
            </div>
            <div class="header-right">
                <div class="workspace-status">
                    <span class="status-indicator active"></span>
                    <span class="status-text">工作台已就绪</span>
                </div>
            </div>
        </header>

        <!-- 主工作区 -->
        <main class="workspace-main">
            <!-- 客户信息区 -->
            <section class="customer-info-section">
                <div class="section-header">
                    <h2>👤 当前服务客户</h2>
                    <div class="customer-lock-status">
                        <span class="lock-icon">🔒</span>
                        <span class="lock-text">客户已锁定</span>
                    </div>
                </div>
                <div class="customer-info-card">
                    <div class="customer-basic-info">
                        <div class="info-item">
                            <label>公司名称：</label>
                            <span id="customer-name" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>统一社会信用代码：</label>
                            <span id="customer-code" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>法定代表人：</label>
                            <span id="customer-legal-rep" class="info-value">-</span>
                        </div>
                        <div class="info-item">
                            <label>注册地址：</label>
                            <span id="customer-address" class="info-value">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 协定存款模块内容区 -->
            <section class="module-content-section">
                <div id="module-content">
                    <!-- 协定存款模块内容将在这里动态加载 -->
                    <div class="loading-placeholder">
                        <div class="loading-spinner"></div>
                        <p>正在加载协定存款业务模块...</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- 底部状态栏 -->
        <footer class="workspace-footer">
            <div class="footer-left">
                <span class="module-info">协定存款业务模块 v1.0</span>
            </div>
            <div class="footer-right">
                <span class="last-update" id="last-update">最后更新：-</span>
            </div>
        </footer>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在处理...</p>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/api-client.js"></script>
    <script src="modules/deposit_services/main.js"></script>
    <script>
        // 协定存款工作台管理器
        class DepositWorkspaceManager {
            constructor() {
                this.currentCustomer = null;
                this.init();
            }

            async init() {
                console.log('协定存款工作台初始化...');
                
                // 绑定返回按钮
                this.bindBackButton();
                
                // 从URL参数获取客户信息
                this.loadCustomerFromParams();
                
                // 初始化协定存款模块
                await this.initDepositModule();
            }

            bindBackButton() {
                const backBtn = document.getElementById('back-to-cockpit');
                if (backBtn) {
                    backBtn.addEventListener('click', () => {
                        // 返回驾驶舱，保持客户信息
                        const params = new URLSearchParams({
                            customer_id: this.currentCustomer?.id || '',
                            customer_name: this.currentCustomer?.company_name || '',
                            customer_code: this.currentCustomer?.unified_social_credit_code || '',
                            from_module: 'deposit_services'
                        });
                        window.location.href = `index_cockpit.html?${params.toString()}`;
                    });
                }
            }

            loadCustomerFromParams() {
                const urlParams = new URLSearchParams(window.location.search);
                const customerId = urlParams.get('customer_id');
                const customerName = urlParams.get('customer_name');
                const customerCode = urlParams.get('customer_code');

                if (customerId && customerName) {
                    this.currentCustomer = {
                        id: customerId,
                        company_name: customerName,
                        unified_social_credit_code: customerCode || ''
                    };

                    // 更新页面显示
                    this.updateCustomerDisplay();
                    
                    console.log('从URL参数加载客户信息:', this.currentCustomer);
                } else {
                    console.error('缺少客户信息参数');
                    alert('缺少客户信息，将返回驾驶舱');
                    window.location.href = 'index_cockpit.html';
                }
            }

            updateCustomerDisplay() {
                if (!this.currentCustomer) return;

                document.getElementById('customer-name').textContent = this.currentCustomer.company_name;
                document.getElementById('customer-code').textContent = this.currentCustomer.unified_social_credit_code;
                
                // 尝试从API获取更详细的客户信息
                this.loadDetailedCustomerInfo();
            }

            async loadDetailedCustomerInfo() {
                try {
                    const response = await fetch(`http://127.0.0.1:5000/api/company/${this.currentCustomer.id}`);
                    if (response.ok) {
                        const result = await response.json();
                        if (result.status === 'success' && result.data) {
                            const company = result.data;
                            document.getElementById('customer-legal-rep').textContent = company.legal_representative || '-';
                            document.getElementById('customer-address').textContent = company.registered_address || '-';
                            
                            // 更新完整的客户信息
                            this.currentCustomer = { ...this.currentCustomer, ...company };
                        }
                    }
                } catch (error) {
                    console.warn('获取详细客户信息失败:', error);
                }
            }

            async initDepositModule() {
                try {
                    if (window.depositModule && this.currentCustomer) {
                        await window.depositModule.init(this.currentCustomer);
                        this.updateLastUpdate();
                        console.log('协定存款模块初始化完成');
                    } else {
                        throw new Error('协定存款模块或客户信息未准备就绪');
                    }
                } catch (error) {
                    console.error('协定存款模块初始化失败:', error);
                    document.getElementById('module-content').innerHTML = `
                        <div class="error-placeholder">
                            <div class="error-icon">❌</div>
                            <h3>模块加载失败</h3>
                            <p>${error.message}</p>
                            <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                        </div>
                    `;
                }
            }

            updateLastUpdate() {
                const now = new Date();
                const timeStr = now.toLocaleString('zh-CN');
                document.getElementById('last-update').textContent = `最后更新：${timeStr}`;
            }
        }

        // 启动工作台管理器
        window.depositWorkspaceManager = new DepositWorkspaceManager();
    </script>
</body>
</html>
