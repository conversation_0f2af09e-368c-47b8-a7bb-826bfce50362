<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面状态</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .debug-title { font-weight: bold; color: #333; }
        .debug-content { margin-top: 10px; }
        .stage-info { margin: 5px 0; padding: 5px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>页面状态调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">URL参数:</div>
        <div class="debug-content" id="url-params"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">页面元素显示状态:</div>
        <div class="debug-content" id="element-states"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">JavaScript对象状态:</div>
        <div class="debug-content" id="js-states"></div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">操作:</div>
        <div class="debug-content">
            <button onclick="checkMainPage()">检查主页面状态</button>
            <button onclick="clearURLParams()">清除URL参数并重新加载</button>
        </div>
    </div>

    <script>
        // 显示URL参数
        function showURLParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const paramsDiv = document.getElementById('url-params');
            
            if (urlParams.toString()) {
                let paramsHTML = '<ul>';
                for (const [key, value] of urlParams) {
                    paramsHTML += `<li><strong>${key}:</strong> ${value}</li>`;
                }
                paramsHTML += '</ul>';
                paramsDiv.innerHTML = paramsHTML;
            } else {
                paramsDiv.innerHTML = '无URL参数';
            }
        }
        
        // 检查主页面状态
        function checkMainPage() {
            // 在新窗口打开主页面并检查状态
            const mainWindow = window.open('http://localhost:8080/index_cockpit.html', '_blank');
            
            setTimeout(() => {
                try {
                    const doc = mainWindow.document;
                    const customerStage = doc.getElementById('customer-selection-stage');
                    const moduleStage = doc.getElementById('module-selection-stage');
                    const workflowStage = doc.getElementById('workflow-stage');
                    
                    const statesDiv = document.getElementById('element-states');
                    statesDiv.innerHTML = `
                        <div class="stage-info">客户选择舞台: ${customerStage ? customerStage.style.display || 'block' : '未找到'}</div>
                        <div class="stage-info">模块选择舞台: ${moduleStage ? moduleStage.style.display || 'block' : '未找到'}</div>
                        <div class="stage-info">工作流程舞台: ${workflowStage ? workflowStage.style.display || 'block' : '未找到'}</div>
                    `;
                    
                    // 检查JavaScript对象
                    if (mainWindow.safeCockpitManager) {
                        const jsStatesDiv = document.getElementById('js-states');
                        jsStatesDiv.innerHTML = `
                            <div class="stage-info">SafeCockpitManager存在: 是</div>
                            <div class="stage-info">当前客户: ${mainWindow.safeCockpitManager.currentCustomer ? mainWindow.safeCockpitManager.currentCustomer.company_name : '无'}</div>
                            <div class="stage-info">客户锁定状态: ${mainWindow.safeCockpitManager.isCustomerLocked || '否'}</div>
                        `;
                    } else {
                        document.getElementById('js-states').innerHTML = 'SafeCockpitManager未初始化';
                    }
                } catch (e) {
                    document.getElementById('element-states').innerHTML = '无法访问主页面状态: ' + e.message;
                }
            }, 2000);
        }
        
        // 清除URL参数
        function clearURLParams() {
            window.location.href = 'http://localhost:8080/index_cockpit.html';
        }
        
        // 页面加载时显示URL参数
        showURLParams();
    </script>
</body>
</html>
