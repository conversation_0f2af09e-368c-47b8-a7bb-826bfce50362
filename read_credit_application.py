#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取信贷业务申请书并识别标黄部分
"""

import openpyxl
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CreditApplicationReader:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.file_path = self.project_root / "templates" / "contract_disbursement" / "信贷业务申请书.xlsx"
        
    def read_and_analyze(self):
        """读取并分析信贷业务申请书"""
        logger.info("📖 开始读取信贷业务申请书...")
        
        if not self.file_path.exists():
            logger.error(f"❌ 文件不存在: {self.file_path}")
            return
        
        try:
            # 加载工作簿
            workbook = openpyxl.load_workbook(self.file_path, data_only=False)
            
            print("\n" + "="*80)
            print("📋 信贷业务申请书内容分析")
            print("="*80)
            print(f"📁 文件路径: {self.file_path}")
            print(f"📊 工作表数量: {len(workbook.sheetnames)}")
            print(f"📝 工作表名称: {', '.join(workbook.sheetnames)}")
            
            # 分析每个工作表
            for sheet_name in workbook.sheetnames:
                self._analyze_worksheet(workbook[sheet_name], sheet_name)
            
            print("\n" + "="*80)
            print("✅ 分析完成")
            print("="*80)
            
        except Exception as e:
            logger.error(f"❌ 读取文件失败: {e}")
    
    def _analyze_worksheet(self, worksheet, sheet_name):
        """分析单个工作表"""
        print(f"\n📄 工作表: {sheet_name}")
        print("-" * 60)
        
        # 获取工作表的使用范围
        if worksheet.max_row == 1 and worksheet.max_column == 1:
            print("   📝 工作表为空")
            return
        
        print(f"   📐 使用范围: A1:{openpyxl.utils.get_column_letter(worksheet.max_column)}{worksheet.max_row}")
        print(f"   📊 数据行数: {worksheet.max_row}")
        print(f"   📊 数据列数: {worksheet.max_column}")
        
        # 查找所有有内容的单元格
        content_cells = []
        highlighted_cells = []
        
        for row in range(1, worksheet.max_row + 1):
            for col in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row, column=col)
                
                if cell.value is not None:
                    content_cells.append({
                        'address': cell.coordinate,
                        'value': str(cell.value),
                        'row': row,
                        'col': col
                    })
                    
                    # 检查单元格是否有背景色（标黄）
                    if self._is_cell_highlighted(cell):
                        highlighted_cells.append({
                            'address': cell.coordinate,
                            'value': str(cell.value),
                            'row': row,
                            'col': col,
                            'fill_color': self._get_fill_color(cell)
                        })
        
        print(f"   📝 有内容的单元格: {len(content_cells)}个")
        print(f"   🎨 标黄的单元格: {len(highlighted_cells)}个")
        
        # 显示标黄的单元格
        if highlighted_cells:
            print("\n   🎨 标黄部分详情:")
            for cell_info in highlighted_cells:
                print(f"      {cell_info['address']}: {cell_info['value']} (颜色: {cell_info['fill_color']})")
        
        # 显示所有有内容的单元格（前20个）
        print(f"\n   📝 内容预览 (前20个单元格):")
        for i, cell_info in enumerate(content_cells[:20]):
            highlight_mark = " 🎨" if any(h['address'] == cell_info['address'] for h in highlighted_cells) else ""
            print(f"      {cell_info['address']}: {cell_info['value'][:50]}{'...' if len(cell_info['value']) > 50 else ''}{highlight_mark}")
        
        if len(content_cells) > 20:
            print(f"      ... 还有 {len(content_cells) - 20} 个单元格")
        
        # 尝试识别表格结构
        self._identify_table_structure(worksheet, content_cells)
    
    def _is_cell_highlighted(self, cell):
        """检查单元格是否被高亮（标黄）"""
        if cell.fill and cell.fill.fill_type:
            # 检查填充类型和颜色
            if hasattr(cell.fill, 'start_color') and cell.fill.start_color:
                color = cell.fill.start_color
                if hasattr(color, 'rgb') and color.rgb:
                    # 检查是否为黄色系
                    rgb = color.rgb
                    if isinstance(rgb, str) and len(rgb) == 8:
                        # ARGB格式，去掉Alpha通道
                        rgb = rgb[2:]
                    
                    # 常见的黄色RGB值
                    yellow_colors = [
                        'FFFF00',  # 纯黄色
                        'FFFF99',  # 浅黄色
                        'FFFFCC',  # 很浅的黄色
                        'FFF2CC',  # Excel默认浅黄色
                        'FFEB9C',  # Excel默认黄色
                        'FFD966',  # 深一点的黄色
                    ]
                    
                    return rgb.upper() in yellow_colors or self._is_yellow_like(rgb)
        return False
    
    def _is_yellow_like(self, rgb_hex):
        """判断颜色是否类似黄色"""
        try:
            if len(rgb_hex) == 6:
                r = int(rgb_hex[0:2], 16)
                g = int(rgb_hex[2:4], 16)
                b = int(rgb_hex[4:6], 16)
                
                # 黄色特征：红色和绿色值较高，蓝色值较低
                return r > 200 and g > 200 and b < 150
        except:
            pass
        return False
    
    def _get_fill_color(self, cell):
        """获取单元格填充颜色"""
        if cell.fill and cell.fill.fill_type:
            if hasattr(cell.fill, 'start_color') and cell.fill.start_color:
                color = cell.fill.start_color
                if hasattr(color, 'rgb') and color.rgb:
                    return color.rgb
        return "无颜色"
    
    def _identify_table_structure(self, worksheet, content_cells):
        """尝试识别表格结构"""
        print(f"\n   📋 表格结构分析:")
        
        # 按行分组
        rows_data = {}
        for cell_info in content_cells:
            row = cell_info['row']
            if row not in rows_data:
                rows_data[row] = []
            rows_data[row].append(cell_info)
        
        # 显示前几行的结构
        for row_num in sorted(rows_data.keys())[:10]:
            row_cells = sorted(rows_data[row_num], key=lambda x: x['col'])
            row_content = " | ".join([f"{cell['value'][:20]}{'...' if len(cell['value']) > 20 else ''}" for cell in row_cells])
            print(f"      第{row_num}行: {row_content}")
        
        if len(rows_data) > 10:
            print(f"      ... 还有 {len(rows_data) - 10} 行")

def main():
    reader = CreditApplicationReader()
    reader.read_and_analyze()

if __name__ == "__main__":
    main()
