#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
初始化数据库
"""

import sqlite3
import os

def init_database():
    """初始化数据库"""
    
    db_path = "database/enterprise_service.db"
    
    print("🔄 初始化数据库...")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 执行创建表脚本
        print("📋 步骤1: 创建表结构...")
        with open("database/01_create_tables.sql", "r", encoding="utf-8") as f:
            create_sql = f.read()
        
        # 分割SQL语句并执行
        statements = create_sql.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement:
                cursor.execute(statement)
        
        print("✅ 表结构创建完成")
        
        # 2. 插入初始数据
        print("📋 步骤2: 插入初始数据...")
        with open("database/02_insert_initial_data.sql", "r", encoding="utf-8") as f:
            insert_sql = f.read()
        
        statements = insert_sql.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.upper().startswith('--'):
                try:
                    cursor.execute(statement)
                except Exception as e:
                    print(f"⚠️ 执行语句时出错: {statement[:50]}... 错误: {e}")
        
        print("✅ 初始数据插入完成")
        
        # 3. 扩展表结构
        print("📋 步骤3: 扩展表结构...")
        with open("database/03_extend_companies_table.sql", "r", encoding="utf-8") as f:
            extend_sql = f.read()
        
        statements = extend_sql.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.upper().startswith('--'):
                try:
                    cursor.execute(statement)
                except Exception as e:
                    print(f"⚠️ 执行语句时出错: {statement[:50]}... 错误: {e}")
        
        print("✅ 表结构扩展完成")
        
        # 提交事务
        conn.commit()
        
        # 验证初始化结果
        print("\n🔍 验证初始化结果...")
        
        # 检查表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ 创建了 {len(tables)} 个表: {[table[0] for table in tables]}")
        
        # 检查公司数据
        cursor.execute("SELECT COUNT(*) FROM companies")
        company_count = cursor.fetchone()[0]
        print(f"✅ 公司记录数: {company_count}")
        
        if company_count > 0:
            cursor.execute("SELECT id, company_name FROM companies")
            companies = cursor.fetchall()
            print("📋 公司列表:")
            for company in companies:
                print(f"   - {company[1]} (ID: {company[0]})")
        
        conn.close()
        
        print("\n🎉 数据库初始化完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def add_shenguang_to_db():
    """将神光光学添加到数据库"""
    
    print("\n🔄 添加神光光学集团有限公司到数据库...")
    
    try:
        conn = sqlite3.connect("database/enterprise_service.db")
        cursor = conn.cursor()
        
        # 插入神光光学数据
        insert_sql = """
            INSERT INTO companies (
                id, company_name, unified_social_credit_code, 
                registered_address, legal_representative, business_description,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """
        
        company_data = (
            "14371dda-2f8f-4d4c-82e6-c431dcf3b146",
            "神光光学集团有限公司",
            "91510100MA62TGHL5X",
            "四川省成都市成华区建设南街9号2层",
            "贾秉炜",
            "主营业务为高纯度合成熔石英的研发和生产，是可控核聚变、半导体光掩膜版、精密光学仪器等领域此前'卡脖子'的关键上游原材料。国家级专精特新小巨人企业。"
        )
        
        cursor.execute(insert_sql, company_data)
        conn.commit()
        
        print("✅ 神光光学集团有限公司添加成功")
        
        # 验证添加结果
        cursor.execute("""
            SELECT company_name, unified_social_credit_code, registered_address, legal_representative
            FROM companies 
            WHERE id = ?
        """, ("14371dda-2f8f-4d4c-82e6-c431dcf3b146",))
        
        result = cursor.fetchone()
        if result:
            print("🔍 验证结果:")
            print(f"   公司名称: {result[0]}")
            print(f"   信用代码: {result[1]}")
            print(f"   注册地址: {result[2]}")
            print(f"   法定代表人: {result[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加神光光学失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 企业服务数据库初始化")
    print("=" * 60)
    
    # 初始化数据库
    if init_database():
        # 添加神光光学
        add_shenguang_to_db()
        
        print("\n" + "=" * 60)
        print("🎉 数据库初始化和数据添加完成!")
        print("🚀 现在可以使用企业服务系统了")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 数据库初始化失败!")
        print("=" * 60)
