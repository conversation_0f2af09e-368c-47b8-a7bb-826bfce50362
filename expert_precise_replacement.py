#!/usr/bin/env python3
"""
专家级精确替换 - 按照预期效果生成第一个版本
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
import sqlite3
import re

def expert_precise_replacement():
    print('=== 专家级精确替换 ===\n')
    
    # 文件路径
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 1. 提取源数据
    print('📖 提取源数据...')
    precondition_text = extract_precondition_from_quota(quota_file)
    continuous_conditions = extract_continuous_conditions_from_quota(quota_file)
    loan_conditions = extract_loan_conditions_from_business(business_file)
    
    # 2. 获取企业数据
    print('📊 获取企业数据...')
    company_data = get_zkzr_company_data()
    
    # 3. 加载模板并进行专家级替换
    print('🔄 进行专家级精确替换...')
    doc = docx.Document(template_file)
    
    # 4. 替换基础信息
    replace_basic_company_info(doc, company_data)
    
    # 5. 替换三个核心部分
    replace_core_conditions(doc, precondition_text, continuous_conditions, loan_conditions)
    
    # 6. 保存专家版本
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(f'test_output/落实情况表_专家版本_{timestamp}.docx')
    doc.save(output_path)
    
    print(f'✅ 专家版本生成完成！')
    print(f'📁 输出文件: {output_path}')
    
    # 7. 生成替换报告
    generate_replacement_report(company_data, precondition_text, continuous_conditions, loan_conditions)
    
    return output_path

def extract_precondition_from_quota(quota_file):
    """从额度申报书提取用信前提条件"""
    doc = docx.Document(quota_file)
    # 第5个表格，第2行，第2列
    table = doc.tables[4]
    text = table.rows[1].cells[1].text.strip()
    print(f'  ✅ 用信前提条件: {len(text)}字符')
    return text

def extract_continuous_conditions_from_quota(quota_file):
    """从额度申报书提取持续条件完整表格"""
    doc = docx.Document(quota_file)
    # 第6个表格 - 持续条件表格
    table = doc.tables[5]
    
    conditions_data = []
    for row_idx, row in enumerate(table.rows):
        row_data = []
        for cell in row.cells:
            row_data.append(cell.text.strip())
        conditions_data.append(row_data)
    
    print(f'  ✅ 持续条件表格: {len(conditions_data)}行 x {len(conditions_data[0]) if conditions_data else 0}列')
    return conditions_data

def extract_loan_conditions_from_business(business_file):
    """从业务申报书提取贷款条件"""
    doc = docx.Document(business_file)
    # 第7个表格 - 贷款条件
    table = doc.tables[6]
    
    conditions = []
    for row in table.rows:
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            if number and content:
                conditions.append({
                    'number': number,
                    'content': content
                })
    
    print(f'  ✅ 贷款条件: {len(conditions)}条')
    return conditions

def get_zkzr_company_data():
    """获取中科卓尔企业数据"""
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT company_name, unified_social_credit_code, legal_representative,
                   registered_capital, registered_address, contact_phone,
                   finance_manager_name, finance_manager_phone, spouse_name,
                   total_assets, total_liabilities, environmental_classification,
                   company_account
            FROM companies 
            WHERE company_name LIKE '%中科卓尔%'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'company_name': result[0],
                'unified_social_credit_code': result[1],
                'legal_representative': result[2],
                'registered_capital': result[3],
                'registered_address': result[4],
                'contact_phone': result[5],
                'finance_manager_name': result[6],
                'finance_manager_phone': result[7],
                'spouse_name': result[8],
                'total_assets': result[9],
                'total_liabilities': result[10],
                'environmental_classification': result[11],
                'company_account': result[12]
            }
        else:
            print('  ❌ 未找到企业数据')
            return None
            
    except Exception as e:
        print(f'  ❌ 数据库查询失败: {e}')
        return None

def replace_basic_company_info(doc, company_data):
    """替换基础企业信息并标记为红色"""
    if not company_data:
        return
    
    print('🔄 替换基础企业信息...')
    
    # 定义替换映射
    replacements = {
        '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
        '杨伟': company_data['legal_representative'],
        '王斯颖': company_data['spouse_name'],
        '91510100MA6CGUGA1W': company_data['unified_social_credit_code']
    }
    
    # 在表格中进行替换
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    for old_text, new_text in replacements.items():
                        if old_text in paragraph.text:
                            print(f'  🔄 替换: {old_text} → {new_text}')
                            replace_text_in_paragraph(paragraph, old_text, new_text, make_red=True)

def replace_core_conditions(doc, precondition_text, continuous_conditions, loan_conditions):
    """替换三个核心条件部分"""
    print('🔄 替换核心条件部分...')
    
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                
                # 1. 替换用信前提条件
                if ('用信前提条件' in cell_text and 
                    '额度申报书单户综合融资总量使用前提条件' in cell_text):
                    print(f'  🔄 替换用信前提条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    replace_cell_content(cell, precondition_text, make_red=True)
                
                # 2. 查找并替换持续条件部分
                elif ('持续条件' in cell_text and len(cell_text) > 100):
                    print(f'  🔄 替换持续条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    continuous_text = format_continuous_conditions(continuous_conditions)
                    replace_cell_content(cell, continuous_text, make_red=True)
                
                # 3. 替换贷款条件
                elif ('批复条件' in cell_text and '业务批复要求' in cell_text):
                    print(f'  🔄 替换贷款条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    loan_text = format_loan_conditions(loan_conditions)
                    # 在现有内容后添加贷款条件
                    add_loan_conditions_to_cell(cell, loan_text)

def replace_text_in_paragraph(paragraph, old_text, new_text, make_red=False):
    """在段落中替换文本"""
    if old_text in paragraph.text:
        # 保存原有格式
        original_runs = list(paragraph.runs)
        paragraph.clear()
        
        # 重新构建段落，替换文本
        full_text = ''.join(run.text for run in original_runs)
        new_full_text = full_text.replace(old_text, new_text)
        
        # 添加新文本
        if make_red and old_text != new_text:
            # 分割文本，只对替换的部分标红
            parts = new_full_text.split(new_text)
            for i, part in enumerate(parts):
                if part:
                    paragraph.add_run(part)
                if i < len(parts) - 1:
                    red_run = paragraph.add_run(new_text)
                    red_run.font.color.rgb = RGBColor(255, 0, 0)
        else:
            paragraph.add_run(new_full_text)

def replace_cell_content(cell, new_content, make_red=False):
    """替换单元格内容"""
    # 清空单元格
    cell._element.clear_content()
    
    # 添加新内容
    paragraph = cell.add_paragraph()
    run = paragraph.add_run(new_content)
    
    if make_red:
        run.font.color.rgb = RGBColor(255, 0, 0)

def format_continuous_conditions(continuous_conditions):
    """格式化持续条件为文本"""
    if not continuous_conditions:
        return "持续条件数据未找到"
    
    formatted_text = "二、单户综合融资总量用信持续条件及落实情况\n\n"
    
    # 假设第一行是表头
    if len(continuous_conditions) > 1:
        headers = continuous_conditions[0]
        for row_idx in range(1, len(continuous_conditions)):
            row_data = continuous_conditions[row_idx]
            if len(row_data) >= 4:
                formatted_text += f"{row_data[0]}：\n"
                formatted_text += f"本次设置：{row_data[1]}\n"
                formatted_text += f"前次设置：{row_data[2]}\n"
                formatted_text += f"落实情况：{row_data[3]}\n\n"
    
    return formatted_text

def format_loan_conditions(loan_conditions):
    """格式化贷款条件为文本"""
    if not loan_conditions:
        return "贷款条件数据未找到"
    
    formatted_text = "\n\n三、单笔业务申报书中列明的贷款条件及落实情况\n\n"
    
    for condition in loan_conditions:
        formatted_text += f"{condition['number']}. {condition['content']}\n\n"
    
    return formatted_text

def add_loan_conditions_to_cell(cell, loan_text):
    """在单元格现有内容后添加贷款条件"""
    # 在现有内容后添加
    paragraph = cell.add_paragraph()
    run = paragraph.add_run(loan_text)
    run.font.color.rgb = RGBColor(255, 0, 0)

def generate_replacement_report(company_data, precondition_text, continuous_conditions, loan_conditions):
    """生成替换报告"""
    print('\n📊 替换报告:')
    print('=' * 50)
    
    if company_data:
        print('✅ 基础信息替换:')
        print(f'  - 公司名称: {company_data["company_name"]}')
        print(f'  - 法定代表人: {company_data["legal_representative"]}')
        print(f'  - 配偶: {company_data["spouse_name"]}')
        print(f'  - 统一社会信用代码: {company_data["unified_social_credit_code"]}')
    
    print(f'\n✅ 核心条件替换:')
    print(f'  - 用信前提条件: {len(precondition_text)}字符')
    print(f'  - 持续条件: {len(continuous_conditions)}行')
    print(f'  - 贷款条件: {len(loan_conditions)}条')
    
    print('\n🎯 所有替换内容均已标记为红色显示')

if __name__ == "__main__":
    expert_precise_replacement()
