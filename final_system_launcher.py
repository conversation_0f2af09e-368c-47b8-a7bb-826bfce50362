#!/usr/bin/env python3
"""
最终系统启动器 - 专家固化版本
"""

import subprocess
import time
import webbrowser
import requests
from pathlib import Path

def final_system_launcher():
    print('=== 最终系统启动器 - 专家固化版本 ===\n')
    
    # 1. 启动简化API
    print('🚀 启动API服务...')
    api_process = start_simplified_api()
    
    # 2. 启动HTTP服务器
    print('🌐 启动HTTP服务器...')
    web_process = start_web_server()
    
    # 3. 等待服务启动
    print('⏳ 等待服务启动...')
    time.sleep(3)
    
    # 4. 验证服务
    print('✅ 验证服务状态...')
    if verify_services():
        print('🎉 系统启动成功！')
        open_interface()
    else:
        print('❌ 系统启动失败')
        cleanup_processes(api_process, web_process)

def start_simplified_api():
    """启动简化API服务"""
    api_code = '''
from flask import Flask, jsonify, request
from flask_cors import CORS
import sqlite3

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return jsonify({"status": "success", "message": "API服务正常"})

@app.route('/health')
def health():
    return jsonify({"status": "success", "message": "API服务运行正常"})

@app.route('/api/companies')
def get_companies():
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, company_name FROM companies ORDER BY company_name")
        companies = [{"id": row[0], "company_name": row[1]} for row in cursor.fetchall()]
        conn.close()
        return jsonify({"status": "success", "data": companies, "message": f"获取{len(companies)}家公司"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/api/yingqi-zhilian/generate', methods=['POST'])
def generate_yingqi():
    data = request.get_json()
    return jsonify({"status": "success", "message": "银企直联文档生成成功", "data": {"file_path": "test.docx"}})

@app.route('/api/deposit-services/generate', methods=['POST'])
def generate_deposit():
    data = request.get_json()
    return jsonify({"status": "success", "message": "协定存款文档生成成功", "data": {"file_path": "test.docx"}})

@app.route('/api/contract-disbursement/generate', methods=['POST'])
def generate_contract():
    data = request.get_json()
    return jsonify({"status": "success", "message": "合同支用文档生成成功", "data": {"file_path": "test.docx"}})

if __name__ == '__main__':
    print("🚀 启动简化API服务器...")
    app.run(host='0.0.0.0', port=5000, debug=False)
'''
    
    # 写入临时API文件
    with open('temp_api.py', 'w', encoding='utf-8') as f:
        f.write(api_code)
    
    # 启动API进程
    process = subprocess.Popen(['python', 'temp_api.py'], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE)
    return process

def start_web_server():
    """启动Web服务器"""
    process = subprocess.Popen(['python', '-m', 'http.server', '8080', '-d', 'frontend'],
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE)
    return process

def verify_services():
    """验证服务状态"""
    max_retries = 10
    
    for i in range(max_retries):
        try:
            # 测试API
            api_response = requests.get('http://localhost:5000/health', timeout=2)
            if api_response.status_code != 200:
                raise Exception("API响应错误")
            
            # 测试公司数据
            companies_response = requests.get('http://localhost:5000/api/companies', timeout=2)
            if companies_response.status_code != 200:
                raise Exception("公司数据API错误")
            
            companies_data = companies_response.json()
            if len(companies_data.get('data', [])) == 0:
                raise Exception("没有公司数据")
            
            print(f'  ✅ API服务正常 - 找到{len(companies_data["data"])}家公司')
            
            # 测试Web服务器
            web_response = requests.get('http://localhost:8080', timeout=2)
            if web_response.status_code == 200:
                print('  ✅ Web服务器正常')
                return True
            
        except Exception as e:
            print(f'  ⏳ 等待服务启动... ({i+1}/{max_retries}) - {e}')
            time.sleep(2)
    
    return False

def open_interface():
    """打开界面"""
    url = 'http://localhost:8080/index_unified_cockpit.html'
    print(f'🌐 打开界面: {url}')
    
    try:
        webbrowser.open(url)
        print('✅ 界面已打开')
    except Exception as e:
        print(f'❌ 无法打开浏览器: {e}')
        print(f'请手动访问: {url}')
    
    print('\n📋 系统信息:')
    print('  - API服务: http://localhost:5000')
    print('  - Web服务: http://localhost:8080')
    print('  - 主界面: http://localhost:8080/index_unified_cockpit.html')
    print('\n💡 使用说明:')
    print('  1. 选择客户')
    print('  2. 选择业务模块')
    print('  3. 直接在当前页面生成文档')
    print('  4. 按 Ctrl+C 停止所有服务')

def cleanup_processes(*processes):
    """清理进程"""
    for process in processes:
        if process:
            process.terminate()

if __name__ == "__main__":
    try:
        final_system_launcher()
        
        # 保持运行
        print('\n⏳ 系统运行中... 按 Ctrl+C 停止')
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print('\n🛑 正在停止系统...')
        # 清理临时文件
        if Path('temp_api.py').exists():
            Path('temp_api.py').unlink()
        print('✅ 系统已停止')
