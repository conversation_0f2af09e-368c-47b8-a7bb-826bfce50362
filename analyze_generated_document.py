#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析生成的条件落实情况表，检查替换问题
"""

import docx
from pathlib import Path

def analyze_generated_document():
    """分析生成的文档"""
    project_root = Path(__file__).parent
    generated_file = project_root / "test_output" / "精确条件落实情况表_成都中科卓尔智能科技集团有限公司.docx"
    
    print("🔍 分析生成的条件落实情况表")
    print("="*60)
    
    if not generated_file.exists():
        print(f"❌ 文件不存在: {generated_file}")
        return
    
    try:
        doc = docx.Document(generated_file)
        
        print(f"📄 文件: {generated_file.name}")
        print(f"📊 段落总数: {len(doc.paragraphs)}")
        print(f"📊 表格总数: {len(doc.tables)}")
        
        # 分析表格内容和颜色标记
        if doc.tables:
            main_table = doc.tables[0]
            print(f"\n📊 主表格分析: {len(main_table.rows)}行 x {len(main_table.columns)}列")
            print("-" * 60)
            
            for row_idx, row in enumerate(main_table.rows):
                print(f"\n🔹 行{row_idx + 1}:")
                
                for col_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f"   列{col_idx + 1}: {cell_text[:100]}{'...' if len(cell_text) > 100 else ''}")
                        
                        # 检查颜色标记
                        highlighted_texts = []
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                if run.font.highlight_color:
                                    highlighted_texts.append(f"'{run.text}' ({run.font.highlight_color})")
                        
                        if highlighted_texts:
                            print(f"         🎨 高亮内容: {', '.join(highlighted_texts)}")
                        
                        # 检查是否有问题
                        problems = []
                        
                        # 检查是否替换了标题
                        if any(title in cell_text for title in ["借款人：", "审批结论文号：", "项目名称：", "本次支用金额："]):
                            if "成都中科卓尔" not in cell_text and "中科卓尔" in cell_text:
                                problems.append("可能标题被误替换")
                        
                        # 检查是否有大段绿色内容
                        total_highlighted_length = sum(len(run.text) for paragraph in cell.paragraphs 
                                                     for run in paragraph.runs 
                                                     if run.font.highlight_color)
                        if total_highlighted_length > 1000:
                            problems.append(f"大段绿色内容({total_highlighted_length}字符)")
                        
                        # 检查是否有重复内容
                        if cell_text.count("持续条件") > 1:
                            problems.append("可能有重复的持续条件")
                        
                        if problems:
                            print(f"         ⚠️ 问题: {', '.join(problems)}")
        
        # 分析具体的替换问题
        print(f"\n🔍 替换问题分析:")
        print("-" * 60)
        
        # 检查企业名称替换
        full_text = ""
        for paragraph in doc.paragraphs:
            full_text += paragraph.text + "\n"
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    full_text += cell.text + " "
        
        # 检查各种替换问题
        issues = []
        
        if "成都中科卓尔智能科技集团有限公司" not in full_text:
            issues.append("企业全名可能被错误替换")
        
        if full_text.count("中科卓尔") < 3:
            issues.append("企业简称替换可能有问题")
        
        if "PIFU510000000N202407210" not in full_text:
            issues.append("额度编号可能未正确替换")
        
        if "KHED510488500202522805" not in full_text:
            issues.append("业务编号可能未正确替换")
        
        if "1300万元" not in full_text:
            issues.append("支用金额可能未正确填写")
        
        # 检查是否有过多的绿色标记
        green_content_count = 0
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            if run.font.highlight_color and len(run.text) > 50:
                                green_content_count += 1
        
        if green_content_count > 10:
            issues.append(f"过多的绿色标记内容({green_content_count}个)")
        
        if issues:
            print("❌ 发现的问题:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ 未发现明显问题")
        
        # 提供修复建议
        print(f"\n💡 修复建议:")
        print("-" * 60)
        print("1. 替换策略问题:")
        print("   - 可能在错误的位置进行了替换")
        print("   - 需要更精确的文本定位")
        print("   - 避免替换标题和固定文字")
        
        print("\n2. 颜色标记问题:")
        print("   - 可能标记了整个段落而不是特定文字")
        print("   - 需要只标记实际替换的内容")
        print("   - 避免标记原文条件的大段内容")
        
        print("\n3. 内容添加问题:")
        print("   - 申报书原文可能被重复添加")
        print("   - 需要检查是否在正确位置添加内容")
        print("   - 避免添加过长的原文内容")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    analyze_generated_document()

if __name__ == "__main__":
    main()
