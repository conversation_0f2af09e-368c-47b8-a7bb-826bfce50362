/**
 * API客户端模块
 * 负责与后端API的所有通信
 */

class APIClient {
    constructor(baseURL = 'http://127.0.0.1:5000') {
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    /**
     * 通用HTTP请求方法
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new APIError(
                    data.message || `HTTP ${response.status}`,
                    response.status,
                    data
                );
            }

            return data;

        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            
            // 网络错误或其他错误
            console.error(`API请求失败: ${endpoint}`, error);
            throw new APIError(
                '网络连接失败，请检查API服务是否正常运行',
                0,
                { originalError: error.message }
            );
        }
    }

    /**
     * GET请求
     */
    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    /**
     * POST请求
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT请求
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE请求
     */
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }

    // ==================== 企业相关API ====================

    /**
     * 获取所有公司列表（用于下拉框）
     */
    async getCompanies() {
        const response = await this.get('/api/companies');
        return response.data || [];
    }

    /**
     * 获取公司详细信息
     */
    async getCompanyDetail(companyId) {
        if (!companyId) {
            throw new APIError('公司ID不能为空', 400);
        }
        
        const response = await this.get(`/api/company/${companyId}`);
        return response.data;
    }

    /**
     * 创建新公司
     */
    async createCompany(companyData) {
        if (!companyData.unified_social_credit_code || !companyData.company_name) {
            throw new APIError('公司名称和统一社会信用代码不能为空', 400);
        }

        const response = await this.post('/api/companies', companyData);
        return response.data;
    }

    /**
     * 更新公司信息
     */
    async updateCompany(companyId, companyData) {
        if (!companyId) {
            throw new APIError('公司ID不能为空', 400);
        }

        const response = await this.put(`/api/company/${companyId}`, companyData);
        return response.data;
    }

    /**
     * 获取公司变更历史
     */
    async getCompanyHistory(companyId) {
        if (!companyId) {
            throw new APIError('公司ID不能为空', 400);
        }

        const response = await this.get(`/api/company/${companyId}/history`);
        return response.data || [];
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            const response = await this.get('/health');
            return response.data;
        } catch (error) {
            throw new APIError('API服务不可用', 503, { originalError: error.message });
        }
    }

    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            console.error('API连接测试失败:', error);
            return false;
        }
    }
}

/**
 * API错误类
 */
class APIError extends Error {
    constructor(message, status = 500, data = null) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.data = data;
    }

    /**
     * 判断是否为网络错误
     */
    isNetworkError() {
        return this.status === 0;
    }

    /**
     * 判断是否为客户端错误
     */
    isClientError() {
        return this.status >= 400 && this.status < 500;
    }

    /**
     * 判断是否为服务器错误
     */
    isServerError() {
        return this.status >= 500;
    }

    /**
     * 获取用户友好的错误消息
     */
    getUserMessage() {
        if (this.isNetworkError()) {
            return '网络连接失败，请检查网络连接或API服务状态';
        }
        
        if (this.status === 404) {
            return '请求的资源不存在';
        }
        
        if (this.status === 409) {
            return '数据冲突，可能是重复的记录';
        }
        
        if (this.isServerError()) {
            return '服务器内部错误，请稍后重试';
        }
        
        return this.message;
    }
}

// 创建全局API客户端实例
const apiClient = new APIClient();

// 导出供其他模块使用
window.APIClient = APIClient;
window.APIError = APIError;
window.apiClient = apiClient;
