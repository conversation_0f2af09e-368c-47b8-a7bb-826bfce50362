#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的表格创建器
在指定位置创建真正的Word表格，而不是文字描述
"""

import shutil
from pathlib import Path
from datetime import datetime
import docx
from docx.shared import RGBColor, Inches
from docx.enum.table import WD_TABLE_ALIGNMENT
import sqlite3

class RealTableCreator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_dir = self.project_root / "templates" / "contract_disbursement"
        self.test_output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 确保输出目录存在
        self.test_output_dir.mkdir(exist_ok=True)
        
        # 中科卓尔公司ID
        self.zkzr_company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
        
        # 绿色RGB值
        self.green_rgb = RGBColor(0, 128, 0)
    
    def get_company_data(self):
        """获取中科卓尔的完整数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM companies WHERE id = ?", (self.zkzr_company_id,))
            company_row = cursor.fetchone()
            
            if not company_row:
                raise Exception("未找到中科卓尔公司数据")
            
            cursor.execute("PRAGMA table_info(companies)")
            columns = [row[1] for row in cursor.fetchall()]
            
            company_data = dict(zip(columns, company_row))
            
            # 获取合同信息
            cursor.execute("""
                SELECT * FROM contracts 
                WHERE company_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            """, (self.zkzr_company_id,))
            
            contract_row = cursor.fetchone()
            if contract_row:
                cursor.execute("PRAGMA table_info(contracts)")
                contract_columns = [row[1] for row in cursor.fetchall()]
                company_data['contract'] = dict(zip(contract_columns, contract_row))
            
            conn.close()
            return company_data
            
        except Exception as e:
            print(f"获取公司数据失败: {e}")
            return None
    
    def create_real_table_in_cell(self, cell, table_type):
        """在单元格中创建真正的表格"""
        try:
            print(f"📋 在单元格中创建真正的{table_type}表格...")
            
            # 清空单元格内容
            cell.text = ""
            
            # 获取单元格的第一个段落
            paragraph = cell.paragraphs[0]
            
            # 添加表格标题
            title_run = paragraph.add_run(f"{table_type}：\n")
            title_run.font.bold = True
            title_run.font.color.rgb = RGBColor(0, 0, 0)  # 黑色标题
            
            # 在段落后添加表格
            if table_type == "前提条件":
                self.add_precondition_table_after_paragraph(paragraph)
            elif table_type == "持续条件":
                self.add_continuous_condition_table_after_paragraph(paragraph)
            elif table_type == "贷款条件":
                self.add_loan_condition_table_after_paragraph(paragraph)
            
            print(f"✅ {table_type}表格创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建{table_type}表格失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def add_precondition_table_after_paragraph(self, paragraph):
        """在段落后添加前提条件表格 - 根据用户截图精准匹配"""
        # 获取文档对象
        doc = paragraph._parent._parent

        # 创建表格 (2行4列)
        table = doc.add_table(rows=2, cols=4)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头 - 完全按照截图内容
        headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充数据行 - 根据截图第一张图的内容
        data_row = table.rows[1]
        data_content = [
            "流动资金贷款（及可串用该额度的其他业务品种）",
            "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
            "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
            "我行将在每次放款前审核落实"
        ]

        for i, content in enumerate(data_content):
            cell = data_row.cells[i]
            cell.text = content
            # 设置数据为绿色
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.color.rgb = self.green_rgb
    
    def add_continuous_condition_table_after_paragraph(self, paragraph):
        """在段落后添加持续条件表格 - 根据用户截图第三张图精准匹配"""
        # 获取文档对象
        doc = paragraph._parent._parent

        # 创建表格 (根据截图内容确定行数)
        table = doc.add_table(rows=13, cols=4)  # 1表头 + 12数据行
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT

        # 设置表头 - 完全按照截图第三张图
        headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
        header_row = table.rows[0]
        for i, header in enumerate(headers):
            cell = header_row.cells[i]
            cell.text = header
            # 设置表头格式
            for para in cell.paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)

        # 填充持续条件数据 - 根据截图第三张图的具体内容
        conditions_data = [
            ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
            ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
            ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
            ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
            ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "公司近期为引入A+轮融资对老股东进行了部分清退，系主动优化股权结构的战略安排。实际控制人杨伟依旧保持绝对控制权。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
            ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "暂不考核", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
            ["对长期投资的限制", "贷款存续期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "公司未对并表范围外公司投资。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
            ["对发行优先权债务的限制", "贷款存续期内，不得优先发行优先权债务（将视影响程度削减甚至冻结未使用额度)", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式未强于我行。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
            ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "客户已将基本户、代发薪账户转移至我行，并指定我行为A+轮融资唯一收款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
            ["其他条件1", "/", "放款前落实", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
            ["其他条件2", "/", "放款前落实", "A+轮融资到账前的临时性条款有效期最长不超过6个月（即至2026年1月31日）。"],
            ["其他条件3", "单户综合融资总量有效期内，客户合并口径有息负债总金额不得超过7000万元", "经核查，截至2025年6月末，公司A+股权融资未到账，客户合并口径有息负债总额为5900万元，低于本条款约定的7500万元上限。", "分阶段调整：A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
        ]

        for i, condition_data in enumerate(conditions_data):
            row = table.rows[i + 1]
            for j, cell_text in enumerate(condition_data):
                cell = row.cells[j]
                cell.text = cell_text
                # 设置数据为绿色
                for para in cell.paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb
    
    def add_loan_condition_table_after_paragraph(self, paragraph):
        """在段落后添加贷款条件表格"""
        # 获取文档对象
        doc = paragraph._parent._parent
        
        # 创建表格 (6行2列: 1表头 + 5数据行)
        table = doc.add_table(rows=6, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.LEFT
        
        # 设置表头
        table.rows[0].cells[0].text = "序号"
        table.rows[0].cells[1].text = "贷款条件"
        
        # 设置表头格式
        for i in range(2):
            for para in table.rows[0].cells[i].paragraphs:
                for run in para.runs:
                    run.font.bold = True
                    run.font.color.rgb = RGBColor(0, 0, 0)
        
        # 填充贷款条件数据
        conditions = [
            "担保措施落实计划：担保方式为信用，追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施。我行将在放款前落实实控人担保情况及部分专利质押情况。",
            "贸易背景条件：作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
            "存量压缩条件（如有）：/",
            "支付方式条件：/",
            "账户管理措施：在放款前对我行贷转存账户设置网银受控"
        ]
        
        for i, condition in enumerate(conditions):
            row = table.rows[i + 1]
            row.cells[0].text = str(i + 1)
            row.cells[1].text = condition
            # 设置数据为绿色
            for j in range(2):
                for para in row.cells[j].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb
    
    def generate_real_table_condition_checklist(self, disbursement_amount=1000):
        """生成真正表格版落实情况表"""
        print("🔧 开始生成真正表格版落实情况表...")
        print("📋 专家要求: 创建真正的Word表格，而不是文字描述")
        
        company_data = self.get_company_data()
        if not company_data:
            print("❌ 无法获取公司数据")
            return None
        
        try:
            # 源文件路径
            condition_template_path = self.template_dir / "落实情况表.docx"
            
            if not condition_template_path.exists():
                print(f"❌ 落实情况表模板不存在: {condition_template_path}")
                return None
            
            # 创建输出文件
            output_path = self.test_output_dir / f"落实情况表_真正表格版_{company_data['company_short_name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(condition_template_path, output_path)
            
            # 打开目标文档
            target_doc = docx.Document(output_path)
            
            # 基础信息替换
            print("\n📝 替换基础信息...")
            current_date = datetime.now()
            company_short = company_data.get('company_short_name', '卓尔')
            contract_number = f"建八{company_short}（2025）001号"
            
            replacements = {
                '【公司名称】': company_data.get('company_name', ''),
                '【当前年份】': str(current_date.year),
                '【当前月份】': str(current_date.month),
                '【支用金额万元】': str(disbursement_amount * 10000),
                '【支用金额】': str(disbursement_amount),
                '【合同编号】': contract_number,
                '【合同编号前缀】': f"建八{company_short}",
                '【合同编号后缀】': "（2025）001号",
                '【审批文号额度】': 'PIFU510000000N202407210',
                '【审批文号业务】': 'PIFU5100000002025N00G8',
                '【贷款类型】': '流动资金贷款',
                '【合同金额】': '2000',
                '【贷款期限】': '13个月',
                '【有效期开始】': '2024-03-06',
                '【有效期结束】': '2025-03-06'
            }
            
            # 执行基础信息替换
            replacement_count = 0
            
            # 替换段落中的基础信息
            for para in target_doc.paragraphs:
                for placeholder, value in replacements.items():
                    if placeholder in para.text:
                        self.replace_text_in_paragraph_with_green(para, placeholder, str(value))
                        replacement_count += 1
            
            # 替换表格中的基础信息并创建真正的表格
            for table in target_doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        # 基础信息替换
                        for para in cell.paragraphs:
                            for placeholder, value in replacements.items():
                                if placeholder in para.text:
                                    self.replace_text_in_paragraph_with_green(para, placeholder, str(value))
                                    replacement_count += 1
                        
                        # 创建真正的表格
                        cell_text = cell.text.strip()
                        
                        # 检查是否包含需要创建表格的内容
                        if "单户综合融资总量方案申报书中列明的用信前提条件及落实情况" in cell_text:
                            print("✅ 创建真正的前提条件表格")
                            self.create_real_table_in_cell(cell, "前提条件")
                        
                        elif "单户综合融资总量方案申报书中列明的持续条件及落实情况" in cell_text:
                            print("✅ 创建真正的持续条件表格")
                            self.create_real_table_in_cell(cell, "持续条件")
                        
                        elif "单笔业务申报书中列明的贷款条件及落实情况" in cell_text:
                            print("✅ 创建真正的贷款条件表格")
                            self.create_real_table_in_cell(cell, "贷款条件")
            
            print(f"✅ 基础信息替换完成，共替换 {replacement_count} 处")
            
            # 保存文档
            target_doc.save(output_path)
            
            print(f"\n✅ 真正表格版落实情况表生成完成: {output_path.name}")
            print(f"📝 真正表格版特点:")
            print(f"  ✅ 创建真正的Word表格结构")
            print(f"  ✅ 有清晰的表格边框和单元格")
            print(f"  ✅ 标准的行列布局")
            print(f"  ✅ 基础信息替换 (绿色标记)")
            print(f"  ✅ 表格数据替换 (绿色标记)")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_real_table_in_cell(self, cell, table_type):
        """在单元格中创建真正的表格 - 根据用户截图精准匹配"""
        # 清空单元格内容
        cell.text = ""

        # 获取文档对象
        doc = cell._parent._parent._parent

        if table_type == "前提条件":
            # 根据截图第一张图创建前提条件表格
            table = doc.add_table(rows=2, cols=4)
            table.style = 'Table Grid'

            # 设置表头
            headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
            header_row = table.rows[0]
            for i, header in enumerate(headers):
                header_row.cells[i].text = header
                for para in header_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充数据行
            data_row = table.rows[1]
            data_content = [
                "流动资金贷款（及可串用该额度的其他业务品种）",
                "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
                "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
                "我行将在每次放款前审核落实"
            ]

            for i, content in enumerate(data_content):
                data_row.cells[i].text = content
                for para in data_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb

        elif table_type == "持续条件":
            # 根据截图第三张图创建持续条件表格
            table = doc.add_table(rows=13, cols=4)  # 1表头 + 12数据行
            table.style = 'Table Grid'

            # 设置表头
            headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
            header_row = table.rows[0]
            for i, header in enumerate(headers):
                header_row.cells[i].text = header
                for para in header_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充持续条件数据
            conditions_data = [
                ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
                ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
                ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
                ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
                ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "公司近期为引入A+轮融资对老股东进行了部分清退，系主动优化股权结构的战略安排。实际控制人杨伟依旧保持绝对控制权。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
                ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "暂不考核", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
                ["对长期投资的限制", "贷款存续期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "公司未对并表范围外公司投资。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
                ["对发行优先权债务的限制", "贷款存续期内，不得优先发行优先权债务（将视影响程度削减甚至冻结未使用额度)", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式未强于我行。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
                ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "客户已将基本户、代发薪账户转移至我行，并指定我行为A+轮融资唯一收款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
                ["其他条件1", "/", "放款前落实", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
                ["其他条件2", "/", "放款前落实", "A+轮融资到账前的临时性条款有效期最长不超过6个月（即至2026年1月31日）。"],
                ["其他条件3", "单户综合融资总量有效期内，客户合并口径有息负债总金额不得超过7000万元", "经核查，截至2025年6月末，公司A+股权融资未到账，客户合并口径有息负债总额为5900万元，低于本条款约定的7500万元上限。", "分阶段调整：A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
            ]

            for i, condition_data in enumerate(conditions_data):
                row = table.rows[i + 1]
                for j, cell_text in enumerate(condition_data):
                    row.cells[j].text = cell_text
                    for para in row.cells[j].paragraphs:
                        for run in para.runs:
                            run.font.color.rgb = self.green_rgb

        elif table_type == "贷款条件":
            # 根据截图第二张图创建贷款条件表格
            table = doc.add_table(rows=7, cols=2)  # 1表头 + 6数据行
            table.style = 'Table Grid'

            # 设置表头
            table.rows[0].cells[0].text = "序号"
            table.rows[0].cells[1].text = "贷款条件"

            for i in range(2):
                for para in table.rows[0].cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充贷款条件数据 - 根据截图第二张图的编号1-6条件
            conditions = [
                "担保措施要求：本次申请的流动资金贷款须由公司实际控制人杨伟提供连带责任保证担保，并由公司以其持有的应收账款提供质押担保。",
                "贸易背景要求：本次申请的流动资金贷款须有真实的贸易背景，公司须提供相关合同、发票等证明材料。",
                "资金用途限制：本次申请的流动资金贷款仅可用于公司正常经营周转，不得用于固定资产投资、股权投资等。",
                "贷款期限：本次申请的流动资金贷款期限不超过12个月。",
                "还款来源：本次申请的流动资金贷款的还款来源为公司经营性现金流。",
                "其他条件：在贷款存续期内，公司须按照我行要求提供相关财务报表及经营情况报告。"
            ]

            for i, condition in enumerate(conditions):
                row = table.rows[i + 1]
                row.cells[0].text = str(i + 1)
                row.cells[1].text = condition
                for j in range(2):
                    for para in row.cells[j].paragraphs:
                        for run in para.runs:
                            run.font.color.rgb = self.green_rgb

    def replace_text_in_paragraph_with_green(self, paragraph, old_text, new_text):
        """在段落中替换文本，设置为绿色"""
        if old_text in paragraph.text:
            for run in paragraph.runs:
                if old_text in run.text:
                    # 替换文本
                    run.text = run.text.replace(old_text, new_text)
                    # 设置为绿色
                    run.font.color.rgb = self.green_rgb

    def create_standalone_conditions_document(self):
        """创建单独的条件汇总文档 - 包含持续条件、前提条件、担保措施落实计划"""
        print("🔧 开始创建单独的条件汇总文档...")
        print("📋 用户要求: 创建单独docx，包含三个真正的表格")

        company_data = self.get_company_data()
        if not company_data:
            print("❌ 无法获取公司数据")
            return None

        try:
            from docx import Document
            from docx.shared import Inches
            from docx.enum.table import WD_TABLE_ALIGNMENT

            # 创建新文档
            doc = Document()

            # 添加标题
            title = doc.add_heading(f'{company_data.get("company_short_name", "公司")}条件汇总表', 0)

            # 1. 添加持续条件表格
            print("✅ 创建持续条件表格...")
            doc.add_heading('一、持续条件表格', level=1)
            doc.add_paragraph('单户综合融资总量申报书中列明的持续条件及落实情况')

            # 创建持续条件表格
            continuous_table = doc.add_table(rows=13, cols=4)
            continuous_table.style = 'Table Grid'
            continuous_table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 设置持续条件表头
            headers = ['持续条件', '本次设置的用信持续条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
            header_row = continuous_table.rows[0]
            for i, header in enumerate(headers):
                header_row.cells[i].text = header
                for para in header_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充持续条件数据
            conditions_data = [
                ["持续的评级水平", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关文件管理要求的在我行科创评级T5及以上。", "当前公司有效评级为10级，科创评级为T5级，符合要求。", "在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。"],
                ["持续的资产负债率水平", "在单户综合融资总量有效期内,客户的资产负债率不得高于60%（合并口径）", "根据2025年3月报表，公司资产负债率为59.7%，符合要求。", "分阶段调整：A+轮融资到账前不高于75%，到账后不高于65%。"],
                ["持续的流动性水平", "在单户综合融资总量有效期内,客户的流动比率不得低于1.1", "根据2025年3月报表（单一口径），客户流动比率为1.11，符合要求。", "分阶段调整：A+轮融资到账前不低于1，到账后不低于1.1。"],
                ["持续的或有负债水平", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。", "根据公司最新征信记录及管理层确认，公司无对集团外的或有负债。", "在单户综合融资总量有效期内, 客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。"],
                ["持续的股权结构", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。", "公司近期为引入A+轮融资对老股东进行了部分清退，系主动优化股权结构的战略安排。实际控制人杨伟依旧保持绝对控制权。", "在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。"],
                ["对盈利能力的要求", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。", "暂不考核", "在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。"],
                ["对长期投资的限制", "贷款存续期内，客户对其并表范围外的公司进行对外投资需征得我行同意。", "公司未对并表范围外公司投资。", "在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。"],
                ["对发行优先权债务的限制", "贷款存续期内，不得优先发行优先权债务（将视影响程度削减甚至冻结未使用额度)", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式未强于我行。", "在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。"],
                ["对账户管理要求", "客户在我行开立存款账户，且将贷转存账户设置网银受控。", "客户已将基本户、代发薪账户转移至我行，并指定我行为A+轮融资唯一收款账户。", "客户在我行开立存款账户，且将贷转存账户设置网银受控。"],
                ["其他条件1", "/", "放款前落实", "本次新增贷款的最终支用日不晚于2025年9月30日。"],
                ["其他条件2", "/", "放款前落实", "A+轮融资到账前的临时性条款有效期最长不超过6个月（即至2026年1月31日）。"],
                ["其他条件3", "单户综合融资总量有效期内，客户合并口径有息负债总金额不得超过7000万元", "经核查，截至2025年6月末，公司A+股权融资未到账，客户合并口径有息负债总额为5900万元，低于本条款约定的7500万元上限。", "分阶段调整：A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"]
            ]

            for i, condition_data in enumerate(conditions_data):
                row = continuous_table.rows[i + 1]
                for j, cell_text in enumerate(condition_data):
                    row.cells[j].text = cell_text
                    for para in row.cells[j].paragraphs:
                        for run in para.runs:
                            run.font.color.rgb = self.green_rgb

            # 2. 添加前提条件表格
            print("✅ 创建前提条件表格...")
            doc.add_page_break()
            doc.add_heading('二、前提条件表格', level=1)
            doc.add_paragraph('单户综合融资总量申报书中列明的用信前提条件及落实情况')

            # 创建前提条件表格
            precondition_table = doc.add_table(rows=2, cols=4)
            precondition_table.style = 'Table Grid'
            precondition_table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 设置前提条件表头
            headers = ['产品', '本次设置的用信前提条件', '前次单户综合融资总量方案设定条件', '本次申报时点实际情况']
            header_row = precondition_table.rows[0]
            for i, header in enumerate(headers):
                header_row.cells[i].text = header
                for para in header_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充前提条件数据
            data_row = precondition_table.rows[1]
            data_content = [
                "流动资金贷款（及可串用该额度的其他业务品种）",
                "在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。",
                "在我行流动资金贷款支用时，公司提供的合同或订单金额不低于贷款发放金额的1.3倍，且合同或订单收款账号为我行收款账号或已更改为我行收款账号。",
                "我行将在每次放款前审核落实"
            ]

            for i, content in enumerate(data_content):
                data_row.cells[i].text = content
                for para in data_row.cells[i].paragraphs:
                    for run in para.runs:
                        run.font.color.rgb = self.green_rgb

            # 3. 添加担保措施落实计划表格
            print("✅ 创建担保措施落实计划表格...")
            doc.add_page_break()
            doc.add_heading('三、担保措施落实计划表格', level=1)
            doc.add_paragraph('业务申报书中列明的担保措施落实计划')

            # 创建担保措施表格
            guarantee_table = doc.add_table(rows=6, cols=2)
            guarantee_table.style = 'Table Grid'
            guarantee_table.alignment = WD_TABLE_ALIGNMENT.LEFT

            # 设置表头
            guarantee_table.rows[0].cells[0].text = "项目"
            guarantee_table.rows[0].cells[1].text = "具体内容"

            for i in range(2):
                for para in guarantee_table.rows[0].cells[i].paragraphs:
                    for run in para.runs:
                        run.font.bold = True

            # 填充担保措施数据 - 根据用户提供的具体内容
            guarantee_conditions = [
                ["担保措施落实计划", "担保方式为信用，追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施。我行将在放款前落实实控人担保情况及部分专利质押情况。"],
                ["贸易背景条件", "作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。"],
                ["存量压缩条件（如有）", "/"],
                ["支付方式条件", "/"],
                ["账户管理措施", "在放款前对我行贷转存账户设置网银受控"]
            ]

            for i, condition in enumerate(guarantee_conditions):
                row = guarantee_table.rows[i + 1]
                row.cells[0].text = condition[0]
                row.cells[1].text = condition[1]
                for j in range(2):
                    for para in row.cells[j].paragraphs:
                        for run in para.runs:
                            run.font.color.rgb = self.green_rgb

            # 保存文档
            output_path = self.test_output_dir / f"条件汇总表_{company_data.get('company_short_name', '公司')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            doc.save(output_path)

            print(f"\n✅ 条件汇总文档生成完成: {output_path.name}")
            print(f"📝 包含内容:")
            print(f"  ✅ 持续条件表格 (4列 x 13行)")
            print(f"  ✅ 前提条件表格 (4列 x 2行)")
            print(f"  ✅ 担保措施落实计划表格 (2列 x 6行)")
            print(f"  ✅ 所有表格都有真正的边框和单元格")
            print(f"  ✅ 内容标记为绿色")

            return output_path

        except Exception as e:
            print(f"❌ 生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    creator = RealTableCreator()

    print("=" * 80)
    print("📋 条件汇总表格创建器")
    print("=" * 80)
    print("🎯 用户需求:")
    print("  ✅ 创建单独的docx文件")
    print("  ✅ 包含三个真正的表格:")
    print("    1. 持续条件表格")
    print("    2. 前提条件表格")
    print("    3. 担保措施落实计划表格")
    print("  ✅ 从业务申报书搜索关键字'担保措施落实计划：'")
    print("=" * 80)

    result = creator.create_standalone_conditions_document()

    if result:
        print(f"\n🎉 条件汇总表格创建完成！")
        print(f"📄 文件位置: {result}")
        print(f"📝 请检查:")
        print(f"  ✅ 三个表格是否都有真正的边框")
        print(f"  ✅ 表格内容是否正确")
        print(f"  ✅ 绿色标记是否正确")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
