"""
公司服务层
处理公司相关的业务逻辑和数据库操作
"""

import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

try:
    from ..sqlite_database import db_manager
    USE_REAL_DB = True if db_manager else False
except ImportError:
    # 如果数据库不可用，使用模拟数据
    USE_REAL_DB = False
    db_manager = None
    mock_db = {
        'companies': [],
        'personnel': [],
        'tags': []
    }

from ..models import CompanyCreateRequest, CompanyUpdateRequest, OperationType

logger = logging.getLogger(__name__)

class CompanyService:
    """公司服务类"""

    def __init__(self):
        if USE_REAL_DB:
            self.db = db_manager
        else:
            self.db = mock_db
            logger.warning("使用模拟数据库 - 仅用于测试目的")
    
    def get_companies_list(self) -> List[Dict[str, Any]]:
        """获取所有公司的简要列表（用于下拉框）"""
        if USE_REAL_DB:
            try:
                result = db_manager.get_companies_list()
                logger.info(f"获取公司列表成功，共 {len(result)} 条记录")
                return result
            except Exception as e:
                logger.error(f"获取公司列表失败: {e}")
                raise
        else:
            # 使用模拟数据
            result = mock_db.get('companies', [])
            logger.info(f"获取公司列表成功（模拟数据），共 {len(result)} 条记录")
            return result
    
    def get_company_detail(self, company_id: str) -> Optional[Dict[str, Any]]:
        """获取公司的完整详细信息（动态结构）"""

        if USE_REAL_DB:
            # 使用SQLite数据库查询
            try:
                result = db_manager.get_company_detail(company_id)
                if result:
                    logger.info(f"获取公司详细信息成功: {company_id}")
                else:
                    logger.warning(f"未找到公司信息: {company_id}")
                return result
            except Exception as e:
                logger.error(f"获取公司详细信息失败: {e}")
                raise


        else:
            # 使用模拟数据
            companies = mock_db.get('companies', [])
            for company in companies:
                if company.get('id') == company_id:
                    logger.info(f"获取公司详细信息成功（模拟数据）: {company_id}")
                    return company
            logger.warning(f"未找到公司信息（模拟数据）: {company_id}")
            return None
    
    def create_company(self, request: CompanyCreateRequest) -> str:
        """创建新公司"""
        if not USE_REAL_DB:
            raise NotImplementedError("模拟数据库模式下不支持创建公司")

        company_id = str(uuid.uuid4())
        current_time = datetime.now()

        # 强化唯一性校验：检查统一社会信用代码是否已存在
        check_credit_code_query = """
        SELECT id, company_name FROM companies WHERE unified_social_credit_code = %s
        """
        existing_credit = self.db.execute_single(check_credit_code_query, (request.unified_social_credit_code,))
        if existing_credit:
            raise ValueError(f"统一社会信用代码 {request.unified_social_credit_code} 已被企业 '{existing_credit['company_name']}' 使用")

        # 强化唯一性校验：检查公司名称是否已存在
        check_name_query = """
        SELECT id, unified_social_credit_code FROM companies WHERE company_name = %s
        """
        existing_name = self.db.execute_single(check_name_query, (request.company_name,))
        if existing_name:
            raise ValueError(f"企业名称 '{request.company_name}' 已存在，信用代码为 {existing_name['unified_social_credit_code']}")

        # 准备事务操作
        operations = [
            # 插入公司主表
            (
                """
                INSERT INTO companies (id, unified_social_credit_code, company_name)
                VALUES (%s, %s, %s)
                """,
                (company_id, request.unified_social_credit_code, request.company_name)
            ),
            # 插入历史记录
            (
                """
                INSERT INTO companies_history
                (history_id, company_id, version_number, unified_social_credit_code,
                 company_name, operation_type, operated_by_user_id, operation_reason, effective_from)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (
                    str(uuid.uuid4()), company_id, 1, request.unified_social_credit_code,
                    request.company_name, OperationType.INSERT.value, request.operated_by_user_id,
                    request.operation_reason, current_time
                )
            )
        ]

        try:
            self.db.execute_transaction(operations)
            logger.info(f"创建公司成功: {company_id}")
            return company_id
        except Exception as e:
            logger.error(f"创建公司失败: {e}")
            raise
    
    def update_company(self, company_id: str, request: CompanyUpdateRequest) -> bool:
        """更新公司信息"""
        if not USE_REAL_DB:
            raise NotImplementedError("模拟数据库模式下不支持更新公司")

        # 原有的更新逻辑...
        logger.warning("更新公司功能需要真实数据库支持")
        return False

    def get_company_history(self, company_id: str) -> List[Dict[str, Any]]:
        """获取公司的变更历史"""
        if not USE_REAL_DB:
            # 返回模拟历史数据
            return [{
                'history_id': str(uuid.uuid4()),
                'version_number': 1,
                'unified_social_credit_code': '91510100MA6CL77FXX',
                'company_name': '成都中科卓尔智能科技集团有限公司',
                'operation_type': 'INSERT',
                'operated_by_user_id': str(uuid.uuid4()),
                'operation_reason': '初始数据录入',
                'effective_from': datetime.now().isoformat()
            }]

        # 原有的历史查询逻辑...
        logger.warning("历史记录功能需要真实数据库支持")
        return []
