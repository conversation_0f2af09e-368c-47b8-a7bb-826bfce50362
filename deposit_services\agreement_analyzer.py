#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协定存款协议分析器
分析协议模板中可以自动填充的字段
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from pathlib import Path
import json
import re
from datetime import datetime

class AgreementAnalyzer:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "templates" / "deposit_services"  # 统一模板目录下的存款服务子目录
        self.output_dir = self.project_root / "test_output"
        
    def analyze_agreement_template(self, template_filename):
        """分析协议模板，识别可填充字段"""
        template_path = self.templates_dir / template_filename
        
        print(f"🔍 分析协定存款协议模板")
        print("="*60)
        
        if not template_path.exists():
            print(f"❌ 模板文件不存在: {template_path}")
            return None
        
        try:
            doc = docx.Document(template_path)
            
            # 分析结果
            analysis_result = {
                'template_name': template_filename,
                'analysis_time': datetime.now().isoformat(),
                'fillable_fields': [],
                'company_info_fields': [],
                'financial_fields': [],
                'date_fields': [],
                'contract_fields': [],
                'other_fields': []
            }
            
            print(f"📄 模板文件: {template_filename}")
            
            # 1. 查找标记字段（黄色高亮、括号占位符等）
            marked_fields = self._find_marked_fields(doc)
            
            # 2. 查找模式化字段（如：甲方、乙方、金额等）
            pattern_fields = self._find_pattern_fields(doc)
            
            # 3. 合并和分类字段
            all_fields = marked_fields + pattern_fields
            categorized_fields = self._categorize_fields(all_fields)
            
            analysis_result.update(categorized_fields)
            
            # 4. 显示分析结果
            self._display_analysis_results(analysis_result)
            
            # 5. 保存分析结果
            output_file = self.output_dir / f"agreement_analysis_{template_filename.replace('.docx', '.json')}"
            self.output_dir.mkdir(exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 分析结果已保存: {output_file}")
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def _find_marked_fields(self, doc):
        """查找标记字段（黄色高亮等）"""
        marked_fields = []
        field_id = 1
        
        print(f"\n🟡 查找标记字段:")
        print("-" * 40)
        
        # 检查段落
        for para_idx, paragraph in enumerate(doc.paragraphs):
            for run_idx, run in enumerate(paragraph.runs):
                if run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                    field = {
                        'id': field_id,
                        'type': 'highlighted',
                        'location': 'paragraph',
                        'position': f"段落{para_idx}",
                        'text': run.text.strip(),
                        'context': paragraph.text.strip()[:100] + "..." if len(paragraph.text) > 100 else paragraph.text.strip()
                    }
                    marked_fields.append(field)
                    print(f"   {field_id:2d}. 高亮字段: '{run.text.strip()}'")
                    field_id += 1
        
        # 检查表格
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        for run_idx, run in enumerate(paragraph.runs):
                            if run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                                field = {
                                    'id': field_id,
                                    'type': 'highlighted',
                                    'location': 'table',
                                    'position': f"表格{table_idx}_行{row_idx}_列{cell_idx}",
                                    'text': run.text.strip(),
                                    'context': paragraph.text.strip()[:100] + "..." if len(paragraph.text) > 100 else paragraph.text.strip()
                                }
                                marked_fields.append(field)
                                print(f"   {field_id:2d}. 高亮字段: '{run.text.strip()}'")
                                field_id += 1
        
        print(f"   📊 找到 {len(marked_fields)} 个标记字段")
        return marked_fields
    
    def _find_pattern_fields(self, doc):
        """查找模式化字段"""
        pattern_fields = []
        field_id = 1000  # 从1000开始，避免与标记字段冲突
        
        print(f"\n🔍 查找模式化字段:")
        print("-" * 40)
        
        # 提取全文
        full_text = self._extract_full_text(doc)
        
        # 定义常见模式
        patterns = {
            '企业名称': [
                r'甲方[：:]?\s*([^\n\r，。；]{2,50})',
                r'乙方[：:]?\s*([^\n\r，。；]{2,50})',
                r'存款人[：:]?\s*([^\n\r，。；]{2,50})',
                r'开户单位[：:]?\s*([^\n\r，。；]{2,50})'
            ],
            '金额': [
                r'存款金额[：:]?\s*([^\n\r，。；]{2,30})',
                r'协定金额[：:]?\s*([^\n\r，。；]{2,30})',
                r'基数[：:]?\s*([^\n\r，。；]{2,30})',
                r'人民币[：:]?\s*([^\n\r，。；]{2,30})'
            ],
            '利率': [
                r'利率[：:]?\s*([^\n\r，。；]{2,20})',
                r'年利率[：:]?\s*([^\n\r，。；]{2,20})',
                r'协定利率[：:]?\s*([^\n\r，。；]{2,20})'
            ],
            '日期': [
                r'(\d{4}年\d{1,2}月\d{1,2}日)',
                r'(\d{4}-\d{1,2}-\d{1,2})',
                r'签署日期[：:]?\s*([^\n\r，。；]{2,20})',
                r'生效日期[：:]?\s*([^\n\r，。；]{2,20})'
            ],
            '账户': [
                r'账户[：:]?\s*([^\n\r，。；]{2,30})',
                r'账号[：:]?\s*([^\n\r，。；]{2,30})',
                r'开户行[：:]?\s*([^\n\r，。；]{2,50})'
            ]
        }
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, full_text, re.IGNORECASE)
                for match in matches:
                    field = {
                        'id': field_id,
                        'type': 'pattern',
                        'category': category,
                        'pattern': pattern,
                        'text': match,
                        'context': f"通过模式匹配找到的{category}字段"
                    }
                    pattern_fields.append(field)
                    print(f"   {field_id:4d}. [{category}] '{match}'")
                    field_id += 1
        
        print(f"   📊 找到 {len(pattern_fields)} 个模式字段")
        return pattern_fields
    
    def _categorize_fields(self, all_fields):
        """对字段进行分类"""
        categorized = {
            'fillable_fields': all_fields,
            'company_info_fields': [],
            'financial_fields': [],
            'date_fields': [],
            'contract_fields': [],
            'other_fields': []
        }
        
        for field in all_fields:
            text = field.get('text', '').lower()
            category = field.get('category', '')
            
            if any(keyword in text for keyword in ['公司', '企业', '甲方', '乙方', '存款人', '开户']):
                categorized['company_info_fields'].append(field)
            elif any(keyword in text for keyword in ['金额', '利率', '利息', '万元', '元', '%']):
                categorized['financial_fields'].append(field)
            elif any(keyword in text for keyword in ['年', '月', '日', '期', '时间']):
                categorized['date_fields'].append(field)
            elif any(keyword in text for keyword in ['合同', '协议', '条款', '签署']):
                categorized['contract_fields'].append(field)
            else:
                categorized['other_fields'].append(field)
        
        return categorized
    
    def _extract_full_text(self, doc):
        """提取文档全文"""
        full_text = ""
        
        for para in doc.paragraphs:
            full_text += para.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    full_text += cell.text + " "
                full_text += "\n"
        
        return full_text
    
    def _display_analysis_results(self, result):
        """显示分析结果"""
        print(f"\n📊 分析结果统计:")
        print("-" * 40)
        print(f"   📝 总可填充字段: {len(result['fillable_fields'])}")
        print(f"   🏢 企业信息字段: {len(result['company_info_fields'])}")
        print(f"   💰 金融字段: {len(result['financial_fields'])}")
        print(f"   📅 日期字段: {len(result['date_fields'])}")
        print(f"   📋 合同字段: {len(result['contract_fields'])}")
        print(f"   🔍 其他字段: {len(result['other_fields'])}")
        
        # 显示企业信息字段详情
        if result['company_info_fields']:
            print(f"\n🏢 企业信息字段详情:")
            for field in result['company_info_fields'][:5]:  # 只显示前5个
                print(f"   - {field['text']}")
        
        # 显示金融字段详情
        if result['financial_fields']:
            print(f"\n💰 金融字段详情:")
            for field in result['financial_fields'][:5]:  # 只显示前5个
                print(f"   - {field['text']}")

def main():
    """测试函数"""
    analyzer = AgreementAnalyzer()

    print("🏦 协定存款协议分析器")
    print("="*50)

    # 分析实际的协议文件
    template_filename = "中国建设银行人民币单位协定存款合同（标准文本）1.docx"
    result = analyzer.analyze_agreement_template(template_filename)

    if result:
        print(f"\n🎉 分析完成！")
        print(f"📊 共识别 {len(result['fillable_fields'])} 个可填充字段")
    else:
        print(f"\n❌ 分析失败！")

if __name__ == "__main__":
    main()
