#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版文档分析器
更准确地提取 2.docx 中的所有标黄内容并生成完整的字段替换清单
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from pathlib import Path
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedDocumentAnalyzer:
    """增强版文档分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.modified_path = self.project_root / "templates" / "contract_disbursement" / "2.docx"
        self.quota_doc_path = self.project_root / "templates" / "contract_disbursement" / "额度申报书.docx"
        self.business_doc_path = self.project_root / "templates" / "contract_disbursement" / "业务申报书.docx"
        
        # 存储分析结果
        self.highlighted_fields = []
        
    def analyze_highlighted_content(self):
        """分析标黄内容"""
        logger.info("🔍 增强版文档分析开始...")
        logger.info("="*70)
        
        # 1. 验证文件存在
        if not self._verify_files():
            return None
        
        # 2. 提取所有标黄内容
        modified_doc = docx.Document(self.modified_path)
        self._extract_all_highlighted_content(modified_doc)
        
        # 3. 分析每个标黄内容的来源
        quota_doc = docx.Document(self.quota_doc_path)
        business_doc = docx.Document(self.business_doc_path)
        
        for field in self.highlighted_fields:
            self._analyze_field_source(field, quota_doc, business_doc)
        
        # 4. 生成完整的替换清单
        self._generate_complete_replacement_list()
        
        return self.highlighted_fields
    
    def _verify_files(self):
        """验证文件存在"""
        files_to_check = [
            ("修改文件", self.modified_path),
            ("额度申报书", self.quota_doc_path),
            ("业务申报书", self.business_doc_path)
        ]
        
        all_exist = True
        for name, path in files_to_check:
            if path.exists():
                logger.info(f"   ✅ {name}: {path.name}")
            else:
                logger.error(f"   ❌ {name}不存在: {path}")
                all_exist = False
        
        return all_exist
    
    def _extract_all_highlighted_content(self, doc):
        """提取所有标黄内容"""
        logger.info("🟡 提取所有标黄内容...")
        
        field_counter = 1
        
        # 检查表格中的标黄内容
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    highlighted_text = self._extract_highlighted_from_cell(cell)
                    if highlighted_text:
                        field_info = {
                            'field_id': f'field_{field_counter}',
                            'field_name': self._generate_field_name(highlighted_text),
                            'location_type': 'table_cell',
                            'location_detail': f'表格{table_idx}-行{row_idx}-列{cell_idx}',
                            'content': highlighted_text,
                            'context': cell.text.strip(),
                            'position_info': {
                                'table_idx': table_idx,
                                'row_idx': row_idx,
                                'cell_idx': cell_idx
                            }
                        }
                        self.highlighted_fields.append(field_info)
                        logger.info(f"   🟡 字段{field_counter}: {highlighted_text[:50]}...")
                        field_counter += 1
        
        # 检查段落中的标黄内容
        for para_idx, paragraph in enumerate(doc.paragraphs):
            highlighted_text = self._extract_highlighted_from_paragraph(paragraph)
            if highlighted_text:
                field_info = {
                    'field_id': f'field_{field_counter}',
                    'field_name': self._generate_field_name(highlighted_text),
                    'location_type': 'paragraph',
                    'location_detail': f'段落{para_idx}',
                    'content': highlighted_text,
                    'context': paragraph.text.strip(),
                    'position_info': {
                        'para_idx': para_idx
                    }
                }
                self.highlighted_fields.append(field_info)
                logger.info(f"   🟡 字段{field_counter}: {highlighted_text[:50]}...")
                field_counter += 1
        
        logger.info(f"   📊 总计提取 {len(self.highlighted_fields)} 个标黄字段")
    
    def _extract_highlighted_from_cell(self, cell):
        """从单元格提取标黄文本"""
        highlighted_parts = []
        
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                if self._is_highlighted(run) and run.text.strip():
                    highlighted_parts.append(run.text.strip())
        
        return ' '.join(highlighted_parts) if highlighted_parts else None
    
    def _extract_highlighted_from_paragraph(self, paragraph):
        """从段落提取标黄文本"""
        highlighted_parts = []
        
        for run in paragraph.runs:
            if self._is_highlighted(run) and run.text.strip():
                highlighted_parts.append(run.text.strip())
        
        return ' '.join(highlighted_parts) if highlighted_parts else None
    
    def _is_highlighted(self, run):
        """判断文本是否被标黄"""
        try:
            if hasattr(run.font, 'highlight_color'):
                return run.font.highlight_color == WD_COLOR_INDEX.YELLOW
            return False
        except:
            return False
    
    def _generate_field_name(self, content):
        """生成字段名称"""
        if not content:
            return '未知字段'
        
        # 基于内容特征生成字段名
        content_lower = content.lower()
        
        if '中科卓尔' in content or '科技集团' in content:
            return '公司名称'
        elif content.strip() == '杨伟':
            return '法定代表人'
        elif re.match(r'91\d{15}[0-9X]', content):
            return '统一社会信用代码'
        elif '万元' in content and re.search(r'\d+', content):
            return '金额字段'
        elif re.match(r'20\d{2}', content) and ('年' in content or '-' in content):
            return '日期字段'
        elif '用信' in content or '前提' in content:
            return '用信前提条件'
        elif '担保' in content or '保证' in content:
            return '担保措施'
        elif '还款' in content or '来源' in content:
            return '还款来源'
        elif '贷款用途' in content or '资金用途' in content:
            return '贷款用途'
        elif '持续条件' in content:
            return '持续条件'
        elif '有效期' in content:
            return '有效期信息'
        elif '支用' in content:
            return '支用金额'
        elif '每次生成' in content or '当月' in content:
            return '生成说明'
        elif '额度' in content:
            return '额度信息'
        elif '业务' in content:
            return '业务信息'
        else:
            return f'字段_{len(self.highlighted_fields) + 1}'
    
    def _analyze_field_source(self, field, quota_doc, business_doc):
        """分析字段来源"""
        content = field['content']
        
        logger.info(f"🔍 分析字段来源: {field['field_name']}")
        
        # 检查是否为公司基础信息
        if self._is_company_basic_info(content):
            field['source_document'] = '数据库'
            field['source_location'] = self._get_database_field_name(content)
            field['source_type'] = '数据库字段'
            field['extraction_method'] = '数据库查询'
            logger.info(f"   ✅ 识别为数据库字段: {field['source_location']}")
            return
        
        # 检查是否为系统生成内容
        if self._is_system_generated(content):
            field['source_document'] = '系统生成'
            field['source_location'] = '自动生成'
            field['source_type'] = '系统字段'
            field['extraction_method'] = '程序生成'
            logger.info(f"   ✅ 识别为系统生成字段")
            return
        
        # 在额度申报书中查找
        quota_result = self._search_content_in_doc(content, quota_doc, "额度申报书.docx")
        if quota_result:
            field.update(quota_result)
            logger.info(f"   ✅ 在额度申报书中找到来源: {quota_result['source_location']}")
            return
        
        # 在业务申报书中查找
        business_result = self._search_content_in_doc(content, business_doc, "业务申报书.docx")
        if business_result:
            field.update(business_result)
            logger.info(f"   ✅ 在业务申报书中找到来源: {business_result['source_location']}")
            return
        
        # 未找到来源
        field['source_document'] = '未知'
        field['source_location'] = '需要手动确认'
        field['source_type'] = '未知'
        field['extraction_method'] = '需要确认'
        logger.warning(f"   ⚠️ 未找到字段来源")
    
    def _is_company_basic_info(self, content):
        """判断是否为公司基础信息"""
        patterns = [
            r'中科卓尔.*科技.*集团',
            r'^杨伟$',
            r'91\d{15}[0-9X]',
            r'^\d+(\.\d+)?万元$'
        ]
        
        for pattern in patterns:
            if re.search(pattern, content):
                return True
        return False
    
    def _is_system_generated(self, content):
        """判断是否为系统生成内容"""
        system_patterns = [
            r'每次生成',
            r'当月.*日空着',
            r'和.*保持一致',
            r'照着写'
        ]
        
        for pattern in system_patterns:
            if re.search(pattern, content):
                return True
        return False
    
    def _get_database_field_name(self, content):
        """获取数据库字段名"""
        if '中科卓尔' in content or '科技集团' in content:
            return 'companies.company_name'
        elif content.strip() == '杨伟':
            return 'companies.legal_representative'
        elif re.match(r'91\d{15}[0-9X]', content):
            return 'companies.unified_social_credit_code'
        elif '万元' in content:
            return 'companies.registered_capital'
        else:
            return 'companies.unknown_field'
    
    def _search_content_in_doc(self, target_content, doc, doc_name):
        """在文档中搜索内容"""
        # 清理目标内容用于匹配
        target_clean = self._clean_text_for_search(target_content)
        
        if len(target_clean) < 5:  # 内容太短，跳过
            return None
        
        # 在段落中搜索
        for para_idx, paragraph in enumerate(doc.paragraphs):
            para_clean = self._clean_text_for_search(paragraph.text)
            if self._content_match(target_clean, para_clean):
                title = self._find_section_title(doc.paragraphs, para_idx)
                return {
                    'source_document': doc_name,
                    'source_location': title or f'段落{para_idx}',
                    'source_type': '段落',
                    'extraction_method': '文本匹配'
                }
        
        # 在表格中搜索
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    cell_clean = self._clean_text_for_search(cell.text)
                    if self._content_match(target_clean, cell_clean):
                        table_title = self._find_table_title(doc, table_idx)
                        return {
                            'source_document': doc_name,
                            'source_location': table_title or f'表格{table_idx}',
                            'source_type': '表格',
                            'extraction_method': '表格匹配'
                        }
        
        return None
    
    def _clean_text_for_search(self, text):
        """清理文本用于搜索"""
        if not text:
            return ""
        # 移除空白字符和标点符号
        cleaned = re.sub(r'[\s\n\r\t\u3000]+', '', text)
        cleaned = re.sub(r'[，。、；：！？""''（）【】《》〈〉]', '', cleaned)
        return cleaned.lower()
    
    def _content_match(self, target, source):
        """内容匹配"""
        if not target or not source:
            return False
        
        # 如果目标内容在源内容中，或者相似度高
        if target in source or source in target:
            return True
        
        # 计算简单的相似度
        if len(target) > 10 and len(source) > 10:
            common_chars = sum(1 for c in target if c in source)
            similarity = common_chars / max(len(target), len(source))
            return similarity > 0.6
        
        return False
    
    def _find_section_title(self, paragraphs, current_idx):
        """查找章节标题"""
        for i in range(current_idx - 1, max(0, current_idx - 10), -1):
            para_text = paragraphs[i].text.strip()
            if self._is_section_title(para_text):
                return para_text
        return None
    
    def _find_table_title(self, doc, table_idx):
        """查找表格标题"""
        # 简化实现：查找表格前的段落
        for para in doc.paragraphs:
            if '表格' in para.text or '表' in para.text:
                return para.text.strip()
        return f'表格{table_idx}'
    
    def _is_section_title(self, text):
        """判断是否为章节标题"""
        if not text or len(text) > 100:
            return False
        
        title_patterns = [
            r'^[一二三四五六七八九十]+[、.]',
            r'^[1-9]\d*[、.]',
            r'^第[一二三四五六七八九十]+[章节部分条]',
            r'.*[条件|措施|分析|审查|说明|情况]$'
        ]
        
        for pattern in title_patterns:
            if re.match(pattern, text):
                return True
        
        return False

    def _generate_complete_replacement_list(self):
        """生成完整的字段替换清单"""
        logger.info("📋 生成完整的字段替换清单...")

        print("\n" + "="*100)
        print("📝 结构化字段替换清单")
        print("="*100)

        # 表格标题
        header = f"{'字段名称':<15} {'模板锚点或插入位置':<20} {'来源文档':<12} {'来源位置说明':<25} {'类型':<8} {'格式要求':<15} {'示例内容预览':<30}"
        print(header)
        print("-" * 125)

        # 按位置排序字段
        sorted_fields = sorted(self.highlighted_fields, key=lambda x: (
            x['position_info'].get('table_idx', 999),
            x['position_info'].get('row_idx', 999),
            x['position_info'].get('cell_idx', 999),
            x['position_info'].get('para_idx', 999)
        ))

        for field in sorted_fields:
            field_name = field['field_name'][:14]
            anchor_point = field['location_detail'][:19]
            source_doc = field.get('source_document', '未知')[:11]
            source_location = field.get('source_location', '未知位置')[:24]
            content_type = field.get('source_type', '文本')[:7]
            format_req = self._get_format_requirements(field)[:14]
            preview = field['content'][:29] + '...' if len(field['content']) > 29 else field['content']

            row = f"{field_name:<15} {anchor_point:<20} {source_doc:<12} {source_location:<25} {content_type:<8} {format_req:<15} {preview:<30}"
            print(row)

        print("-" * 125)
        print(f"总计: {len(sorted_fields)} 个字段")

        # 详细分段说明
        print("\n" + "="*100)
        print("📊 详细分段说明")
        print("="*100)

        for idx, field in enumerate(sorted_fields, 1):
            print(f"\n{idx}. 【{field['field_name']}】")
            print(f"   替换位置: {field['location_detail']}")
            print(f"   替换内容: {field['content']}")
            print(f"   内容来源: {field.get('source_document', '未知')} - {field.get('source_location', '未知位置')}")
            print(f"   内容类型: {field.get('source_type', '未知')}")
            print(f"   替换方式: {self._get_replacement_method(field)}")

            # 如果有上下文，显示上下文
            if field.get('context') and field['context'] != field['content']:
                print(f"   上下文: {field['context'][:100]}...")

        # 按来源文档分组统计
        print(f"\n" + "="*100)
        print("📈 按来源文档分组统计")
        print("="*100)

        source_stats = {}
        for field in sorted_fields:
            source = field.get('source_document', '未知')
            if source not in source_stats:
                source_stats[source] = []
            source_stats[source].append(field['field_name'])

        for source, fields in source_stats.items():
            print(f"\n📁 {source}: {len(fields)} 个字段")
            for field_name in fields:
                print(f"   - {field_name}")

    def _get_format_requirements(self, field):
        """获取格式要求"""
        source_type = field.get('source_type', '')
        source_doc = field.get('source_document', '')

        if source_type == '表格':
            return '保留表格格式'
        elif source_doc == '数据库':
            return '宋体12pt'
        elif source_doc == '系统生成':
            return '宋体12pt+注释'
        else:
            return '宋体12pt+黄标'

    def _get_replacement_method(self, field):
        """获取替换方式建议"""
        source_type = field.get('source_type', '')
        source_doc = field.get('source_document', '')

        if source_type == '表格':
            return '表格整体插入，保留边框和格式'
        elif source_type == '段落':
            return '段落内容替换，应用黄色高亮'
        elif source_doc == '数据库':
            return '直接文本替换，从数据库字段获取'
        elif source_doc == '系统生成':
            return '程序自动生成，如当前日期等'
        else:
            return '内容替换，保持原有格式并添加黄色高亮'


def main():
    """主函数"""
    print("🔍 增强版文档分析器")
    print("="*70)
    print("专门分析 2.docx 中的标黄内容并生成完整的字段替换清单")

    analyzer = EnhancedDocumentAnalyzer()

    try:
        results = analyzer.analyze_highlighted_content()

        if results:
            print(f"\n✅ 分析完成!")
            print(f"📊 总计分析了 {len(results)} 个标黄字段")

            # 统计各类来源
            source_count = {}
            for field in results:
                source = field.get('source_document', '未知')
                source_count[source] = source_count.get(source, 0) + 1

            print(f"\n📈 来源统计:")
            for source, count in source_count.items():
                print(f"   {source}: {count} 个字段")

            print(f"\n💡 此清单可用于自动替换和批量插入的开发工作")
            print(f"🎯 所有标黄内容已完整提取并分析来源")

        else:
            print("❌ 分析失败，请检查文件路径和内容")

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
