<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业服务驾驶舱 - 统一业务管理平台</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/cockpit.css">
</head>
<body>
    <div class="cockpit-container">
        <!-- 页面标题 -->
        <header class="cockpit-header">
            <h1>🏦 企业服务驾驶舱</h1>
            <p class="subtitle">请按照以下步骤开始您的工作流程</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="cockpit-main">
            <!-- 客户选择界面（初始状态） -->
            <div id="customer-selection-stage" class="selection-stage">
                <div class="selection-content">
                    <div class="selection-icon">🎯</div>
                    <h2>选择您要服务的客户</h2>
                    <p>请从下方列表中选择一个客户，然后选择要办理的业务</p>

                    <div class="customer-selector-enhanced">
                        <select id="customer-select" class="form-select-enhanced">
                            <option value="">请选择客户...</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 业务选择舞台（客户选定后显示） -->
            <div id="business-selection-stage" class="business-stage" style="display: none;">
                <div class="stage-header">
                    <h2>📋 选择业务模块</h2>
                    <div class="current-customer-info">
                        <span>当前客户：</span>
                        <span id="current-customer-display" class="customer-name-display">-</span>
                        <button id="change-customer-btn" class="change-btn">更换客户</button>
                    </div>
                </div>

                <div class="business-modules-grid">
                    <!-- 银企直联模块 -->
                    <div class="business-module-card" data-module="bank-enterprise">
                        <div class="module-header">
                            <div class="module-icon">⚡</div>
                            <h3>银企直联</h3>
                        </div>
                        <div class="module-description">
                            <p>为企业客户开通银行系统与企业财务系统的直连接服务</p>
                            <ul class="module-features">
                                <li>生成服务协议</li>
                                <li>AI协作生成OA正文</li>
                                <li>下载标准表单</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn primary-action" data-action="enter-bank-enterprise">
                                <span class="btn-icon">🚀</span>
                                <span class="btn-text">进入办理</span>
                            </button>
                        </div>
                    </div>

                    <!-- 客户接洽与资料准备模块 -->
                    <div class="business-module-card" data-module="customer-engagement">
                        <div class="module-header">
                            <div class="module-icon">📋</div>
                            <h3>客户接洽与资料准备</h3>
                        </div>
                        <div class="module-description">
                            <p>为客户提供标准化工具分发和资料收集管理服务</p>
                            <ul class="module-features">
                                <li>融资资料清单下载</li>
                                <li>定制化服务方案生成</li>
                                <li>客户资料上传管理</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn primary-action" data-action="enter-customer-engagement">
                                <span class="btn-icon">📁</span>
                                <span class="btn-text">进入办理</span>
                            </button>
                        </div>
                    </div>

                    <!-- 协定存款业务模块 -->
                    <div class="business-module-card" data-module="deposit-services">
                        <div class="module-header">
                            <div class="module-icon">💰</div>
                            <h3>协定存款业务</h3>
                        </div>
                        <div class="module-description">
                            <p>为企业客户提供协定存款协议生成与管理服务</p>
                            <ul class="module-features">
                                <li>协定存款协议生成</li>
                                <li>字体格式保持</li>
                                <li>协议条款管理</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn primary-action" data-action="enter-deposit-services">
                                <span class="btn-icon">💼</span>
                                <span class="btn-text">进入办理</span>
                            </button>
                        </div>
                    </div>

                    <!-- 合同支用模块 -->
                    <div class="business-module-card" data-module="contract-disbursement">
                        <div class="module-header">
                            <div class="module-icon">📋</div>
                            <h3>合同支用</h3>
                        </div>
                        <div class="module-description">
                            <p>合同支用与放款管理，包含审核台账、合同制作等功能</p>
                            <ul class="module-features">
                                <li>放款审核台账</li>
                                <li>合同制作信息</li>
                                <li>条件落实情况表</li>
                                <li>提款申请书</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn primary-action" data-action="enter-contract-disbursement">
                                <span class="btn-icon">📊</span>
                                <span class="btn-text">进入办理</span>
                            </button>
                        </div>
                    </div>

                    <!-- 授信申请模块 -->
                    <div class="business-module-card disabled" data-module="credit-application">
                        <div class="module-header">
                            <div class="module-icon">📄</div>
                            <h3>授信申请</h3>
                        </div>
                        <div class="module-description">
                            <p>为企业客户准备授信相关申请材料，进行信用评估</p>
                            <ul class="module-features">
                                <li>信用评估报告</li>
                                <li>授信申请材料</li>
                                <li>风险评估分析</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn disabled-action" disabled>
                                <span class="btn-icon">🔧</span>
                                <span class="btn-text">开发中</span>
                            </button>
                        </div>
                    </div>

                    <!-- 更多模块占位 -->
                    <div class="business-module-card add-module">
                        <div class="module-header">
                            <div class="module-icon">➕</div>
                            <h3>更多业务</h3>
                        </div>
                        <div class="module-description">
                            <p>更多业务模块正在开发中</p>
                            <ul class="module-features">
                                <li>敬请期待</li>
                                <li>持续更新</li>
                                <li>功能扩展</li>
                            </ul>
                        </div>
                        <div class="module-actions">
                            <button class="action-btn disabled-action" disabled>
                                <span class="btn-icon">⏳</span>
                                <span class="btn-text">敬请期待</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 右侧：操作日志面板 -->
        <aside class="logs-panel" id="logs-panel" style="display: none;">
            <div class="panel-header">
                <h3>📋 操作日志</h3>
                <button id="clear-logs-btn" class="clear-btn">清空</button>
            </div>
            <div id="operation-logs" class="logs-container-enhanced">
                <!-- 动态生成日志 -->
            </div>
        </aside>
    </div>

    <!-- JavaScript -->
    <script src="js/unified-cockpit-manager.js"></script>
</body>
</html>
