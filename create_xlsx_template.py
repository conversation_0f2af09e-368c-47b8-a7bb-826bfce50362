#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的.xlsx格式模板
"""

import openpyxl
from pathlib import Path
from openpyxl.styles import Font, PatternFill, Alignment

def create_xlsx_template():
    """创建.xlsx格式的信贷业务申请书模板"""
    try:
        print("🔧 创建.xlsx格式模板...")
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "信贷业务申请书"
        
        # 设置样式
        title_font = Font(name='宋体', size=16, bold=True)
        normal_font = Font(name='宋体', size=11)
        yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 设置列宽
        ws.column_dimensions['A'].width = 25
        ws.column_dimensions['B'].width = 60
        
        # 标题
        ws.merge_cells('A1:B1')
        ws['A1'] = "信贷业务申请书"
        ws['A1'].font = title_font
        ws['A1'].alignment = center_alignment
        
        # 内容行
        data_rows = [
            ("填报日期：", "2025年3月     日"),
            ("借款人：", "成都中科卓尔智能科技集团有限公司"),
            ("额度编号：", "PIFU510000000N202407210（额度）"),
            ("业务编号：", "PIFU5100000002025N00G8（业务）"),
            ("单户综合融资总量有效期：", "2024-03-06至2025-03-06"),
            ("担保方式：", "信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押"),
            ("本次支用金额：", "        万元"),
            ("期限：", "13个月"),
            ("担保合同编号：", "建八卓尔保（2024）001号"),
            ("质押合同编号：", "建八卓尔专质（2024）001号"),
            ("担保限额：", "担保限额为4000万元"),
            ("质押物价值：", "价值为328.98万元"),
            ("环保分类：", "借款人环保分类为C类"),
            ("客户经理签字日期：", "客户经理签字日期2025年7月日"),
            ("分管领导签字日期：", "分管经营行领导签字日期2025年7月日")
        ]
        
        # 填充数据
        for i, (label, value) in enumerate(data_rows, start=2):
            ws[f'A{i}'] = label
            ws[f'B{i}'] = value
            ws[f'B{i}'].fill = yellow_fill  # 标黄
            
            # 设置字体
            ws[f'A{i}'].font = normal_font
            ws[f'B{i}'].font = normal_font
        
        # 保存为.xlsx文件（注意这里明确指定.xlsx）
        output_file = Path("test_output/信贷业务申请书_正确模板.xlsx")
        
        # 确保保存为xlsx格式
        wb.save(str(output_file))
        wb.close()
        
        print(f"✅ .xlsx模板创建成功: {output_file}")
        
        # 验证文件
        wb2 = openpyxl.load_workbook(output_file)
        ws2 = wb2.active
        print(f"✅ 验证成功: {ws2['A1'].value}")
        print(f"📏 尺寸: {ws2.max_row} 行 x {ws2.max_column} 列")
        wb2.close()
        
        return output_file
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    create_xlsx_template()
