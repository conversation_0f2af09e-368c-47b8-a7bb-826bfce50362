/**
 * UI管理器模块
 * 负责界面元素的操作和状态管理
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.currentCompany = null;
        this.initializeElements();
        this.bindEvents();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            // 客户选择相关
            customerSelect: document.getElementById('customerSelect'),
            refreshBtn: document.getElementById('refreshBtn'),
            loadingIndicator: document.getElementById('loadingIndicator'),
            
            // 客户信息展示
            customerInfoSection: document.getElementById('customerInfoSection'),
            customerDetails: document.getElementById('customerDetails'),
            
            // 文档相关
            documentSection: document.getElementById('documentSection'),
            generateDocBtn: document.getElementById('generateDocBtn'),
            exportDataBtn: document.getElementById('exportDataBtn'),
            refreshTemplatesBtn: document.getElementById('refreshTemplatesBtn'),
            templateList: document.getElementById('templateList'),
            documentContent: document.getElementById('documentContent'),
            
            // 扩展信息
            extendedInfoSection: document.getElementById('extendedInfoSection'),
            extendedDetails: document.getElementById('extendedDetails'),
            
            // 日志相关
            logContainer: document.getElementById('logContainer'),
            clearLogBtn: document.getElementById('clearLogBtn'),
            
            // 模态框
            errorModal: document.getElementById('errorModal'),
            successModal: document.getElementById('successModal'),
            errorMessage: document.getElementById('errorMessage'),
            successMessage: document.getElementById('successMessage'),
            closeErrorModal: document.getElementById('closeErrorModal'),
            closeSuccessModal: document.getElementById('closeSuccessModal'),
            confirmErrorBtn: document.getElementById('confirmErrorBtn'),
            confirmSuccessBtn: document.getElementById('confirmSuccessBtn')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 客户选择变更
        this.elements.customerSelect.addEventListener('change', (e) => {
            this.onCustomerChange(e.target.value);
        });

        // 刷新按钮
        this.elements.refreshBtn.addEventListener('click', () => {
            this.refreshCustomerList();
        });

        // 文档生成按钮
        this.elements.generateDocBtn.addEventListener('click', () => {
            this.generateDocument();
        });

        // 数据导出按钮
        this.elements.exportDataBtn.addEventListener('click', () => {
            this.exportCustomerData();
        });

        // 刷新模板按钮
        this.elements.refreshTemplatesBtn.addEventListener('click', () => {
            this.refreshTemplates();
        });

        // 清空日志按钮
        this.elements.clearLogBtn.addEventListener('click', () => {
            this.clearLog();
        });

        // 模态框关闭事件
        this.elements.closeErrorModal.addEventListener('click', () => {
            this.hideErrorModal();
        });

        this.elements.closeSuccessModal.addEventListener('click', () => {
            this.hideSuccessModal();
        });

        this.elements.confirmErrorBtn.addEventListener('click', () => {
            this.hideErrorModal();
        });

        this.elements.confirmSuccessBtn.addEventListener('click', () => {
            this.hideSuccessModal();
        });

        // 点击模态框背景关闭
        this.elements.errorModal.addEventListener('click', (e) => {
            if (e.target === this.elements.errorModal) {
                this.hideErrorModal();
            }
        });

        this.elements.successModal.addEventListener('click', (e) => {
            if (e.target === this.elements.successModal) {
                this.hideSuccessModal();
            }
        });
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.elements.loadingIndicator.classList.remove('hidden');
        this.elements.refreshBtn.disabled = true;
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.elements.loadingIndicator.classList.add('hidden');
        this.elements.refreshBtn.disabled = false;
    }

    /**
     * 填充客户下拉列表
     */
    populateCustomerList(companies) {
        const select = this.elements.customerSelect;
        
        // 清空现有选项
        select.innerHTML = '<option value="">请选择客户...</option>';
        
        // 添加公司选项
        companies.forEach(company => {
            const option = document.createElement('option');
            option.value = company.id;
            option.textContent = company.company_name;
            select.appendChild(option);
        });

        this.addLog(`已加载 ${companies.length} 个客户企业`);
    }

    /**
     * 客户选择变更处理
     */
    async onCustomerChange(companyId) {
        if (!companyId) {
            this.clearCustomerInfo();
            // 清除工作流程管理器的当前公司
            if (window.workflowManager) {
                window.workflowManager.setCurrentCompany(null);
            }
            return;
        }

        try {
            this.addLog(`正在加载客户信息: ${companyId}`);
            const companyData = await apiClient.getCompanyDetail(companyId);
            this.displayCustomerInfo(companyData);
            this.currentCompany = companyData;
            this.enableDocumentButtons();
            this.addLog('客户信息加载成功', 'success');

            // 通知工作流程管理器当前选择的公司
            if (window.workflowManager) {
                window.workflowManager.setCurrentCompany(companyData);
                this.addLog(`工作流程已激活: ${companyData.company_name}`, 'info');
            }
        } catch (error) {
            this.addLog(`加载客户信息失败: ${error.getUserMessage()}`, 'error');
            this.showError(`加载客户信息失败: ${error.getUserMessage()}`);
            this.clearCustomerInfo();
        }
    }

    /**
     * 显示客户信息
     */
    displayCustomerInfo(company) {
        const container = this.elements.customerDetails;
        
        // 基本信息
        const basicInfo = this.createBasicInfoHTML(company);
        
        // 人员信息
        const personnelInfo = this.createPersonnelInfoHTML(company.personnel || []);
        
        // 标签信息
        const tagsInfo = this.createTagsInfoHTML(company.tags || []);
        
        // 扩展信息（未知字段）
        const extendedInfo = this.createExtendedInfoHTML(company);
        
        container.innerHTML = `
            ${basicInfo}
            ${personnelInfo}
            ${tagsInfo}
        `;

        // 显示扩展信息
        this.elements.extendedDetails.innerHTML = extendedInfo;
    }

    /**
     * 创建基本信息HTML
     */
    createBasicInfoHTML(company) {
        return `
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">企业名称</div>
                    <div class="info-value">${company.company_name || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">统一社会信用代码</div>
                    <div class="info-value" style="font-family: monospace; font-weight: bold; color: #2c3e50;">${company.unified_social_credit_code || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">企业ID</div>
                    <div class="info-value" style="font-family: monospace; font-size: 0.9em; color: #7f8c8d;">${company.id || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">注册地址</div>
                    <div class="info-value">${company.registered_address || '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号D区B5栋18层1805-1808号'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">通讯地址</div>
                    <div class="info-value">${company.communication_address || '成都市武侯区天府五街200号'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">业务简介</div>
                    <div class="info-value">${company.business_description || '国内领先的卫星通信整体解决方案提供商，主营业务为卫星通信系统搭建及配套调制解调器的销售，以及卫星通信地面终端设备及零配件。核心竞争力在于掌握了部分部委及央企的国产化卫星通信专网协议标准。'}</div>
                </div>
            </div>
        `;
    }

    /**
     * 创建人员信息HTML
     */
    createPersonnelInfoHTML(personnel) {
        if (!personnel || personnel.length === 0) {
            return '<div class="personnel-list"><p class="info-note">暂无人员信息</p></div>';
        }

        const personnelHTML = personnel.map(person => `
            <div class="personnel-item">
                <div class="personnel-header">${person.person_name} - ${person.role}</div>
                <div class="personnel-details">
                    <div><strong>证件类型:</strong> ${person.id_type}</div>
                    <div><strong>证件号码:</strong> ${person.id_number}</div>
                    <div><strong>状态:</strong> ${person.is_active ? '在职' : '离职'}</div>
                    ${person.start_date ? `<div><strong>开始日期:</strong> ${person.start_date}</div>` : ''}
                    ${person.end_date ? `<div><strong>结束日期:</strong> ${person.end_date}</div>` : ''}
                </div>
            </div>
        `).join('');

        return `
            <div class="personnel-list">
                <h4>人员信息 (${personnel.length}人)</h4>
                ${personnelHTML}
            </div>
        `;
    }

    /**
     * 创建标签信息HTML
     */
    createTagsInfoHTML(tags) {
        if (!tags || tags.length === 0) {
            return '<div class="tags-container"><p class="info-note">暂无标签信息</p></div>';
        }

        const tagsHTML = tags.map(tag => {
            const tagClass = tag.tag_category === '企业资质' ? 'qualification' : 'relationship';
            return `<span class="tag ${tagClass}">${tag.tag_name}</span>`;
        }).join('');

        return `
            <div class="tags-container">
                <h4>企业标签 (${tags.length}个)</h4>
                ${tagsHTML}
            </div>
        `;
    }

    /**
     * 创建扩展信息HTML（处理未知字段）
     */
    createExtendedInfoHTML(company) {
        const knownFields = new Set([
            'id', 'company_name', 'unified_social_credit_code', 
            'personnel', 'tags'
        ]);

        const unknownFields = Object.keys(company).filter(key => !knownFields.has(key));

        if (unknownFields.length === 0) {
            return '<p class="info-note">暂无扩展字段信息</p>';
        }

        const fieldsHTML = unknownFields.map(field => `
            <div class="unknown-field">
                <strong>${field}:</strong> ${JSON.stringify(company[field])}
            </div>
        `).join('');

        return `
            <h4>扩展字段 (${unknownFields.length}个)</h4>
            ${fieldsHTML}
        `;
    }

    /**
     * 清空客户信息
     */
    clearCustomerInfo() {
        this.elements.customerDetails.innerHTML = '<p class="no-selection">请先选择一个客户企业</p>';
        this.elements.extendedDetails.innerHTML = '<p class="info-note">此区域将显示数据库中新增但前端尚未显式支持的字段</p>';
        this.currentCompany = null;
        this.disableDocumentButtons();
    }

    /**
     * 启用文档按钮
     */
    enableDocumentButtons() {
        this.elements.generateDocBtn.disabled = false;
        this.elements.exportDataBtn.disabled = false;
    }

    /**
     * 禁用文档按钮
     */
    disableDocumentButtons() {
        this.elements.generateDocBtn.disabled = true;
        this.elements.exportDataBtn.disabled = true;
    }

    /**
     * 刷新客户列表
     */
    async refreshCustomerList() {
        try {
            this.showLoading();
            this.addLog('正在刷新客户列表...');
            
            const companies = await apiClient.getCompanies();
            this.populateCustomerList(companies);
            this.addLog('客户列表刷新成功', 'success');
        } catch (error) {
            this.addLog(`刷新客户列表失败: ${error.getUserMessage()}`, 'error');
            this.showError(`刷新客户列表失败: ${error.getUserMessage()}`);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * 生成文档
     */
    async generateDocument() {
        if (!this.currentCompany) {
            this.showError('请先选择一个客户企业');
            return;
        }

        try {
            this.addLog('开始生成业务文档...', 'info');

            // 显示加载状态
            this.elements.generateDocBtn.disabled = true;
            this.elements.generateDocBtn.textContent = '生成中...';

            // 生成HTML预览文档
            const documentHTML = window.documentGenerator.generateBankConnectDocument(this.currentCompany);
            this.elements.documentContent.innerHTML = documentHTML;

            // 生成并下载所有可用的模板文档
            const results = await window.documentGenerator.generateAllDocuments(this.currentCompany);

            // 显示生成结果
            this.displayDocumentResults(results);

            this.addLog('业务文档生成完成', 'success');

        } catch (error) {
            this.addLog(`文档生成失败: ${error.message}`, 'error');
            this.showError(`文档生成失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            this.elements.generateDocBtn.disabled = false;
            this.elements.generateDocBtn.textContent = '生成业务文档';
        }
    }

    /**
     * 显示文档生成结果
     */
    displayDocumentResults(results) {
        if (!results || results.length === 0) {
            this.addLog('没有可用的模板文件', 'info');
            return;
        }

        let successCount = 0;
        let errorCount = 0;

        results.forEach(result => {
            if (result.status === 'success') {
                successCount++;
                this.addLog(`✓ ${result.type}文档生成成功: ${result.filename}`, 'success');
            } else {
                errorCount++;
                this.addLog(`✗ ${result.type}文档生成失败: ${result.error}`, 'error');
            }
        });

        // 显示总结
        const summary = `文档生成完成 - 成功: ${successCount}, 失败: ${errorCount}`;
        this.addLog(summary, successCount > 0 ? 'success' : 'error');

        if (successCount > 0) {
            this.showSuccess(`成功生成 ${successCount} 个文档，请查看下载文件夹`);
        }
    }

    /**
     * 导出客户数据
     */
    exportCustomerData() {
        if (!this.currentCompany) {
            this.showError('请先选择一个客户企业');
            return;
        }

        try {
            const dataStr = JSON.stringify(this.currentCompany, null, 2);
            const blob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentCompany.company_name}_客户数据.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.addLog('客户数据导出成功', 'success');
        } catch (error) {
            this.addLog(`数据导出失败: ${error.message}`, 'error');
            this.showError(`数据导出失败: ${error.message}`);
        }
    }

    /**
     * 添加日志
     */
    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('p');
        logEntry.className = `log-entry ${type}`;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        this.elements.logContainer.appendChild(logEntry);
        this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
    }

    /**
     * 清空日志
     */
    clearLog() {
        this.elements.logContainer.innerHTML = '<p class="log-entry">日志已清空</p>';
    }

    /**
     * 显示错误模态框
     */
    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.errorModal.classList.remove('hidden');
    }

    /**
     * 隐藏错误模态框
     */
    hideErrorModal() {
        this.elements.errorModal.classList.add('hidden');
    }

    /**
     * 显示成功模态框
     */
    showSuccess(message) {
        this.elements.successMessage.textContent = message;
        this.elements.successModal.classList.remove('hidden');
    }

    /**
     * 隐藏成功模态框
     */
    hideSuccessModal() {
        this.elements.successModal.classList.add('hidden');
    }

    /**
     * 刷新模板列表
     */
    async refreshTemplates() {
        try {
            this.addLog('正在刷新模板列表...');
            await window.documentGenerator.loadAvailableTemplates();
            this.displayTemplateList();
            this.addLog('模板列表刷新成功', 'success');
        } catch (error) {
            this.addLog(`刷新模板列表失败: ${error.message}`, 'error');
            this.showError(`刷新模板列表失败: ${error.message}`);
        }
    }

    /**
     * 显示模板列表
     */
    displayTemplateList() {
        const rawTemplates = window.documentGenerator.getAvailableTemplates();

        if (!rawTemplates) {
            this.elements.templateList.innerHTML = '<p class="info-note">模板列表加载中...</p>';
            return;
        }

        // 使用白名单过滤模板
        const filteredTemplates = this.filterAllowedTemplates(rawTemplates);

        let html = '';

        // Word模板
        if (filteredTemplates.word && filteredTemplates.word.length > 0) {
            html += '<div class="template-category">';
            html += '<h5>📄 Word文档模板</h5>';
            html += '<ul class="template-items">';
            filteredTemplates.word.forEach(template => {
                const displayName = this.getTemplateDisplayName(template.filename);
                html += `<li class="template-item word-template" data-template="${template.filename}" data-template-id="${template.id}">${displayName}</li>`;
            });
            html += '</ul></div>';
        }

        // PDF模板
        if (filteredTemplates.pdf && filteredTemplates.pdf.length > 0) {
            html += '<div class="template-category">';
            html += '<h5>📋 PDF表单模板</h5>';
            html += '<ul class="template-items">';
            filteredTemplates.pdf.forEach(template => {
                const displayName = this.getTemplateDisplayName(template.filename);
                html += `<li class="template-item pdf-template" data-template="${template.filename}" data-template-id="${template.id}">${displayName}</li>`;
            });
            html += '</ul></div>';
        }

        if (html === '') {
            html = '<p class="info-note">未找到可用的模板文件</p>';
        }

        this.elements.templateList.innerHTML = html;

        // 添加模板项点击事件
        this.bindTemplateItemEvents();
    }

    /**
     * 过滤允许的模板（白名单机制）
     */
    filterAllowedTemplates(rawTemplates) {
        // 定义允许的模板白名单
        const ALLOWED_TEMPLATES = [
            { id: 'agreement', filename: 'yingqi_zhilian_agreement_blueprint.docx', name: '《服务协议》', type: 'word' },
            { id: 'agreement_test', filename: 'test_agreement_template.docx', name: '《服务协议》', type: 'word' },
            { id: 'agreement_with_placeholders', filename: 'yingqi_zhilian_agreement_blueprint_with_placeholders.docx', name: '《服务协议》', type: 'word' },
            { id: 'oa_text', filename: 'yingqi_zhilian_oa_text_blueprint.docx', name: '《OA正文》', type: 'word' },
            { id: 'application_form', filename: 'yingqi_zhilian_application_form_template.pdf', name: '《对公综合服务申请书》', type: 'pdf' },
            { id: 'authorization_form', filename: 'yingqi_zhilian_authorization_form_template.pdf', name: '《对公客户授权及承诺书》', type: 'pdf' }
        ];

        const filtered = {
            word: [],
            pdf: []
        };

        // 过滤Word模板
        if (rawTemplates.word) {
            rawTemplates.word.forEach(filename => {
                const allowedTemplate = ALLOWED_TEMPLATES.find(t =>
                    t.filename === filename && t.type === 'word'
                );
                if (allowedTemplate) {
                    filtered.word.push(allowedTemplate);
                }
            });
        }

        // 过滤PDF模板
        if (rawTemplates.pdf) {
            rawTemplates.pdf.forEach(filename => {
                const allowedTemplate = ALLOWED_TEMPLATES.find(t =>
                    t.filename === filename && t.type === 'pdf'
                );
                if (allowedTemplate) {
                    filtered.pdf.push(allowedTemplate);
                }
            });
        }

        // 去重：如果有多个服务协议模板，只显示一个
        filtered.word = this.deduplicateTemplates(filtered.word);

        return filtered;
    }

    /**
     * 去重模板（优先显示测试模板）
     */
    deduplicateTemplates(templates) {
        const seen = new Set();
        const result = [];

        // 优先级排序：test > with_placeholders > 原始
        const priority = {
            'test_agreement_template.docx': 1,
            'yingqi_zhilian_agreement_blueprint_with_placeholders.docx': 2,
            'yingqi_zhilian_agreement_blueprint.docx': 3
        };

        templates.sort((a, b) => {
            const priorityA = priority[a.filename] || 999;
            const priorityB = priority[b.filename] || 999;
            return priorityA - priorityB;
        });

        templates.forEach(template => {
            if (!seen.has(template.name)) {
                seen.add(template.name);
                result.push(template);
            }
        });

        return result;
    }

    /**
     * 获取模板显示名称
     */
    getTemplateDisplayName(templateName) {
        // 模板中文名称映射（完整白名单）
        const templateNames = {
            // Word模板
            'yingqi_zhilian_agreement_blueprint.docx': '《服务协议》',
            'test_agreement_template.docx': '《服务协议》',
            'yingqi_zhilian_agreement_blueprint_with_placeholders.docx': '《服务协议》',
            'yingqi_zhilian_oa_text_blueprint.docx': '《OA正文》',

            // PDF模板
            'yingqi_zhilian_authorization_form_template.pdf': '《对公客户授权及承诺书》',
            'yingqi_zhilian_application_form_template.pdf': '《对公综合服务申请书》'
        };

        // 返回中文名称，如果没有映射则返回原名称（但这种情况不应该发生，因为有白名单过滤）
        return templateNames[templateName] || templateName;
    }

    /**
     * 绑定模板项事件
     */
    bindTemplateItemEvents() {
        const templateItems = this.elements.templateList.querySelectorAll('.template-item');

        templateItems.forEach(item => {
            item.addEventListener('click', async () => {
                const templateName = item.dataset.template;
                const isWordTemplate = item.classList.contains('word-template');

                if (!this.currentCompany && isWordTemplate) {
                    this.showError('请先选择一个客户企业');
                    return;
                }

                try {
                    this.addLog(`正在生成文档: ${templateName}...`);

                    if (isWordTemplate) {
                        await window.documentGenerator.downloadWordDocument(this.currentCompany.id, templateName);
                    } else {
                        await window.documentGenerator.downloadPdfDocument(templateName);
                    }

                    this.addLog(`文档生成成功: ${templateName}`, 'success');

                } catch (error) {
                    this.addLog(`文档生成失败: ${error.message}`, 'error');
                    this.showError(`文档生成失败: ${error.message}`);
                }
            });
        });
    }
}

// 创建全局UI管理器实例
const uiManager = new UIManager();

// 导出供其他模块使用
window.UIManager = UIManager;
window.uiManager = uiManager;
