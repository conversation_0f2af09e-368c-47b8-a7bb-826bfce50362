#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从申报书中提取完整的条件内容
包括用信前提条件、持续条件、管理条件的完整描述
"""

import docx
from pathlib import Path
import re

def extract_complete_conditions():
    """提取完整条件内容"""
    project_root = Path(__file__).parent
    test_output_dir = project_root / "test_output"
    
    print("🔍 提取完整条件内容")
    print("="*60)
    
    results = {}
    
    # 读取额度申报书
    quota_file = test_output_dir / "额度申报书.docx"
    if quota_file.exists():
        print(f"\n📄 分析额度申报书...")
        quota_doc = docx.Document(quota_file)
        quota_text = extract_full_text(quota_doc)
        
        quota_conditions = extract_conditions_from_text(quota_text, "额度申报书")
        results.update(quota_conditions)
    
    # 读取业务申报书
    business_file = test_output_dir / "业务申报书.docx"
    if business_file.exists():
        print(f"\n📄 分析业务申报书...")
        business_doc = docx.Document(business_file)
        business_text = extract_full_text(business_doc)
        
        business_conditions = extract_conditions_from_text(business_text, "业务申报书")
        results.update(business_conditions)
    
    # 显示提取结果
    display_results(results)
    
    return results

def extract_full_text(doc):
    """提取文档的完整文本"""
    full_text = ""
    
    # 提取段落
    for para in doc.paragraphs:
        full_text += para.text + "\n"
    
    # 提取表格
    for table in doc.tables:
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = ""
                for para in cell.paragraphs:
                    cell_text += para.text + " "
                row_text.append(cell_text.strip())
            full_text += " | ".join(row_text) + "\n"
    
    return full_text

def extract_conditions_from_text(text, source):
    """从文本中提取条件内容"""
    conditions = {}
    
    print(f"🔍 从{source}中提取条件...")
    
    # 1. 提取用信前提条件
    prerequisite_patterns = [
        r'用信前提条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'放款条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'提款条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
    ]
    
    for pattern in prerequisite_patterns:
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        if matches:
            content = matches[0].strip()
            if len(content) > 20:  # 确保不是片段
                conditions['prerequisite_conditions'] = content
                print(f"   ✅ 用信前提条件: {content[:100]}...")
                break
    
    # 2. 提取持续条件
    continuous_patterns = [
        r'持续条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'持续性条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'持续管理条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
    ]
    
    for pattern in continuous_patterns:
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        if matches:
            content = matches[0].strip()
            if len(content) > 20:
                conditions['continuous_conditions'] = content
                print(f"   ✅ 持续条件: {content[:100]}...")
                break
    
    # 3. 提取管理条件
    management_patterns = [
        r'管理条件[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'贷后管理[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
        r'管理要求[：:]?\s*([^。]*(?:。[^。]*)*?)(?=\n\s*[一二三四五六七八九十]|\n\s*\d+\.|\n\s*[（(]\d+[）)]|\n\s*[A-Za-z]\.|\Z)',
    ]
    
    for pattern in management_patterns:
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        if matches:
            content = matches[0].strip()
            if len(content) > 20:
                conditions['management_conditions'] = content
                print(f"   ✅ 管理条件: {content[:100]}...")
                break
    
    # 4. 搜索包含"1.2倍"的完整段落（用信前提条件的详细描述）
    lines = text.split('\n')
    for i, line in enumerate(lines):
        if '1.2倍' in line:
            # 获取前后几行作为完整上下文
            start = max(0, i-2)
            end = min(len(lines), i+3)
            context = '\n'.join(lines[start:end]).strip()
            if len(context) > 50:
                conditions['detailed_prerequisite'] = context
                print(f"   ✅ 详细用信前提条件: {context[:100]}...")
            break
    
    # 5. 搜索ESG相关内容
    esg_patterns = [
        r'ESG[^。]*(?:。[^。]*)*?(?=\n|\Z)',
        r'环保[^。]*(?:。[^。]*)*?(?=\n|\Z)',
        r'绿色[^。]*(?:。[^。]*)*?(?=\n|\Z)',
    ]
    
    for pattern in esg_patterns:
        matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
        if matches:
            for match in matches:
                if len(match.strip()) > 10:
                    conditions['esg_description'] = match.strip()
                    print(f"   ✅ ESG描述: {match.strip()[:100]}...")
                    break
    
    return conditions

def display_results(results):
    """显示提取结果"""
    print(f"\n📊 完整条件提取结果:")
    print("="*60)
    
    if not results:
        print("❌ 未提取到条件内容")
        return
    
    for key, value in results.items():
        print(f"\n🔹 {key}:")
        print(f"   {value}")
        print("-" * 40)

def generate_esg_description(classification="ESG绿色"):
    """生成ESG描述的标准模板"""
    description = f"该企业{classification}分类，整体经营符合绿色环保理念，业务发展与环境保护协调统一，具备良好的可持续发展能力。"
    return description

def main():
    results = extract_complete_conditions()
    
    print(f"\n💡 ESG描述建议:")
    print("="*60)
    esg_desc = generate_esg_description("ESG绿色")
    print(f"标准描述: {esg_desc}")
    
    return results

if __name__ == "__main__":
    main()
