-- 信贷业务申请书字段扩展
-- 为companies表添加信贷相关字段

-- 添加信贷业务相关字段
ALTER TABLE companies ADD COLUMN credit_line_number TEXT DEFAULT 'KHED5104885002025228054';
ALTER TABLE companies ADD COLUMN business_number TEXT DEFAULT 'KHED510488500202522805';
ALTER TABLE companies ADD COLUMN guarantee_method TEXT;
ALTER TABLE companies ADD COLUMN loan_amount DECIMAL(15,2);
ALTER TABLE companies ADD COLUMN loan_term_months INTEGER;
ALTER TABLE companies ADD COLUMN guarantee_amount DECIMAL(15,2) DEFAULT 4000.00;
ALTER TABLE companies ADD COLUMN pledge_value DECIMAL(15,2) DEFAULT 328.98;
ALTER TABLE companies ADD COLUMN company_short_name TEXT;

-- 更新现有数据的公司简称
UPDATE companies SET company_short_name = 
  CASE 
    WHEN company_name LIKE '%卓尔%' THEN '卓尔'
    WHEN company_name LIKE '%神光%' THEN '神光'
    ELSE SUBSTR(company_name, 1, 2)
  END
WHERE company_short_name IS NULL;

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_companies_credit_line ON companies(credit_line_number);
CREATE INDEX IF NOT EXISTS idx_companies_business_number ON companies(business_number);
