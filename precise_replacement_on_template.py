#!/usr/bin/env python3
"""
基于现有落实情况表模板进行精确替换
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
import sqlite3

def precise_replacement_on_template():
    print('=== 基于现有模板进行精确替换 ===\n')
    
    # 文件路径
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 1. 提取源数据
    print('📖 提取源数据...')
    precondition_text = extract_precondition(quota_file)
    continuous_conditions = extract_continuous_conditions(quota_file)
    loan_conditions = extract_loan_conditions(business_file)
    
    # 2. 获取企业数据
    print('📊 获取企业数据...')
    company_data = get_company_data()
    
    # 3. 加载模板并替换
    print('🔄 加载模板并进行替换...')
    doc = docx.Document(template_file)
    
    # 4. 替换基础信息（红色显示）
    replace_basic_info(doc, company_data)
    
    # 5. 替换三个核心部分（红色显示）
    replace_three_core_sections(doc, precondition_text, continuous_conditions, loan_conditions)
    
    # 6. 保存到test目录
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(f'test_output/落实情况表_基于模板替换_{timestamp}.docx')
    doc.save(output_path)
    
    print(f'✅ 替换完成！输出文件: {output_path}')
    return output_path

def extract_precondition(quota_file):
    """提取用信前提条件"""
    doc = docx.Document(quota_file)
    # 第5个表格，第2行，第2列
    table = doc.tables[4]
    text = table.rows[1].cells[1].text.strip()
    print(f'✅ 提取用信前提条件: {len(text)}字符')
    return text

def extract_continuous_conditions(quota_file):
    """提取持续条件"""
    doc = docx.Document(quota_file)
    # 第6个表格
    table = doc.tables[5]
    conditions = []
    
    for row_idx in range(len(table.rows)):
        row_data = []
        for cell in table.rows[row_idx].cells:
            row_data.append(cell.text.strip())
        conditions.append(row_data)
    
    print(f'✅ 提取持续条件: {len(conditions)}行')
    return conditions

def extract_loan_conditions(business_file):
    """提取贷款条件"""
    doc = docx.Document(business_file)
    # 第7个表格
    table = doc.tables[6]
    conditions = []
    
    for row in table.rows:
        if len(row.cells) >= 2:
            number = row.cells[0].text.strip()
            content = row.cells[1].text.strip()
            if number and content:
                conditions.append({'number': number, 'content': content})
    
    print(f'✅ 提取贷款条件: {len(conditions)}条')
    return conditions

def get_company_data():
    """从数据库获取企业数据"""
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        # 获取成都中科卓尔的数据
        cursor.execute("""
            SELECT company_name, unified_social_credit_code, legal_representative,
                   registered_capital, business_scope
            FROM companies 
            WHERE company_name LIKE '%中科卓尔%'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'company_name': result[0],
                'unified_social_credit_code': result[1],
                'legal_representative': result[2],
                'registered_capital': result[3],
                'business_scope': result[4]
            }
        else:
            print('❌ 未找到企业数据')
            return None
            
    except Exception as e:
        print(f'❌ 数据库查询失败: {e}')
        return None

def set_text_red(paragraph):
    """将段落文字设置为红色"""
    for run in paragraph.runs:
        run.font.color.rgb = RGBColor(255, 0, 0)  # 红色

def replace_basic_info(doc, company_data):
    """替换基础信息并标记为红色"""
    if not company_data:
        return
    
    print('🔄 替换基础信息...')
    
    # 遍历所有段落和表格，查找并替换基础信息
    for paragraph in doc.paragraphs:
        if '公司名称' in paragraph.text or '企业名称' in paragraph.text:
            # 替换公司名称
            for run in paragraph.runs:
                if '公司名称' in run.text:
                    run.text = run.text.replace('公司名称', company_data['company_name'])
                    run.font.color.rgb = RGBColor(255, 0, 0)
    
    # 在表格中查找并替换
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                if '公司名称' in cell.text:
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            if '公司名称' in run.text:
                                run.text = run.text.replace('公司名称', company_data['company_name'])
                                run.font.color.rgb = RGBColor(255, 0, 0)

def replace_three_core_sections(doc, precondition_text, continuous_conditions, loan_conditions):
    """替换三个核心部分并标记为红色"""
    print('🔄 替换三个核心部分...')
    
    # 遍历所有表格，查找需要替换的内容
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                cell_text = cell.text.strip()
                
                # 1. 替换用信前提条件
                if '用信前提条件' in cell_text and len(cell_text) > 20:
                    print(f'  🔄 替换用信前提条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    # 清空单元格内容
                    cell._element.clear_content()
                    paragraph = cell.add_paragraph()
                    run = paragraph.add_run(precondition_text)
                    run.font.color.rgb = RGBColor(255, 0, 0)
                
                # 2. 查找持续条件相关的表格
                elif '持续条件' in cell_text and '持续的评级水平' in cell_text:
                    print(f'  🔄 替换持续条件表格 (表格{table_idx+1})')
                    replace_continuous_conditions_table(table, continuous_conditions)
                
                # 3. 替换贷款条件
                elif '贷款条件' in cell_text and ('担保措施' in cell_text or '贸易背景' in cell_text):
                    print(f'  🔄 替换贷款条件 (表格{table_idx+1}, 行{row_idx+1}, 列{cell_idx+1})')
                    # 清空单元格内容
                    cell._element.clear_content()
                    paragraph = cell.add_paragraph()

                    # 将贷款条件合并为文本
                    loan_text = '\n'.join([f"{cond['number']}. {cond['content']}" for cond in loan_conditions])
                    run = paragraph.add_run(loan_text)
                    run.font.color.rgb = RGBColor(255, 0, 0)

def replace_continuous_conditions_table(table, continuous_conditions):
    """替换持续条件表格内容"""
    # 清空现有内容并用红色填入新内容
    for row_idx, condition_row in enumerate(continuous_conditions):
        if row_idx < len(table.rows):
            table_row = table.rows[row_idx]
            for col_idx, content in enumerate(condition_row):
                if col_idx < len(table_row.cells):
                    cell = table_row.cells[col_idx]
                    # 清空单元格内容
                    cell._element.clear_content()
                    paragraph = cell.add_paragraph()
                    run = paragraph.add_run(content)
                    run.font.color.rgb = RGBColor(255, 0, 0)

if __name__ == "__main__":
    precise_replacement_on_template()
