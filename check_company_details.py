#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查公司详细信息
"""

import requests
import json

def check_company_details():
    """检查所有公司的详细信息"""
    
    companies = [
        ("34af7659-d69a-4c05-a697-6ae6eb00aad3", "成都卫讯科技有限公司"),
        ("a1b2c3d4-e5f6-7890-1234-567890abcdef", "成都中科卓尔智能科技集团有限公司"),
        ("14371dda-2f8f-4d4c-82e6-c431dcf3b146", "神光光学集团有限公司")
    ]
    
    print("=" * 80)
    print("📋 检查公司详细信息")
    print("=" * 80)
    
    for company_id, expected_name in companies:
        print(f"\n🏢 检查公司: {expected_name}")
        print(f"🆔 ID: {company_id}")
        print("-" * 60)
        
        try:
            response = requests.get(f"http://localhost:5000/api/company/{company_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()["data"]
                
                print(f"✅ 公司名称: {data.get('company_name', 'N/A')}")
                print(f"✅ 信用代码: {data.get('unified_social_credit_code', 'N/A')}")
                print(f"✅ 注册地址: {data.get('registered_address', 'N/A')}")
                print(f"✅ 法定代表人: {data.get('legal_representative', 'N/A')}")
                print(f"✅ 业务描述: {data.get('business_description', 'N/A')}")
                print(f"✅ 联系电话: {data.get('contact_phone', 'N/A')}")
                print(f"✅ 联系邮箱: {data.get('contact_email', 'N/A')}")
                
                # 检查是否需要更新
                needs_update = []
                if not data.get('unified_social_credit_code'):
                    needs_update.append("统一社会信用代码")
                if not data.get('registered_address'):
                    needs_update.append("注册地址")
                if not data.get('legal_representative'):
                    needs_update.append("法定代表人")
                
                if needs_update:
                    print(f"⚠️  需要更新的字段: {', '.join(needs_update)}")
                else:
                    print("✅ 信息完整，无需更新")
                    
            else:
                print(f"❌ 获取失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    check_company_details()
