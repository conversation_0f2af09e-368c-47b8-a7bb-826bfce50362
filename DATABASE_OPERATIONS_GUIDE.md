# 🗄️ 企业服务数据库操作指南

## 📋 **概述**

本文档详细说明了企业服务系统的数据库架构、操作方法和最佳实践。系统使用SQLite数据库，支持企业信息管理、人员关系管理和标签管理。

---

## 🏗️ **数据库架构**

### **核心表结构**

#### **1. companies（公司主表）**
```sql
CREATE TABLE companies (
    id TEXT PRIMARY KEY,                    -- 公司唯一标识符
    unified_social_credit_code TEXT UNIQUE NOT NULL,  -- 统一社会信用代码
    company_name TEXT NOT NULL,             -- 公司名称
    registered_address TEXT,                -- 注册地址
    communication_address TEXT,             -- 通讯地址
    business_description TEXT,              -- 业务描述
    legal_representative TEXT,              -- 法定代表人
    registration_date DATE,                 -- 注册日期
    registered_capital REAL,                -- 注册资本
    business_scope TEXT,                    -- 经营范围
    contact_phone TEXT,                     -- 联系电话
    contact_email TEXT,                     -- 联系邮箱
    website TEXT,                           -- 网站
    industry_category TEXT,                 -- 行业分类
    company_type TEXT,                      -- 公司类型
    business_status TEXT DEFAULT 'active',  -- 经营状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **2. persons（人员表）**
```sql
CREATE TABLE persons (
    id TEXT PRIMARY KEY,
    id_number TEXT UNIQUE NOT NULL,         -- 身份证号
    id_type TEXT NOT NULL,                  -- 证件类型
    person_name TEXT NOT NULL,              -- 姓名
    mobile_phone TEXT,                      -- 手机号
    other_contact TEXT,                     -- 其他联系方式
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **3. company_person_relationships（公司人员关系表）**
```sql
CREATE TABLE company_person_relationships (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL,
    person_id TEXT NOT NULL,
    relationship_type TEXT NOT NULL,        -- 关系类型（法定代表人、股东等）
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (person_id) REFERENCES persons(id)
);
```

#### **4. company_tags（公司标签表）**
```sql
CREATE TABLE company_tags (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL,
    tag_name TEXT NOT NULL,                 -- 标签名称
    tag_category TEXT,                      -- 标签分类
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id)
);
```

---

## 🔧 **数据库操作方法**

### **1. 数据库初始化**

```bash
# 运行初始化脚本
python init_sqlite_database.py
```

**功能：**
- 创建所有必要的表结构
- 插入初始测试数据
- 验证数据库完整性

### **2. 添加新公司**

#### **方法A：通过API**
```python
import requests

company_data = {
    "company_name": "新公司名称",
    "unified_social_credit_code": "91510100MA62TGHL5X",  # 18位标准格式
    "registered_address": "注册地址",
    "legal_representative": "法定代表人姓名",
    "business_description": "业务描述",
    "operated_by_user_id": "12345678-1234-1234-1234-123456789012",
    "operation_reason": "录入原因"
}

response = requests.post("http://localhost:5000/api/companies", json=company_data)
```

#### **方法B：直接数据库操作**
```python
import sqlite3

conn = sqlite3.connect("database/enterprise_service.db")
cursor = conn.cursor()

cursor.execute("""
    INSERT INTO companies (
        id, company_name, unified_social_credit_code, 
        registered_address, legal_representative, business_description
    ) VALUES (?, ?, ?, ?, ?, ?)
""", (
    "新的UUID",
    "公司名称",
    "统一社会信用代码",
    "注册地址",
    "法定代表人",
    "业务描述"
))

conn.commit()
conn.close()
```

### **3. 更新公司信息**

#### **直接数据库更新（推荐）**
```python
import sqlite3
from datetime import datetime

conn = sqlite3.connect("database/enterprise_service.db")
cursor = conn.cursor()

# 更新公司信息
cursor.execute("""
    UPDATE companies 
    SET 
        legal_representative = ?,
        registered_address = ?,
        updated_at = ?
    WHERE id = ?
""", (
    "新的法定代表人",
    "新的注册地址",
    datetime.now().isoformat(),
    "公司ID"
))

conn.commit()
conn.close()
```

### **4. 查询公司信息**

#### **获取所有公司**
```python
import requests

response = requests.get("http://localhost:5000/api/companies")
companies = response.json()["data"]
```

#### **获取特定公司详情**
```python
import requests

company_id = "14371dda-2f8f-4d4c-82e6-c431dcf3b146"
response = requests.get(f"http://localhost:5000/api/company/{company_id}")
company_detail = response.json()["data"]
```

### **5. 删除公司**

```python
import sqlite3

conn = sqlite3.connect("database/enterprise_service.db")
cursor = conn.cursor()

# 软删除（推荐）
cursor.execute("""
    UPDATE companies 
    SET business_status = 'inactive', updated_at = ?
    WHERE id = ?
""", (datetime.now().isoformat(), "公司ID"))

# 硬删除（谨慎使用）
# cursor.execute("DELETE FROM companies WHERE id = ?", ("公司ID",))

conn.commit()
conn.close()
```

---

## ⚠️ **重要注意事项**

### **1. 统一社会信用代码格式**
- **必须是18位**
- **格式：** `^[0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}$`
- **不能包含：** I、O、V、Z等字母
- **示例：** `91510100MA62TGHL5X`

### **2. UUID格式**
- **所有ID字段必须使用标准UUID格式**
- **示例：** `14371dda-2f8f-4d4c-82e6-c431dcf3b146`

### **3. API vs 直接数据库操作**
- **API更新功能目前未完全实现**
- **推荐使用直接数据库操作进行更新**
- **查询操作优先使用API**

### **4. 数据库文件位置**
- **路径：** `database/enterprise_service.db`
- **备份：** 定期备份数据库文件

---

## 🛠️ **常用操作脚本**

### **1. 验证数据库状态**
```bash
python check_db_structure.py
```

### **2. 验证API服务**
```bash
python verify_api.py
```

### **3. 重新初始化数据库**
```bash
# 删除现有数据库
del database\enterprise_service.db

# 重新初始化
python init_sqlite_database.py
```

---

## 🔍 **故障排除**

### **问题1：API返回空数据**
**原因：** API服务使用模拟数据而非真实数据库
**解决：** 确保`api/services/company_service.py`中`USE_REAL_DB = True`

### **问题2：数据库文件不存在**
**解决：** 运行`python init_sqlite_database.py`

### **问题3：统一社会信用代码格式错误**
**解决：** 使用18位标准格式，避免使用I、O、V、Z等字母

### **问题4：API服务无法启动**
**解决：** 检查端口5000是否被占用，确保依赖包已安装

---

## 📊 **数据示例**

### **当前系统中的公司数据**
1. **成都卫讯科技有限公司**
   - ID: `34af7659-d69a-4c05-a697-6ae6eb00aad3`
   - 信用代码: `915101003320526751`
   - 法人: `万明刚`

2. **成都中科卓尔智能科技集团有限公司**
   - ID: `a1b2c3d4-e5f6-7890-1234-567890abcdef`
   - 信用代码: `91510100MA6CGUGA1W`
   - 法人: `杨伟`

3. **神光光学集团有限公司**
   - ID: `14371dda-2f8f-4d4c-82e6-c431dcf3b146`
   - 信用代码: `91510100MA62TGHL5X`
   - 法人: `贾秉炜`

---

## 🎯 **最佳实践**

1. **始终备份数据库**
2. **使用事务处理批量操作**
3. **验证数据格式后再插入**
4. **优先使用软删除**
5. **定期验证数据完整性**
6. **记录所有重要操作**

---

**📝 最后更新：2025-07-30**
**🔧 维护者：Augment Agent**
