#!/usr/bin/env python3
"""
生成落实情况表 - 直接复制原始表格格式
"""

import docx
from pathlib import Path
import sqlite3
from datetime import datetime
from docx.shared import RGBColor

class ConditionChecklistGeneratorWithTables:
    def __init__(self):
        self.template_path = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
        self.quota_doc_path = Path('templates/contract_disbursement/额度申报书.docx')
        self.business_doc_path = Path('templates/contract_disbursement/业务申报书.docx')
        self.db_path = Path('database/enterprise_service.db')
    
    def generate_checklist(self, output_path=None):
        """生成落实情况表，直接复制原始表格"""
        if not self.template_path.exists():
            print('模板文件不存在')
            return False
        
        # 加载模板
        doc = docx.Document(self.template_path)
        
        # 获取表格
        if not doc.tables:
            print('模板中没有找到表格')
            return False
        
        table = doc.tables[0]
        
        try:
            # 填充基础信息
            self.fill_basic_info(table)
            
            # 直接复制原始表格内容
            self.copy_original_table_content(table)
            
            # 生成输出文件
            if output_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = f'test_output/落实情况表_原始表格_{timestamp}.docx'
            
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文档
            doc.save(output_path)
            print(f'落实情况表已生成: {output_path}')
            return True
            
        except Exception as e:
            print(f'生成失败: {e}')
            return False
    
    def fill_basic_info(self, table):
        """填充基础信息"""
        # 填报日期
        current_date = datetime.now()
        date_text = f'填报日期：{current_date.year}年{current_date.month}月{current_date.day}日'
        cell = table.rows[0].cells[1]
        cell.text = date_text
        self.set_text_color(cell, RGBColor(255, 0, 0))
        
        # 单户综合融资总量
        cell = table.rows[3].cells[0]
        current_text = cell.text
        new_text = current_text.replace('40,000,000.00万元', '4000万元')
        cell.text = new_text
        
        # 单笔业务债项批复金额
        cell = table.rows[4].cells[0]
        current_text = cell.text
        new_text = current_text.replace('4000.00万元', '1300万元')
        cell.text = new_text
        
        # 期限
        cell = table.rows[6].cells[1]
        current_text = cell.text
        new_text = current_text.replace('期限：', '期限：13个月')
        cell.text = new_text
    
    def copy_original_table_content(self, target_table):
        """直接复制原始文档中的表格内容"""
        # 目标单元格：行9（索引8）
        target_cell_left = target_table.rows[8].cells[0]
        target_cell_right = target_table.rows[8].cells[1]
        
        # 清空原有内容
        target_cell_left.text = ""
        target_cell_right.text = ""
        
        # 构建内容
        content = self.build_content_from_source_tables()
        
        # 填入内容并标红
        target_cell_left.text = content
        target_cell_right.text = content
        
        self.set_text_color(target_cell_left, RGBColor(255, 0, 0))
        self.set_text_color(target_cell_right, RGBColor(255, 0, 0))
    
    def build_content_from_source_tables(self):
        """从源文档构建内容"""
        content = ""
        
        # 一、用信前提条件
        content += "一、单户综合融资总量方案申报书中列明的用信前提条件及落实情况\n\n"
        content += "本次新增贷款的最终支用日不晚于2025年9月30日。\n\n"
        
        # 二、持续条件（从额度申报书表格20提取）
        content += "二、单户综合融资总量方案申报书中列明的持续条件及落实情况\n\n"
        
        # 从额度申报书提取的表格内容
        quota_conditions = self.extract_quota_table_content()
        content += quota_conditions + "\n\n"
        
        # 三、业务贷款条件（从业务申报书表格7提取）
        content += "三、单笔业务申报书中列明的贷款条件及落实情况\n\n"
        
        # 从业务申报书提取的表格内容
        business_conditions = self.extract_business_table_content()
        content += business_conditions
        
        return content
    
    def extract_quota_table_content(self):
        """从额度申报书提取表格20的内容"""
        if not self.quota_doc_path.exists():
            return "额度申报书文件不存在"
        
        doc = docx.Document(self.quota_doc_path)
        
        # 查找表格20（索引19）
        if len(doc.tables) > 19:
            table = doc.tables[19]  # 表格20
            content = ""
            
            # 提取相关行的内容
            relevant_rows = [8, 9, 10, 11, 15]  # 行9,10,11,12,16
            
            for row_idx in relevant_rows:
                if row_idx < len(table.rows):
                    row = table.rows[row_idx]
                    if len(row.cells) > 2:
                        condition_text = row.cells[2].text.strip()
                        if condition_text:
                            content += f"• {condition_text}\n\n"
            
            return content
        else:
            return "未找到表格20"
    
    def extract_business_table_content(self):
        """从业务申报书提取表格7的内容"""
        if not self.business_doc_path.exists():
            return "业务申报书文件不存在"
        
        doc = docx.Document(self.business_doc_path)
        
        # 查找表格7（索引6）
        if len(doc.tables) > 6:
            table = doc.tables[6]  # 表格7
            content = ""
            
            # 提取相关行的内容
            for row_idx in range(1, min(4, len(table.rows))):  # 行2,3,4
                row = table.rows[row_idx]
                if len(row.cells) > 1:
                    condition_text = row.cells[1].text.strip()
                    if condition_text and condition_text != "/":
                        content += f"• {condition_text}\n\n"
            
            return content
        else:
            return "未找到表格7"
    
    def set_text_color(self, cell, color):
        """设置单元格文本颜色"""
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.color.rgb = color

def main():
    generator = ConditionChecklistGeneratorWithTables()
    success = generator.generate_checklist()
    
    if success:
        print('✅ 落实情况表生成成功！')
    else:
        print('❌ 落实情况表生成失败！')

if __name__ == "__main__":
    main()
