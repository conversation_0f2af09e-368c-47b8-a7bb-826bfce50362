#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析模板中的标黄字段
第1步：识别所有需要替换的黄色字段
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
from pathlib import Path
import json

def analyze_yellow_fields():
    """分析模板中的标黄字段"""
    project_root = Path(__file__).parent
    template_path = project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
    
    print("🔍 第1步：分析模板中的标黄字段")
    print("="*60)
    
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_path}")
        return []
    
    try:
        doc = docx.Document(template_path)
        
        yellow_fields = []
        field_id = 1
        
        print(f"📄 模板文件: {template_path.name}")
        print(f"\n🟡 发现的标黄字段:")
        print("-" * 60)
        
        # 检查段落中的黄色字段
        for para_idx, paragraph in enumerate(doc.paragraphs):
            for run_idx, run in enumerate(paragraph.runs):
                if run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                    field_info = {
                        'id': field_id,
                        'location': 'paragraph',
                        'para_index': para_idx,
                        'run_index': run_idx,
                        'text': run.text.strip(),
                        'context': paragraph.text.strip()[:200] + "..." if len(paragraph.text) > 200 else paragraph.text.strip(),
                        'category': _categorize_field(run.text.strip()),
                        'length': len(run.text.strip())
                    }
                    yellow_fields.append(field_info)
                    
                    print(f"   {field_id:2d}. [{field_info['category']}] '{run.text.strip()}' (长度: {len(run.text)})")
                    print(f"       位置: 段落 {para_idx}")
                    print(f"       上下文: {field_info['context'][:100]}...")
                    print()
                    
                    field_id += 1
        
        # 检查表格中的黄色字段
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        for run_idx, run in enumerate(paragraph.runs):
                            if run.font.highlight_color == WD_COLOR_INDEX.YELLOW:
                                field_info = {
                                    'id': field_id,
                                    'location': 'table',
                                    'table_index': table_idx,
                                    'row_index': row_idx,
                                    'cell_index': cell_idx,
                                    'para_index': para_idx,
                                    'run_index': run_idx,
                                    'text': run.text.strip(),
                                    'context': paragraph.text.strip()[:200] + "..." if len(paragraph.text) > 200 else paragraph.text.strip(),
                                    'category': _categorize_field(run.text.strip()),
                                    'length': len(run.text.strip())
                                }
                                yellow_fields.append(field_info)
                                
                                print(f"   {field_id:2d}. [{field_info['category']}] '{run.text.strip()}' (长度: {len(run.text)})")
                                print(f"       位置: 表格 {table_idx}, 行 {row_idx}, 列 {cell_idx}")
                                print(f"       上下文: {field_info['context'][:100]}...")
                                print()
                                
                                field_id += 1
        
        # 统计信息
        print(f"📊 统计信息:")
        print("-" * 60)
        print(f"   📝 总标黄字段数: {len(yellow_fields)}")
        
        # 按类别统计
        categories = {}
        for field in yellow_fields:
            category = field['category']
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        print(f"   📋 按类别统计:")
        for category, count in categories.items():
            print(f"      {category}: {count}个")
        
        # 保存分析结果
        output_file = project_root / "test_output" / "yellow_fields_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(yellow_fields, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存: {output_file}")
        
        return yellow_fields
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def _categorize_field(text):
    """对字段进行分类"""
    text_lower = text.lower().strip()
    
    # 企业信息
    if any(keyword in text for keyword in ['成都', '中科卓尔', '智能科技', '公司']):
        return '企业信息'
    
    # 编号信息
    if any(keyword in text for keyword in ['PIFU', 'KHED', '编号', '文号']):
        return '编号信息'
    
    # 金额信息
    if any(keyword in text for keyword in ['万元', '金额', '资金']):
        return '金额信息'
    
    # 日期信息
    if any(keyword in text for keyword in ['年', '月', '日', '期']):
        return '日期信息'
    
    # 条件描述
    if any(keyword in text for keyword in ['条件', '要求', '管理', '评级', '负债率', '流动比率']):
        return '条件描述'
    
    # 落实情况
    if any(keyword in text for keyword in ['落实', '截止', '已', '符合', '完成']):
        return '落实情况'
    
    # 担保信息
    if any(keyword in text for keyword in ['担保', '保证', '质押', '抵押']):
        return '担保信息'
    
    # 合同信息
    if any(keyword in text for keyword in ['合同', '协议', '签署', '签订']):
        return '合同信息'
    
    # 其他
    return '其他'

def main():
    yellow_fields = analyze_yellow_fields()
    
    if yellow_fields:
        print(f"\n✅ 第1步完成：发现 {len(yellow_fields)} 个标黄字段")
        print(f"📝 下一步：从申报书中提取对应内容")
    else:
        print(f"\n❌ 第1步失败：未发现标黄字段")

if __name__ == "__main__":
    main()
