# 企业服务文档生成系统依赖包
# Python 3.8+ 兼容

# 核心文档处理
python-docx>=0.8.11          # Word文档处理
openpyxl>=3.1.0              # Excel文档处理
xlsxwriter>=3.1.0            # Excel文档写入

# 配置文件处理
PyYAML>=6.0                  # YAML配置文件
jsonschema>=4.17.0           # JSON配置验证

# 日志和监控
colorlog>=6.7.0              # 彩色日志输出

# 数据处理
pandas>=1.5.0                # 数据分析处理 (可选)

# 类型提示
typing-extensions>=4.4.0     # 类型提示扩展

# 开发和测试 (可选)
pytest>=7.2.0               # 单元测试框架
pytest-cov>=4.0.0           # 测试覆盖率
black>=22.0.0                # 代码格式化
flake8>=5.0.0                # 代码检查

# AI集成预留 (可选，暂时注释)
# openai>=0.27.0             # OpenAI API
# anthropic>=0.3.0           # Anthropic API
# requests>=2.28.0           # HTTP请求

# 原有依赖 (保留兼容，但Web功能将被移除)
Flask==2.3.3
Flask-CORS==4.0.0
psycopg2-binary==2.9.7
python-dotenv==1.0.0
gunicorn==21.2.0
PyPDF2==3.0.1
reportlab==4.0.4
