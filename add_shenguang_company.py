#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据操作员脚本：录入神光光学集团有限公司
"""

import requests
import json

def add_shenguang_company():
    """添加神光光学集团有限公司到数据库"""
    
    # API端点
    api_url = "http://localhost:5000/api/companies"
    
    # 构造客户数据
    company_data = {
        "company_name": "神光光学集团有限公司",
        "unified_social_credit_code": "91510100MA62TGHL5X",  # 18位标准格式，将S替换为T
        "operated_by_user_id": "12345678-1234-1234-1234-123456789012",  # 标准UUID格式
        "operation_reason": "录入神光光学集团有限公司档案",
        "registered_address": "待补充 - 集团总部注册地址",
        "mailing_address": "待补充 - 通讯地址",
        "contact_email": "待补充 - 联系邮箱",
        "business_description": "主营业务为高纯度合成熔石英的研发和生产，是可控核聚变、半导体光掩膜版、精密光学仪器等领域此前'卡脖子'的关键上游原材料。国家级专精特新小巨人企业。",
        "industry_tags": [
            "国家级专精特新小巨人",
            "制造业",
            "合成熔石英",
            "半导体材料",
            "军工"
        ],
        "key_personnel": [
            {
                "name": "卢卫民",
                "position": "总经理",
                "id_type": "身份证",
                "notes": "新任命总经理"
            },
            {
                "name": "奥山道雄",
                "position": "副总经理",
                "id_type": "身份证",
                "notes": "新任命副总经理"
            }
        ],
        "company_notes": "公司无实际控制人，由董事会领导。专注于高纯度合成熔石英技术，服务于可控核聚变、半导体等高科技领域。"
    }
    
    print("🎯 开始录入神光光学集团有限公司数据...")
    print("📋 发送的数据:")
    print(json.dumps(company_data, ensure_ascii=False, indent=2))
    print()
    
    try:
        # 发送POST请求
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.post(
            api_url, 
            json=company_data, 
            headers=headers,
            timeout=30
        )
        
        print(f"📡 API响应状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ 数据录入成功!")
            print("📄 响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 获取新创建的公司ID
            if 'data' in result and 'id' in result['data']:
                company_id = result['data']['id']
                print(f"\n🆔 新公司ID: {company_id}")
                
                # 验证数据是否正确录入
                verify_company(company_id)
                
            return True
            
        else:
            print("❌ 数据录入失败!")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API调用异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def verify_company(company_id):
    """验证公司数据是否正确录入"""
    try:
        verify_url = f"http://localhost:5000/api/company/{company_id}"
        response = requests.get(verify_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("\n🔍 数据验证成功!")
            print("✅ 公司信息已正确存储在数据库中")
            
            company_info = result.get('data', {})
            print(f"   - 公司名称: {company_info.get('company_name')}")
            print(f"   - 信用代码: {company_info.get('unified_social_credit_code')}")
            print(f"   - 业务描述: {company_info.get('business_description', '')[:50]}...")
            
        else:
            print(f"\n⚠️ 数据验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"\n⚠️ 验证过程出错: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 企业数据录入系统")
    print("📋 任务: 录入神光光学集团有限公司")
    print("=" * 60)
    
    success = add_shenguang_company()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 任务完成!")
        print("新客户'神光光学集团有限公司'的完整档案已成功导入数据库。")
        print("系统现已可以为该公司提供所有自动化服务。")
    else:
        print("❌ 任务失败!")
        print("请检查API服务状态和数据格式。")
    print("=" * 60)
