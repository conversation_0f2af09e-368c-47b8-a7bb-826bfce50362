#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统日志模块
提供统一的日志记录、错误追踪、性能监控功能
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any


class SystemLogger:
    """系统日志管理器"""
    
    def __init__(self, name: str = "enterprise_service", 
                 log_level: str = "INFO",
                 log_file: Optional[str] = None):
        """初始化日志器"""
        self.name = name
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # 设置日志文件路径
        if log_file:
            self.log_file = Path(log_file)
        else:
            log_dir = Path(__file__).parent.parent / "logs"
            log_dir.mkdir(exist_ok=True)
            self.log_file = log_dir / f"{name}.log"
        
        # 创建日志器
        self.logger = self._setup_logger()
        
        # 性能监控
        self.performance_stats = {
            'start_time': datetime.now(),
            'operations': 0,
            'errors': 0,
            'warnings': 0
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        # 创建日志器
        logger = logging.getLogger(self.name)
        logger.setLevel(self.log_level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器 (带轮转)
        try:
            file_handler = logging.handlers.RotatingFileHandler(
                self.log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"警告: 无法创建文件日志处理器: {e}")
        
        return logger
    
    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录信息日志"""
        self.performance_stats['operations'] += 1
        if extra:
            message = f"{message} | {extra}"
        self.logger.info(message)
    
    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录警告日志"""
        self.performance_stats['warnings'] += 1
        if extra:
            message = f"{message} | {extra}"
        self.logger.warning(message)
    
    def error(self, message: str, extra: Optional[Dict[str, Any]] = None, 
              exc_info: bool = False):
        """记录错误日志"""
        self.performance_stats['errors'] += 1
        if extra:
            message = f"{message} | {extra}"
        self.logger.error(message, exc_info=exc_info)
    
    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录调试日志"""
        if extra:
            message = f"{message} | {extra}"
        self.logger.debug(message)
    
    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """记录严重错误日志"""
        self.performance_stats['errors'] += 1
        if extra:
            message = f"{message} | {extra}"
        self.logger.critical(message)
    
    def log_operation(self, operation: str, company_id: str = None, 
                     template_key: str = None, result: str = "success",
                     duration: float = None, details: Dict[str, Any] = None):
        """记录操作日志"""
        log_data = {
            'operation': operation,
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        
        if company_id:
            log_data['company_id'] = company_id
        if template_key:
            log_data['template_key'] = template_key
        if duration:
            log_data['duration_seconds'] = round(duration, 3)
        if details:
            log_data.update(details)
        
        message = f"操作记录: {operation}"
        if result == "success":
            self.info(message, log_data)
        elif result == "warning":
            self.warning(message, log_data)
        else:
            self.error(message, log_data)
    
    def log_document_generation(self, company_id: str, template_key: str,
                              output_path: str, replacements: int = 0,
                              duration: float = None, success: bool = True):
        """记录文档生成日志"""
        details = {
            'output_path': output_path,
            'replacements': replacements
        }
        
        result = "success" if success else "error"
        self.log_operation(
            operation="document_generation",
            company_id=company_id,
            template_key=template_key,
            result=result,
            duration=duration,
            details=details
        )
    
    def log_batch_operation(self, operation: str, total_count: int,
                          success_count: int, duration: float = None):
        """记录批量操作日志"""
        details = {
            'total_count': total_count,
            'success_count': success_count,
            'failure_count': total_count - success_count,
            'success_rate': f"{(success_count/total_count)*100:.1f}%" if total_count > 0 else "0%"
        }
        
        result = "success" if success_count == total_count else "warning"
        self.log_operation(
            operation=f"batch_{operation}",
            result=result,
            duration=duration,
            details=details
        )
    
    def log_performance_stats(self):
        """记录性能统计"""
        current_time = datetime.now()
        uptime = current_time - self.performance_stats['start_time']
        
        stats = {
            'uptime_seconds': uptime.total_seconds(),
            'total_operations': self.performance_stats['operations'],
            'total_errors': self.performance_stats['errors'],
            'total_warnings': self.performance_stats['warnings'],
            'error_rate': f"{(self.performance_stats['errors']/max(self.performance_stats['operations'], 1))*100:.2f}%"
        }
        
        self.info("性能统计", stats)
    
    def get_log_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取日志摘要"""
        try:
            # 这里可以实现日志文件分析
            # 暂时返回当前会话的统计
            return {
                'period_hours': hours,
                'operations': self.performance_stats['operations'],
                'errors': self.performance_stats['errors'],
                'warnings': self.performance_stats['warnings'],
                'uptime_seconds': (datetime.now() - self.performance_stats['start_time']).total_seconds()
            }
        except Exception as e:
            self.error(f"获取日志摘要失败: {e}")
            return {}
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        try:
            log_dir = self.log_file.parent
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            
            deleted_count = 0
            for log_file in log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    deleted_count += 1
            
            self.info(f"清理旧日志文件: 删除 {deleted_count} 个文件")
            
        except Exception as e:
            self.error(f"清理日志文件失败: {e}")


class OperationTimer:
    """操作计时器上下文管理器"""
    
    def __init__(self, logger: SystemLogger, operation: str, **kwargs):
        self.logger = logger
        self.operation = operation
        self.kwargs = kwargs
        self.start_time = None
        self.success = True
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.debug(f"开始操作: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            
            if exc_type is not None:
                self.success = False
                self.logger.error(f"操作失败: {self.operation} - {exc_val}")
            
            result = "success" if self.success else "error"
            self.logger.log_operation(
                operation=self.operation,
                result=result,
                duration=duration,
                **self.kwargs
            )
    
    def mark_failure(self, reason: str = None):
        """标记操作失败"""
        self.success = False
        if reason:
            self.logger.warning(f"操作标记为失败: {self.operation} - {reason}")


# 创建全局日志器实例
default_logger = SystemLogger()

# 便捷函数
def get_logger(name: str = None) -> SystemLogger:
    """获取日志器实例"""
    if name:
        return SystemLogger(name)
    return default_logger

def log_operation(operation: str, **kwargs):
    """记录操作的装饰器"""
    def decorator(func):
        def wrapper(*args, **func_kwargs):
            with OperationTimer(default_logger, operation, **kwargs):
                return func(*args, **func_kwargs)
        return wrapper
    return decorator
