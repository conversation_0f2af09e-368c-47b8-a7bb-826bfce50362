# 企业服务文档生成系统 - 模板配置文件
# 定义所有业务模板的配置信息、字段映射、AI生成规则等

version: "2.0"
last_updated: "2025-01-01"
description: "企业服务文档生成系统模板配置 - 支持AI字段生成和智能校验"

# 全局配置
global_settings:
  # 输出目录配置
  output_base_directory: "output"
  # 文件命名规则：{template_id}_{company_name}_{timestamp}
  file_naming_pattern: "{template_id}_{company_name}_{timestamp}"
  timestamp_format: "%Y%m%d_%H%M%S"

  # 字体格式保持配置
  format_preservation:
    font_name: "宋体"
    font_size: 14
    highlight_color: "YELLOW"
    preserve_original_format: true

  # AI集成配置
  ai_integration:
    enabled: true
    confidence_threshold: 0.8
    auto_generate_missing: true

# 业务分类定义
categories:
  deposit_services:
    name: "协定存款服务"
    description: "协定存款相关文档模板"
    icon: "🏦"
    color: "#4CAF50"
    output_subdirectory: "deposit_agreements"

  contract_disbursement:
    name: "合同放款"
    description: "合同放款相关文档模板"
    icon: "📋"
    color: "#2196F3"
    output_subdirectory: "disbursement_docs"

  customer_engagement:
    name: "客户接洽"
    description: "客户接洽相关文档模板"
    icon: "🤝"
    color: "#FF9800"
    output_subdirectory: "engagement_materials"

# 模板定义
templates:
  # 协定存款业务模板
  deposit_agreement:
    # 模板基本信息
    id: "deposit_agreement"
    name: "协定存款协议"
    description: "中国建设银行人民币单位协定存款合同标准文本"
    category: "deposit_services"

    # 模板文件配置
    template_file:
      path: "templates/deposit_services/中国建设银行人民币单位协定存款合同（标准文本）1.docx"
      type: "docx"  # 支持: docx, xlsx, xlsm
      encoding: "utf-8"

    # 输出配置
    output:
      subdirectory: "deposit_agreements"
      filename_prefix: "协定存款协议"
      file_extension: "docx"

    # 使用字段列表（与field_mapping.json对应）
    fields:
      required:  # 必填字段
        - "company_name"
        - "unified_social_credit_code"
        - "legal_representative"
        - "deposit_amount"
        - "account_number"

      optional:  # 可选字段
        - "registered_address"
        - "contact_phone"
        - "agreement_date"
        - "current_date"

      ai_generated:  # AI自动生成字段
        - "deposit_amount"        # 根据企业规模推荐存款额度
        - "interest_rate_adjustment"  # 根据企业等级推荐利率调整
        - "agreement_number"      # 自动生成协议编号
        - "agreement_date"        # 智能推荐协议日期
        - "current_date"          # 系统当前日期

    # 字段映射（模板占位符 -> 字段名）
    field_mappings:
      "【公司名称】": "company_name"
      "【企业名称】": "company_name"
      "【统一社会信用代码】": "unified_social_credit_code"
      "【法定代表人】": "legal_representative"
      "【注册地址】": "registered_address"
      "【联系电话】": "contact_phone"
      "【协议编号】": "agreement_number"
      "【账户号码】": "account_number"
      "【存款金额】": "deposit_amount"
      "【利率调整】": "interest_rate_adjustment"
      "【协议日期】": "agreement_date"
      "【当前日期】": "current_date"
      "（大写）            万": "deposit_amount"
      "在乙方开立协定存款账户的账号：待补充": "account_number"
      "活期存款账户（账号：待补充_）": "account_number"

    # 默认值配置
    default_values:
      deposit_amount: 1000
      interest_rate_adjustment: 45
      account_number: "51050148850800008651"

    # 校验规则
    validation:
      deposit_amount:
        min_value: 100
        max_value: 100000
        format: "integer"
      unified_social_credit_code:
        length: 18
        pattern: "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$"
      account_number:
        length: 20
        pattern: "^\\d{20}$"

  # 合同放款业务模板
  disbursement_checklist:
    # 模板基本信息
    id: "disbursement_checklist"
    name: "放款条件核查表"
    description: "合同放款条件落实情况核查表"
    category: "contract_disbursement"

    # 模板文件配置
    template_file:
      path: "templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx"
      type: "docx"
      encoding: "utf-8"

    # 输出配置
    output:
      subdirectory: "disbursement_docs"
      filename_prefix: "放款条件核查表"
      file_extension: "docx"

    # 使用字段列表
    fields:
      required:  # 必填字段
        - "company_name"
        - "unified_social_credit_code"
        - "legal_representative"
        - "loan_amount"
        - "loan_purpose"
        - "repayment_source"
        - "guarantee_method"

      optional:  # 可选字段
        - "registered_address"
        - "loan_term"
        - "interest_rate"
        - "current_date"

      ai_generated:  # AI自动生成字段
        - "interest_rate"         # 根据企业等级推荐贷款利率
        - "current_date"          # 系统当前日期

      user_input:  # 需要用户输入的字段
        - "loan_amount"
        - "loan_purpose"
        - "repayment_source"
        - "guarantee_method"
        - "loan_term"

    # 字段映射（模板占位符 -> 字段名）
    field_mappings:
      "【公司名称】": "company_name"
      "【企业名称】": "company_name"
      "【统一社会信用代码】": "unified_social_credit_code"
      "【法定代表人】": "legal_representative"
      "【注册地址】": "registered_address"
      "【贷款金额】": "loan_amount"
      "【贷款用途】": "loan_purpose"
      "【还款来源】": "repayment_source"
      "【担保方式】": "guarantee_method"
      "【贷款期限】": "loan_term"
      "【贷款利率】": "interest_rate"
      "【当前日期】": "current_date"
      "{{还款来源}}": "repayment_source"
      "还款来源审查": "repayment_source"

    # 默认值配置
    default_values:
      guarantee_method: "信用"
      loan_term: 12

    # 校验规则
    validation:
      loan_amount:
        min_value: 10
        max_value: 1000000
        format: "decimal"
      loan_purpose:
        min_length: 10
        max_length: 500
      repayment_source:
        min_length: 20
        max_length: 1000
      guarantee_method:
        enum_values: ["信用", "保证", "抵押", "质押", "组合担保"]

  # 信贷业务申请书模板
  credit_application:
    # 模板基本信息
    id: "credit_application"
    name: "信贷业务申请书"
    description: "银行信贷业务申请书标准模板"
    category: "contract_disbursement"

    # 模板文件配置
    template_file:
      path: "test_output/简单信贷申请书.xlsm"
      type: "xlsm"
      encoding: "utf-8"

    # 输出配置
    output:
      subdirectory: "test_output"
      filename_prefix: "信贷业务申请书"
      file_extension: "xlsm"

    # 使用字段列表
    fields:
      required:  # 必填字段
        - "company_name"
        - "legal_representative"
        - "guarantee_method"
        - "loan_amount"
        - "loan_term_months"

      optional:  # 可选字段
        - "unified_social_credit_code"
        - "registered_address"
        - "application_date"
        - "credit_line_number"
        - "business_number"
        - "pledge_contract_number"
        - "guarantee_contract_number"
        - "guarantee_amount"
        - "pledge_value"
        - "environmental_category"
        - "manager_sign_date"
        - "leader_sign_date"

      ai_generated:  # AI自动生成字段
        - "application_date"          # 当前年月+空日
        - "credit_line_number"        # 固定额度编号
        - "business_number"           # 固定业务编号
        - "pledge_contract_number"    # 自动生成合同编号
        - "guarantee_contract_number" # 自动生成担保合同编号
        - "guarantee_amount"          # 固定担保限额
        - "pledge_value"              # 固定质押物价值
        - "environmental_category"    # 固定ESG分类
        - "manager_sign_date"         # 当前年月+空日
        - "leader_sign_date"          # 当前年月+空日

      user_input:  # 需要用户输入的字段
        - "guarantee_method"          # 担保方式详细描述
        - "loan_amount"               # 本次支用金额
        - "loan_term_months"          # 贷款期限

    # 字段映射（模板占位符 -> 字段名）
    field_mappings:
      # 基础企业信息
      "成都中科卓尔智能科技集团有限公司": "company_name"
      "杨伟": "legal_representative"

      # 日期字段
      "2025年3月日": "application_date"
      "填报日期：2025年3月     日": "application_date"
      "客户经理签字日期2025年7月日": "manager_sign_date"
      "分管经营行领导签字日期2025年7月日": "leader_sign_date"

      # 编号字段
      "PIFU510000000N202407210（额度）": "credit_line_number"
      "PIFU5100000002025N00G8（业务）": "business_number"
      "批复号：KHED510488500202522805": "business_number"

      # 担保相关
      "信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押": "guarantee_method"
      "建八卓尔保（2024）001号": "guarantee_contract_number"
      "建八卓尔专质（2024）001号": "pledge_contract_number"
      "建八卓尔专质(2024）001号": "pledge_contract_number"
      "担保限额为4000万元": "guarantee_amount"
      "价值为328.98万元": "pledge_value"

      # 贷款信息
      "本次支用金额万元": "loan_amount"
      "期限13个月": "loan_term_months"

      # 环保分类
      "借款人环保分类为C类": "environmental_category"
      "ESG分类为绿色": "environmental_category"

    # 默认值配置
    default_values:
      credit_line_number: "KHED5104885002025228054"
      business_number: "KHED510488500202522805"
      guarantee_amount: 4000
      pledge_value: 328.98
      environmental_category: "ESG分类为绿色"

    # 校验规则
    validation:
      loan_amount:
        min_value: 1
        max_value: 100000
        format: "decimal"
      loan_term_months:
        min_value: 1
        max_value: 360
        format: "integer"
      guarantee_method:
        min_length: 5
        max_length: 500

  # 客户接洽业务模板
  customer_engagement_plan:
    # 模板基本信息
    id: "customer_engagement_plan"
    name: "客户接洽计划"
    description: "客户接洽服务计划书"
    category: "customer_engagement"

    # 模板文件配置
    template_file:
      path: "templates/customer_engagement/customer_engagement_plan_blueprint.docx"
      type: "docx"
      encoding: "utf-8"

    # 输出配置
    output:
      subdirectory: "engagement_materials"
      filename_prefix: "客户接洽计划"
      file_extension: "docx"

    # 使用字段列表
    fields:
      required:  # 必填字段
        - "company_name"
        - "contact_person"
        - "engagement_purpose"
        - "engagement_date"

      optional:  # 可选字段
        - "unified_social_credit_code"
        - "legal_representative"
        - "contact_phone"
        - "service_plan"
        - "current_date"

      ai_generated:  # AI自动生成字段
        - "service_plan"          # 根据企业需求推荐服务方案
        - "current_date"          # 系统当前日期

      user_input:  # 需要用户输入的字段
        - "contact_person"
        - "engagement_purpose"
        - "engagement_date"

    # 字段映射（模板占位符 -> 字段名）
    field_mappings:
      "【公司名称】": "company_name"
      "【企业名称】": "company_name"
      "【统一社会信用代码】": "unified_social_credit_code"
      "【法定代表人】": "legal_representative"
      "【联系人】": "contact_person"
      "【企业联系人】": "contact_person"
      "【联系电话】": "contact_phone"
      "【接洽目的】": "engagement_purpose"
      "【接洽内容】": "engagement_purpose"
      "【接洽日期】": "engagement_date"
      "【服务方案】": "service_plan"
      "【解决方案】": "service_plan"
      "【当前日期】": "current_date"

    # 默认值配置
    default_values: {}

    # 校验规则
    validation:
      contact_person:
        min_length: 2
        max_length: 100
      engagement_purpose:
        min_length: 10
        max_length: 500
      engagement_date:
        date_format: "YYYY-MM-DD"

# 全局字段映射
field_mappings:
  common:
    "【公司名称】": "company_name"
    "【企业名称】": "company_name"
    "【公司全称】": "company_name"
    "【统一社会信用代码】": "unified_social_credit_code"
    "【信用代码】": "unified_social_credit_code"
    "【法定代表人】": "legal_representative"
    "【法人代表】": "legal_representative"
    "【法人姓名】": "legal_representative"
    "【注册地址】": "registered_address"
    "【经营范围】": "business_scope"
    "【联系电话】": "contact_phone"
    "【联系邮箱】": "contact_email"
    "【当前日期】": "current_date"
    "【申请日期】": "application_date"
    "【签署日期】": "signature_date"

# 输出设置
output_settings:
  base_directory: "output"
  naming_pattern: "{template_id}_{company_name}_{timestamp}"
  timestamp_format: "%Y%m%d_%H%M%S"
  create_subdirectories: true
  
  # 文件清理设置
  cleanup:
    enabled: true
    retention_days: 30
    auto_cleanup: false

# 日志设置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/template_system.log"
  max_file_size: "10MB"
  backup_count: 5

# 验证设置
validation:
  strict_mode: false
  warn_on_missing_fields: true
  fail_on_invalid_data: false
  
  # 字段验证规则
  field_rules:
    unified_social_credit_code:
      pattern: "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$"
      length: 18
      
    contact_phone:
      pattern: "^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$"
      
    contact_email:
      pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

# 扩展设置 (为AI功能预留)
extensions:
  ai_integration:
    enabled: false
    provider: "openai"
    model: "gpt-4"
    api_key_env: "OPENAI_API_KEY"
    
  auto_completion:
    enabled: false
    confidence_threshold: 0.8
    
  batch_processing:
    enabled: true
    max_concurrent: 5
    timeout_seconds: 300
