/* 银企直联模块样式 - 独立的插件样式 */

.yingqi-zhilian-module {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
}

/* 模块头部 */
.module-header {
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    flex: 1;
}

.module-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.module-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #4CAF50;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 80px;
}

/* 工作流程容器 */
.workflow-container {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.workflow-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

/* 工作流程项目 */
.workflow-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.workflow-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.task-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.task-checkbox {
    position: relative;
}

.task-check {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.task-check:checked {
    background: #4CAF50;
    border-color: #4CAF50;
}

.task-check:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.task-content {
    flex: 1;
}

.task-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.task-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover:not(:disabled) {
    background: #5a6268;
    transform: translateY(-2px);
}

.action-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* 状态反馈区域 */
.status-feedback {
    padding: 1rem 2rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.feedback-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    background: white;
    border: 1px solid #e9ecef;
}

.feedback-content.success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.feedback-content.error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.feedback-content.processing {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.feedback-icon {
    font-size: 1.1rem;
}

.feedback-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* OA工作台模态窗口 */
.oa-workspace-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.oa-workspace-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.oa-workspace-placeholder {
    padding: 3rem;
    text-align: center;
}

.oa-workspace-placeholder h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.oa-workspace-placeholder p {
    margin: 0 0 2rem 0;
    color: #6c757d;
}

.oa-workspace-placeholder button {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
}

.oa-workspace-placeholder button:hover {
    background: #5a6fd8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .module-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .workflow-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .task-info {
        flex-direction: column;
        text-align: center;
    }
    
    .progress-bar {
        width: 150px;
    }
}
