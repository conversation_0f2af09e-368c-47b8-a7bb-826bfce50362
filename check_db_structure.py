#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库结构
"""

import sqlite3
import os

def check_database():
    """检查数据库结构"""
    
    db_path = "database/enterprise_service.db"
    print(f"🔍 检查数据库文件: {db_path}")
    print(f"📁 文件存在: {os.path.exists(db_path)}")
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 数据库中的表 ({len(tables)} 个):")
        for table in tables:
            print(f"   - {table[0]}")
        
        # 如果有companies表，检查其结构
        if any(table[0] == 'companies' for table in tables):
            print("\n🏢 companies表结构:")
            cursor.execute("PRAGMA table_info(companies)")
            columns = cursor.fetchall()
            
            for col in columns:
                print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查是否有神光光学的记录
        if any(table[0] == 'companies' for table in tables):
            print("\n🔍 查找神光光学集团有限公司记录:")
            cursor.execute("""
                SELECT id, company_name, unified_social_credit_code, 
                       registered_address, legal_representative
                FROM companies 
                WHERE company_name LIKE '%神光%'
            """)
            
            records = cursor.fetchall()
            if records:
                for record in records:
                    print(f"   ✅ 找到记录:")
                    print(f"      ID: {record[0]}")
                    print(f"      名称: {record[1]}")
                    print(f"      信用代码: {record[2]}")
                    print(f"      注册地址: {record[3] or '待补充'}")
                    print(f"      法定代表人: {record[4] or '待补充'}")
            else:
                print("   ❌ 未找到神光光学相关记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")

if __name__ == "__main__":
    check_database()
