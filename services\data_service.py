#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据服务模块
负责数据库连接、字段读取、数据校验
"""

import sqlite3
import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from utils.logger import SystemLogger


class UnifiedDataService:
    """统一数据服务类 - 从SQLite数据库读取企业数据并进行标准化校验"""

    def __init__(self, db_path: Optional[str] = None, field_mapping_path: Optional[str] = None):
        """初始化数据服务"""
        self.logger = SystemLogger("data_service")

        # 设置数据库路径
        self.db_path = db_path or self._get_default_db_path()

        # 加载字段映射配置
        self.field_mapping_path = field_mapping_path or self._get_default_field_mapping_path()
        self.field_dictionary = self._load_field_mapping()

        # 验证数据库连接
        self._verify_database_connection()

        self.logger.info("UnifiedDataService 初始化完成")

    def _get_default_db_path(self) -> str:
        """获取默认数据库路径"""
        project_root = Path(__file__).parent.parent
        return str(project_root / "database" / "enterprise_service.db")

    def _get_default_field_mapping_path(self) -> str:
        """获取默认字段映射配置路径"""
        project_root = Path(__file__).parent.parent
        return str(project_root / "config" / "field_mapping.json")

    def _load_field_mapping(self) -> Dict[str, Any]:
        """加载字段映射配置"""
        try:
            config_path = Path(self.field_mapping_path)
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    field_dict = config.get('field_dictionary', {})
                    self.logger.info(f"加载字段映射配置成功，共 {len(field_dict)} 个字段")
                    return field_dict
            else:
                self.logger.warning("字段映射配置文件不存在，使用默认映射")
                return self._get_default_field_mapping()
        except Exception as e:
            self.logger.error(f"加载字段映射失败: {e}")
            return self._get_default_field_mapping()

    def _get_default_field_mapping(self) -> Dict[str, Any]:
        """获取默认字段映射"""
        return {
            "company_name": {
                "field": "company_name",
                "label": "公司名称",
                "required": True,
                "type": "text"
            },
            "unified_social_credit_code": {
                "field": "unified_social_credit_code",
                "label": "统一社会信用代码",
                "required": True,
                "type": "text"
            },
            "legal_representative": {
                "field": "legal_representative",
                "label": "法定代表人",
                "required": True,
                "type": "text"
            }
        }

    def _verify_database_connection(self):
        """验证数据库连接"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM companies")
                count = cursor.fetchone()[0]
                self.logger.info(f"数据库连接成功，共有 {count} 家企业")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise ConnectionError(f"无法连接到数据库: {e}")

    def get_companies_list(self) -> List[Dict[str, str]]:
        """获取企业列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, company_name, unified_social_credit_code
                    FROM companies
                    ORDER BY company_name
                """)

                companies = []
                for row in cursor.fetchall():
                    companies.append({
                        'id': row[0],
                        'company_name': row[1],
                        'unified_social_credit_code': row[2]
                    })

                self.logger.info(f"获取企业列表成功，共 {len(companies)} 家企业")
                return companies

        except Exception as e:
            self.logger.error(f"获取企业列表失败: {e}")
            raise

    def get_company_data_by_id(self, company_id: str) -> Optional[Dict[str, Any]]:
        """根据企业ID获取企业数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, company_name, unified_social_credit_code,
                           legal_representative, registered_address,
                           business_scope, contact_phone, contact_email,
                           registered_capital, credit_line_number, business_number,
                           guarantee_method, loan_amount, loan_term_months,
                           guarantee_amount, pledge_value, company_short_name
                    FROM companies
                    WHERE id = ?
                """, (company_id,))

                row = cursor.fetchone()
                if row:
                    company_data = {
                        'id': row[0],
                        'company_name': row[1],
                        'unified_social_credit_code': row[2],
                        'legal_representative': row[3],
                        'registered_address': row[4] or "",
                        'business_scope': row[5] or "",
                        'contact_phone': row[6] or "",
                        'contact_email': row[7] or "",
                        'registered_capital': row[8] or "",
                        'credit_line_number': row[9] or "KHED5104885002025228054",
                        'business_number': row[10] or "KHED510488500202522805",
                        'guarantee_method': row[11] or "",
                        'loan_amount': row[12] or 0,
                        'loan_term_months': row[13] or 0,
                        'guarantee_amount': row[14] or 4000,
                        'pledge_value': row[15] or 328.98,
                        'company_short_name': row[16] or self._extract_company_short_name(row[1])
                    }

                    self.logger.info(f"获取企业数据成功: {company_data['company_name']}")
                    return company_data
                else:
                    self.logger.warning(f"未找到企业数据: {company_id}")
                    return None

        except Exception as e:
            self.logger.error(f"获取企业数据失败: {e}")
            raise

    def get_company_data_by_name(self, company_name: str) -> Optional[Dict[str, Any]]:
        """根据企业名称获取企业数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, company_name, unified_social_credit_code,
                           legal_representative, registered_address,
                           business_scope, contact_phone, contact_email,
                           registered_capital, credit_line_number, business_number,
                           guarantee_method, loan_amount, loan_term_months,
                           guarantee_amount, pledge_value, company_short_name
                    FROM companies
                    WHERE company_name = ? OR company_name LIKE ?
                """, (company_name, f"%{company_name}%"))

                row = cursor.fetchone()
                if row:
                    company_data = {
                        'id': row[0],
                        'company_name': row[1],
                        'unified_social_credit_code': row[2],
                        'legal_representative': row[3],
                        'registered_address': row[4] or "",
                        'business_scope': row[5] or "",
                        'contact_phone': row[6] or "",
                        'contact_email': row[7] or "",
                        'registered_capital': row[8] or "",
                        'credit_line_number': row[9] or "KHED5104885002025228054",
                        'business_number': row[10] or "KHED510488500202522805",
                        'guarantee_method': row[11] or "",
                        'loan_amount': row[12] or 0,
                        'loan_term_months': row[13] or 0,
                        'guarantee_amount': row[14] or 4000,
                        'pledge_value': row[15] or 328.98,
                        'company_short_name': row[16] or self._extract_company_short_name(row[1])
                    }

                    self.logger.info(f"根据名称获取企业数据成功: {company_data['company_name']}")
                    return company_data
                else:
                    self.logger.warning(f"未找到企业数据: {company_name}")
                    return None

        except Exception as e:
            self.logger.error(f"根据名称获取企业数据失败: {e}")
            raise

    def get_company_data(self, company_identifier: str) -> Optional[Dict[str, Any]]:
        """获取企业数据（支持ID或名称）"""
        try:
            # 先尝试按ID查找
            company_data = self.get_company_data_by_id(company_identifier)

            # 如果按ID找不到，尝试按名称查找
            if not company_data:
                company_data = self.get_company_data_by_name(company_identifier)

            if company_data:
                # 添加系统生成字段
                company_data['current_date'] = datetime.now().strftime('%Y年%m月%d日')
                company_data['application_date'] = datetime.now().strftime('%Y年%m月 日')
                company_data['manager_sign_date'] = datetime.now().strftime('%Y年%m月 日')
                company_data['leader_sign_date'] = datetime.now().strftime('%Y年%m月 日')

                # 生成合同编号
                company_data['pledge_contract_number'] = self._generate_contract_number(
                    company_data['company_short_name'], '专质'
                )
                company_data['guarantee_contract_number'] = self._generate_contract_number(
                    company_data['company_short_name'], '保'
                )

                # 设置固定值
                company_data['environmental_category'] = 'ESG分类为绿色'

                self.logger.info(f"获取企业数据成功: {company_data['company_name']}")
                return company_data
            else:
                self.logger.warning(f"未找到企业数据: {company_identifier}")
                return None

        except Exception as e:
            self.logger.error(f"获取企业数据失败: {e}")
            raise

    def validate_required_fields(self, company_data: Dict[str, Any], required_fields: List[str]) -> Dict[str, Any]:
        """校验必填字段是否缺失"""
        try:
            missing_fields = []
            invalid_fields = []
            warnings = []

            # 检查必填字段
            for field_name in required_fields:
                field_info = self.field_dictionary.get(field_name, {})
                field_label = field_info.get('label', field_name)

                if field_name not in company_data or not company_data[field_name]:
                    missing_fields.append(field_label)

            # 检查字段格式
            format_validation = self.validate_field_formats(company_data)
            invalid_fields.extend(format_validation['invalid_fields'])
            warnings.extend(format_validation['warnings'])

            is_valid = len(missing_fields) == 0 and len(invalid_fields) == 0

            result = {
                'is_valid': is_valid,
                'missing_fields': missing_fields,
                'invalid_fields': invalid_fields,
                'warnings': warnings,
                'summary': f"校验{'通过' if is_valid else '未通过'}: 缺失{len(missing_fields)}个必填字段, {len(invalid_fields)}个格式错误"
            }

            self.logger.info(f"必填字段校验完成: {result['summary']}")
            return result

        except Exception as e:
            self.logger.error(f"必填字段校验失败: {e}")
            return {
                'is_valid': False,
                'missing_fields': [],
                'invalid_fields': [f"校验异常: {e}"],
                'warnings': [],
                'summary': f"校验异常: {e}"
            }

    def validate_field_formats(self, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """校验字段格式"""
        try:
            invalid_fields = []
            warnings = []

            for field_name, field_value in company_data.items():
                if not field_value:  # 跳过空值
                    continue

                field_info = self.field_dictionary.get(field_name, {})
                validation_rules = field_info.get('validation', {})

                # 统一社会信用代码格式校验
                if field_name == 'unified_social_credit_code':
                    pattern = validation_rules.get('pattern')
                    if pattern and not re.match(pattern, str(field_value)):
                        invalid_fields.append("统一社会信用代码格式不正确")
                    elif len(str(field_value)) != 18:
                        invalid_fields.append("统一社会信用代码长度应为18位")

                # 联系电话格式校验
                elif field_name == 'contact_phone':
                    pattern = validation_rules.get('pattern')
                    if pattern and not re.match(pattern, str(field_value)):
                        warnings.append("联系电话格式可能不正确")

                # 邮箱格式校验
                elif field_name == 'contact_email':
                    pattern = validation_rules.get('pattern')
                    if pattern and not re.match(pattern, str(field_value)):
                        warnings.append("邮箱格式可能不正确")

                # 长度校验
                min_length = validation_rules.get('min_length')
                max_length = validation_rules.get('max_length')
                if min_length and len(str(field_value)) < min_length:
                    invalid_fields.append(f"{field_info.get('label', field_name)}长度不能少于{min_length}字符")
                if max_length and len(str(field_value)) > max_length:
                    invalid_fields.append(f"{field_info.get('label', field_name)}长度不能超过{max_length}字符")

            return {
                'invalid_fields': invalid_fields,
                'warnings': warnings
            }

        except Exception as e:
            self.logger.error(f"字段格式校验失败: {e}")
            return {
                'invalid_fields': [f"格式校验异常: {e}"],
                'warnings': []
            }

    def format_field_value(self, field_name: str, field_value: Any) -> str:
        """根据字段配置格式化字段值"""
        try:
            if not field_value:
                return ""

            field_info = self.field_dictionary.get(field_name, {})
            field_type = field_info.get('type', 'text')
            format_rule = field_info.get('format', '')

            # 文本类型
            if field_type == 'text':
                return str(field_value).strip()

            # 金额类型
            elif field_type == 'money':
                try:
                    amount = float(field_value)
                    if 'unit:万元' in format_rule:
                        return f"{amount:.0f}万元"
                    elif 'currency:CNY' in format_rule:
                        return f"¥{amount:,.2f}"
                    else:
                        return f"{amount:.2f}"
                except (ValueError, TypeError):
                    return str(field_value)

            # 日期类型
            elif field_type == 'date':
                if isinstance(field_value, str) and field_value:
                    return field_value
                elif hasattr(field_value, 'strftime'):
                    return field_value.strftime('%Y年%m月%d日')
                else:
                    return str(field_value)

            # 百分比类型
            elif field_type == 'percent':
                try:
                    percent = float(field_value)
                    return f"{percent:.2f}%"
                except (ValueError, TypeError):
                    return str(field_value)

            # 数字类型
            elif field_type == 'number':
                try:
                    number = float(field_value)
                    if 'decimal:0' in format_rule:
                        return f"{number:.0f}"
                    else:
                        return f"{number:.2f}"
                except (ValueError, TypeError):
                    return str(field_value)

            # 默认返回字符串
            else:
                return str(field_value)

        except Exception as e:
            self.logger.warning(f"格式化字段值失败 {field_name}: {e}")
            return str(field_value)

    def get_field_info(self, field_name: str) -> Dict[str, Any]:
        """获取字段信息"""
        return self.field_dictionary.get(field_name, {})

    def get_all_field_names(self) -> List[str]:
        """获取所有字段名称"""
        return list(self.field_dictionary.keys())

    def get_required_fields(self) -> List[str]:
        """获取所有必填字段"""
        required_fields = []
        for field_name, field_info in self.field_dictionary.items():
            if field_info.get('required', False):
                required_fields.append(field_name)
        return required_fields

    def _extract_company_short_name(self, company_name: str) -> str:
        """提取公司简称（前两字）"""
        try:
            if '卓尔' in company_name:
                return '卓尔'
            elif '神光' in company_name:
                return '神光'
            else:
                # 提取公司名称中的前两个中文字符
                import re
                chinese_chars = re.findall(r'[\u4e00-\u9fff]', company_name)
                if len(chinese_chars) >= 2:
                    return ''.join(chinese_chars[:2])
                else:
                    return company_name[:2]
        except Exception as e:
            self.logger.warning(f"提取公司简称失败: {e}")
            return company_name[:2] if company_name else ""

    def _generate_contract_number(self, company_short_name: str, contract_type: str) -> str:
        """生成合同编号"""
        try:
            current_year = datetime.now().year
            # 这里可以添加序号逻辑，暂时使用001
            sequence = "001"

            if contract_type == '专质':
                return f"建八{company_short_name}专质（{current_year}）{sequence}号"
            elif contract_type == '保':
                return f"建八{company_short_name}保（{current_year}）{sequence}号"
            else:
                return f"建八{company_short_name}{contract_type}（{current_year}）{sequence}号"

        except Exception as e:
            self.logger.warning(f"生成合同编号失败: {e}")
            return f"建八{company_short_name}{contract_type}（2025）001号"
