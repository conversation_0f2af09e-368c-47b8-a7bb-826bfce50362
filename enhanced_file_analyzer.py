#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版文件分析器 - 支持新旧Office格式
"""

import os
import zipfile
from pathlib import Path
import logging
import struct

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedFileAnalyzer:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.templates_dir = self.project_root / "templates" / "contract_disbursement"
        
    def analyze_all_files(self):
        """分析所有文件"""
        logger.info("🔍 开始增强版文件分析...")
        logger.info(f"📁 目录: {self.templates_dir}")
        
        if not self.templates_dir.exists():
            logger.error(f"❌ 目录不存在: {self.templates_dir}")
            return
        
        # 获取所有文件
        files = [f for f in self.templates_dir.iterdir() if f.is_file() and not f.name.startswith('~')]
        
        print("\n" + "="*80)
        print("📋 合同支用模块文件增强分析报告")
        print("="*80)
        
        # 按类型分组
        word_files = []
        excel_files = []
        other_files = []
        
        for file_path in files:
            if file_path.suffix.lower() in ['.docx', '.doc']:
                word_files.append(file_path)
            elif file_path.suffix.lower() in ['.xlsx', '.xlsm', '.xls']:
                excel_files.append(file_path)
            else:
                other_files.append(file_path)
        
        # 分析Word文件
        if word_files:
            print(f"\n📝 Word文档 ({len(word_files)}个):")
            for file_path in word_files:
                self._analyze_word_file_enhanced(file_path)
        
        # 分析Excel文件
        if excel_files:
            print(f"\n📊 Excel文件 ({len(excel_files)}个):")
            for file_path in excel_files:
                self._analyze_excel_file_enhanced(file_path)
        
        # 其他文件
        if other_files:
            print(f"\n📄 其他文件 ({len(other_files)}个):")
            for file_path in other_files:
                self._analyze_other_file(file_path)
        
        print("\n" + "="*80)
        print("✅ 增强分析完成")
        print("="*80)
    
    def _analyze_word_file_enhanced(self, file_path):
        """增强版Word文件分析"""
        file_size = file_path.stat().st_size
        print(f"  📝 {file_path.name}")
        print(f"     格式: {file_path.suffix.upper()}")
        print(f"     大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        
        # 检查文件格式
        if file_path.suffix.lower() == '.docx':
            # 新版Word格式
            if self._is_valid_docx(file_path):
                print(f"     状态: ✅ 有效的Word 2007+文档")
                content_info = self._get_docx_content_info(file_path)
                if content_info:
                    print(f"     内容: {content_info}")
            else:
                print(f"     状态: ❌ 无效的DOCX文档")
        
        elif file_path.suffix.lower() == '.doc':
            # 老版Word格式
            doc_info = self._analyze_doc_file(file_path)
            print(f"     状态: {doc_info['status']}")
            if doc_info['valid']:
                print(f"     版本: {doc_info['version']}")
                if doc_info['content']:
                    print(f"     内容: {doc_info['content']}")
        
        print()
    
    def _analyze_excel_file_enhanced(self, file_path):
        """增强版Excel文件分析"""
        file_size = file_path.stat().st_size
        print(f"  📊 {file_path.name}")
        print(f"     格式: {file_path.suffix.upper()}")
        print(f"     大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        
        # 检查是否包含宏
        has_macro = self._check_excel_macro(file_path)
        if has_macro is not None:
            if has_macro:
                print(f"     宏代码: ✅ 包含VBA宏")
                if file_path.suffix.lower() == '.xlsx':
                    print(f"     建议: 💡 应该使用.xlsm格式")
            else:
                print(f"     宏代码: ❌ 不包含宏")
                if file_path.suffix.lower() == '.xlsm':
                    print(f"     建议: 💡 可以使用.xlsx格式")
        
        # 检查文件有效性
        if file_path.suffix.lower() in ['.xlsx', '.xlsm']:
            if self._is_valid_excel_new(file_path):
                print(f"     状态: ✅ 有效的Excel 2007+文档")
                sheet_info = self._get_excel_sheet_info(file_path)
                if sheet_info:
                    print(f"     工作表: {sheet_info}")
            else:
                print(f"     状态: ❌ 无效的Excel文档")
        elif file_path.suffix.lower() == '.xls':
            xls_info = self._analyze_xls_file(file_path)
            print(f"     状态: {xls_info['status']}")
            if xls_info['valid']:
                print(f"     版本: {xls_info['version']}")
        
        print()
    
    def _analyze_other_file(self, file_path):
        """分析其他文件"""
        file_size = file_path.stat().st_size
        print(f"  📄 {file_path.name}")
        print(f"     格式: {file_path.suffix.upper() if file_path.suffix else '无扩展名'}")
        print(f"     大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        print()
    
    def _analyze_doc_file(self, file_path):
        """分析老版Word文件(.doc)"""
        try:
            with open(file_path, 'rb') as f:
                # 读取文件头
                header = f.read(8)
                
                # 检查OLE文件签名
                if header[:8] == b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1':
                    return {
                        'valid': True,
                        'status': '✅ 有效的Word 97-2003文档',
                        'version': 'Word 97-2003 (.doc)',
                        'content': '老版Word格式，无法详细分析内容'
                    }
                else:
                    return {
                        'valid': False,
                        'status': '❌ 不是有效的Word文档',
                        'version': '未知',
                        'content': None
                    }
        except Exception as e:
            return {
                'valid': False,
                'status': f'⚠️ 分析失败: {str(e)}',
                'version': '未知',
                'content': None
            }
    
    def _analyze_xls_file(self, file_path):
        """分析老版Excel文件(.xls)"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)
                
                if header[:8] == b'\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1':
                    return {
                        'valid': True,
                        'status': '✅ 有效的Excel 97-2003文档',
                        'version': 'Excel 97-2003 (.xls)'
                    }
                else:
                    return {
                        'valid': False,
                        'status': '❌ 不是有效的Excel文档',
                        'version': '未知'
                    }
        except Exception as e:
            return {
                'valid': False,
                'status': f'⚠️ 分析失败: {str(e)}',
                'version': '未知'
            }
    
    def _is_valid_docx(self, file_path):
        """检查是否为有效的DOCX文件"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                required_files = ['[Content_Types].xml', 'word/document.xml']
                for required_file in required_files:
                    if required_file not in zip_file.namelist():
                        return False
                return True
        except:
            return False
    
    def _is_valid_excel_new(self, file_path):
        """检查是否为有效的新版Excel文件"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                required_files = ['[Content_Types].xml', 'xl/workbook.xml']
                for required_file in required_files:
                    if required_file not in zip_file.namelist():
                        return False
                return True
        except:
            return False
    
    def _check_excel_macro(self, file_path):
        """检查Excel文件是否包含宏"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                file_list = zip_file.namelist()
                
                # 检查VBA相关文件
                vba_indicators = [
                    'xl/vbaProject.bin',
                    'xl/macrosheets/',
                    'xl/dialogsheets/',
                    'xl/macros/'
                ]
                
                for indicator in vba_indicators:
                    if any(indicator in f for f in file_list):
                        return True
                
                # 检查content types
                try:
                    content_types = zip_file.read('[Content_Types].xml').decode('utf-8')
                    if 'application/vnd.ms-office.vbaProject' in content_types:
                        return True
                except:
                    pass
                
                return False
        except:
            return None
    
    def _get_docx_content_info(self, file_path):
        """获取DOCX内容信息"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                if 'word/document.xml' in zip_file.namelist():
                    content = zip_file.read('word/document.xml').decode('utf-8')
                    paragraph_count = content.count('<w:p ')
                    table_count = content.count('<w:tbl>')
                    return f"{paragraph_count}个段落, {table_count}个表格"
        except:
            pass
        return None
    
    def _get_excel_sheet_info(self, file_path):
        """获取Excel工作表信息"""
        try:
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                if 'xl/workbook.xml' in zip_file.namelist():
                    content = zip_file.read('xl/workbook.xml').decode('utf-8')
                    sheet_count = content.count('<sheet ')
                    return f"{sheet_count}个工作表"
        except:
            pass
        return None

def main():
    analyzer = EnhancedFileAnalyzer()
    analyzer.analyze_all_files()

if __name__ == "__main__":
    main()
