#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展数据库以支持客户文档管理
"""

import sqlite3
import os
from datetime import datetime

def extend_database_for_documents():
    """扩展数据库以支持文档管理功能"""
    
    db_path = "database/enterprise_service.db"
    
    print("🔄 扩展数据库以支持客户文档管理...")
    print("=" * 60)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建company_documents表
        print("📋 创建company_documents表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS company_documents (
                id TEXT PRIMARY KEY,
                company_id TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                file_type TEXT,
                mime_type TEXT,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                uploaded_by TEXT,
                document_category TEXT,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
        """)
        
        # 2. 创建文档分类表（可选）
        print("📋 创建document_categories表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS document_categories (
                id TEXT PRIMARY KEY,
                category_name TEXT NOT NULL UNIQUE,
                category_description TEXT,
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 3. 插入默认文档分类
        print("📋 插入默认文档分类...")
        default_categories = [
            ("business_license", "营业执照", "企业营业执照及相关证件", 1),
            ("financial_reports", "财务报表", "审计报告、财务报表等", 2),
            ("legal_documents", "法律文件", "合同、协议等法律文件", 3),
            ("tax_documents", "税务资料", "税务登记证、纳税证明等", 4),
            ("bank_statements", "银行流水", "银行对账单、流水记录", 5),
            ("other_documents", "其他资料", "其他相关文档", 99)
        ]
        
        for cat_id, name, desc, order in default_categories:
            cursor.execute("""
                INSERT OR IGNORE INTO document_categories 
                (id, category_name, category_description, sort_order)
                VALUES (?, ?, ?, ?)
            """, (cat_id, name, desc, order))
        
        # 4. 创建索引以提高查询性能
        print("📋 创建数据库索引...")
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_documents_company_id 
            ON company_documents(company_id)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_documents_upload_date 
            ON company_documents(upload_date)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_company_documents_category 
            ON company_documents(document_category)
        """)
        
        # 提交事务
        conn.commit()
        
        print("✅ 数据库扩展完成")
        
        # 验证表结构
        print("\n🔍 验证表结构...")
        
        # 检查company_documents表
        cursor.execute("PRAGMA table_info(company_documents)")
        columns = cursor.fetchall()
        print(f"✅ company_documents表包含 {len(columns)} 个字段:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # 检查文档分类
        cursor.execute("SELECT COUNT(*) FROM document_categories")
        cat_count = cursor.fetchone()[0]
        print(f"✅ 默认文档分类: {cat_count} 个")
        
        conn.close()
        
        # 创建文件存储目录
        create_upload_directories()
        
        print("\n🎉 数据库扩展成功!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库扩展失败: {e}")
        return False

def create_upload_directories():
    """创建文件上传目录结构"""
    
    print("\n📁 创建文件存储目录...")
    
    # 主上传目录
    upload_dir = "uploads"
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
        print(f"✅ 创建目录: {upload_dir}")
    
    # 按公司ID分类的子目录将在上传时动态创建
    # 临时文件目录
    temp_dir = os.path.join(upload_dir, "temp")
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
        print(f"✅ 创建目录: {temp_dir}")
    
    # 创建.gitignore文件保护上传文件
    gitignore_path = os.path.join(upload_dir, ".gitignore")
    if not os.path.exists(gitignore_path):
        with open(gitignore_path, "w", encoding="utf-8") as f:
            f.write("# 忽略所有上传的文件\n")
            f.write("*\n")
            f.write("# 但保留.gitignore文件\n")
            f.write("!.gitignore\n")
        print(f"✅ 创建文件: {gitignore_path}")

def verify_document_system():
    """验证文档系统是否正常工作"""
    
    print("\n🔍 验证文档系统...")
    
    try:
        # 检查数据库表
        conn = sqlite3.connect("database/enterprise_service.db")
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('company_documents', 'document_categories')
        """)
        tables = cursor.fetchall()
        
        if len(tables) == 2:
            print("✅ 数据库表创建成功")
        else:
            print("❌ 数据库表创建失败")
            return False
        
        # 检查文档分类
        cursor.execute("SELECT category_name FROM document_categories ORDER BY sort_order")
        categories = cursor.fetchall()
        print(f"✅ 文档分类: {[cat[0] for cat in categories]}")
        
        conn.close()
        
        # 检查目录
        if os.path.exists("uploads"):
            print("✅ 文件存储目录创建成功")
        else:
            print("❌ 文件存储目录创建失败")
            return False
        
        print("🎉 文档系统验证通过!")
        return True
        
    except Exception as e:
        print(f"❌ 文档系统验证失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🏢 企业服务系统 - 文档管理功能扩展")
    print("=" * 60)
    
    if extend_database_for_documents():
        if verify_document_system():
            print("\n" + "=" * 60)
            print("🎉 文档管理功能扩展完成!")
            print("✅ 数据库表结构已扩展")
            print("✅ 文件存储目录已创建")
            print("✅ 系统已准备好支持文档上传和管理")
            print("=" * 60)
        else:
            print("\n❌ 系统验证失败!")
    else:
        print("\n❌ 数据库扩展失败!")
