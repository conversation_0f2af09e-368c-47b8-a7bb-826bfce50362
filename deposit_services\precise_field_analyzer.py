#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确字段分析器
专门分析协定存款协议中需要从数据库填充的字段
"""

import docx
from pathlib import Path
import json
from datetime import datetime

class PreciseFieldAnalyzer:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "templates" / "deposit_services"
        self.output_dir = self.project_root / "test_output"
        
    def analyze_deposit_agreement(self):
        """分析协定存款协议中需要填充的字段"""
        template_file = "中国建设银行人民币单位协定存款合同（标准文本）1.docx"
        template_path = self.templates_dir / template_file
        
        print("🔍 精确分析协定存款协议需要填充的字段")
        print("="*60)
        
        if not template_path.exists():
            print(f"❌ 模板文件不存在: {template_path}")
            return None
        
        try:
            doc = docx.Document(template_path)
            full_text = self._extract_full_text(doc)
            
            # 需要从数据库填充的字段
            database_fields = self._identify_database_fields(full_text)
            
            # 显示分析结果
            self._display_database_fields(database_fields)
            
            # 保存结果
            self._save_analysis_result(database_fields)
            
            return database_fields
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return None
    
    def _extract_full_text(self, doc):
        """提取文档全文"""
        full_text = ""
        for para in doc.paragraphs:
            full_text += para.text + "\n"
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    full_text += cell.text + " "
                full_text += "\n"
        return full_text
    
    def _identify_database_fields(self, full_text):
        """识别需要从数据库填充的字段"""
        database_fields = {
            'company_info': [],      # 企业信息
            'account_info': [],      # 账户信息  
            'financial_info': [],    # 金融信息
            'date_info': [],         # 日期信息
            'contract_info': []      # 合同信息
        }
        
        print("\n📋 识别需要从数据库填充的字段:")
        print("-" * 50)
        
        # 1. 企业信息字段
        company_fields = [
            {
                'field_name': '甲方企业名称',
                'placeholder': '待补充',
                'database_source': 'companies.company_name',
                'description': '协议甲方企业全称',
                'example': '成都中科卓尔智能科技集团有限公司'
            }
        ]
        
        # 2. 账户信息字段
        account_fields = [
            {
                'field_name': '协定存款账户号',
                'placeholder': '在乙方开立协定存款账户的账号：待补充',
                'database_source': '需要新生成或从账户表获取',
                'description': '协定存款专用账户号',
                'example': '51001234567890123456'
            },
            {
                'field_name': '活期存款账户号',
                'placeholder': '活期存款账户（账号：待补充_）',
                'database_source': '需要从账户表获取或新开立',
                'description': '基本存款账户号',
                'example': '51001234567890123457'
            }
        ]
        
        # 3. 金融信息字段
        financial_fields = [
            {
                'field_name': '基本存款额度',
                'placeholder': '（大写）            万',
                'database_source': '需要根据企业规模设定',
                'description': '协定存款基本额度',
                'example': '壹仟万元整'
            },
            {
                'field_name': '利率调整幅度',
                'placeholder': '加（选填"加"或"减"）25 bps',
                'database_source': '根据企业等级和政策设定',
                'description': '在基准利率基础上的调整',
                'example': '加25 bps'
            }
        ]
        
        # 4. 日期信息字段
        date_fields = [
            {
                'field_name': '合同签署日期',
                'placeholder': '年    月    日',
                'database_source': '系统当前日期',
                'description': '协议签署日期',
                'example': '2025年7月31日'
            }
        ]
        
        # 5. 合同信息字段
        contract_fields = [
            {
                'field_name': '通知期限',
                'placeholder': '通知后【】个工作日内',
                'database_source': '银行政策配置',
                'description': '利率调整通知期限',
                'example': '5个工作日'
            }
        ]
        
        # 组装结果
        database_fields['company_info'] = company_fields
        database_fields['account_info'] = account_fields
        database_fields['financial_info'] = financial_fields
        database_fields['date_info'] = date_fields
        database_fields['contract_info'] = contract_fields
        
        return database_fields
    
    def _display_database_fields(self, database_fields):
        """显示需要填充的字段"""
        total_fields = sum(len(fields) for fields in database_fields.values())
        
        print(f"\n📊 需要从数据库填充的字段统计:")
        print("-" * 50)
        print(f"   📝 总字段数: {total_fields}")
        print(f"   🏢 企业信息: {len(database_fields['company_info'])}个")
        print(f"   🏦 账户信息: {len(database_fields['account_info'])}个")
        print(f"   💰 金融信息: {len(database_fields['financial_info'])}个")
        print(f"   📅 日期信息: {len(database_fields['date_info'])}个")
        print(f"   📋 合同信息: {len(database_fields['contract_info'])}个")
        
        # 详细显示每个字段
        for category, fields in database_fields.items():
            if fields:
                category_names = {
                    'company_info': '🏢 企业信息字段',
                    'account_info': '🏦 账户信息字段',
                    'financial_info': '💰 金融信息字段',
                    'date_info': '📅 日期信息字段',
                    'contract_info': '📋 合同信息字段'
                }
                
                print(f"\n{category_names[category]}:")
                print("-" * 40)
                
                for i, field in enumerate(fields, 1):
                    print(f"   {i}. {field['field_name']}")
                    print(f"      📍 占位符: {field['placeholder']}")
                    print(f"      🗄️ 数据源: {field['database_source']}")
                    print(f"      📝 说明: {field['description']}")
                    print(f"      💡 示例: {field['example']}")
                    print()
    
    def _save_analysis_result(self, database_fields):
        """保存分析结果"""
        self.output_dir.mkdir(exist_ok=True)
        
        result = {
            'analysis_time': datetime.now().isoformat(),
            'template_name': '中国建设银行人民币单位协定存款合同（标准文本）1.docx',
            'database_fields': database_fields,
            'total_fields': sum(len(fields) for fields in database_fields.values())
        }
        
        output_file = self.output_dir / "deposit_agreement_database_fields.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存: {output_file}")

def main():
    """主函数"""
    analyzer = PreciseFieldAnalyzer()
    
    print("🏦 协定存款协议精确字段分析器")
    print("="*50)
    
    result = analyzer.analyze_deposit_agreement()
    
    if result:
        print(f"\n✅ 分析完成！")
        print(f"📋 识别出需要从数据库填充的关键字段")
        print(f"🎯 下一步：创建协议生成器，实现自动填充")
    else:
        print(f"\n❌ 分析失败！")

if __name__ == "__main__":
    main()
