#!/usr/bin/env python3
"""
最终正确替换 - 贷款条件也做成真实表格
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
import sqlite3

def final_correct_replacement():
    print('=== 最终正确替换 - 所有表格都是真实表格 ===\n')
    
    # 文件路径
    template_file = Path('templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx')
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    # 1. 提取源数据
    print('📖 提取源数据...')
    precondition_text = extract_precondition(quota_file)
    continuous_table_data = extract_continuous_table_data(quota_file)
    loan_table_data = extract_loan_table_data(business_file)  # 提取表格数据而非文本
    
    # 2. 获取企业数据
    company_data = get_company_data()
    
    # 3. 加载模板
    doc = docx.Document(template_file)
    
    # 4. 进行完全正确的替换
    print('🔄 进行完全正确的替换...')
    
    # 替换用信前提条件（文本）
    replace_precondition_text(doc, precondition_text)
    
    # 插入持续条件表格（真实表格）
    insert_continuous_table(doc, continuous_table_data)
    
    # 插入贷款条件表格（真实表格）- 这是关键修正
    insert_loan_conditions_table(doc, loan_table_data)
    
    # 替换基础信息
    replace_basic_info(doc, company_data)
    
    # 5. 保存文件
    from datetime import datetime
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(f'test_output/落实情况表_完全正确版_{timestamp}.docx')
    doc.save(output_path)
    
    print(f'✅ 完全正确版生成完成！')
    print(f'📁 输出文件: {output_path}')
    print(f'📊 三个部分都是正确格式：')
    print(f'  1. 用信前提条件: 文本格式 ✅')
    print(f'  2. 持续条件: 13行x4列表格 ✅')
    print(f'  3. 贷款条件: 5行x2列表格 ✅')
    
    return output_path

def extract_precondition(quota_file):
    """提取用信前提条件"""
    doc = docx.Document(quota_file)
    table = doc.tables[4]
    text = table.rows[1].cells[1].text.strip()
    print(f'  ✅ 用信前提条件: {len(text)}字符')
    return text

def extract_continuous_table_data(quota_file):
    """提取持续条件完整表格数据"""
    doc = docx.Document(quota_file)
    table = doc.tables[5]
    
    table_data = []
    for row in table.rows:
        row_data = []
        for cell in row.cells:
            row_data.append(cell.text.strip())
        table_data.append(row_data)
    
    print(f'  ✅ 持续条件表格: {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列')
    return table_data

def extract_loan_table_data(business_file):
    """提取贷款条件完整表格数据"""
    doc = docx.Document(business_file)
    table = doc.tables[6]
    
    table_data = []
    for row in table.rows:
        row_data = []
        for cell in row.cells:
            row_data.append(cell.text.strip())
        table_data.append(row_data)
    
    print(f'  ✅ 贷款条件表格: {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列')
    return table_data

def get_company_data():
    """获取企业数据"""
    try:
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT company_name, legal_representative, spouse_name
            FROM companies 
            WHERE company_name LIKE '%中科卓尔%'
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'company_name': result[0],
                'legal_representative': result[1],
                'spouse_name': result[2]
            }
        return None
    except Exception as e:
        print(f'  ❌ 数据库查询失败: {e}')
        return None

def replace_precondition_text(doc, precondition_text):
    """替换用信前提条件为文本"""
    print('  🔄 替换用信前提条件（文本格式）...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    cell_text = target_cell.text
    if '额度申报书单户综合融资总量使用前提条件直接提取后替换这里' in cell_text:
        new_text = cell_text.replace(
            '额度申报书单户综合融资总量使用前提条件直接提取后替换这里（把我这句话删掉）',
            precondition_text
        )
        
        # 清空并重新填入
        target_cell._element.clear_content()
        
        # 添加用信前提条件标题
        title_para = target_cell.add_paragraph()
        title_run = title_para.add_run('单户综合融资总量方案申报书中列明的用信前提条件及落实情况\n\n')
        
        # 添加用信前提条件内容
        content_para = target_cell.add_paragraph()
        content_run = content_para.add_run(precondition_text)
        content_run.font.color.rgb = RGBColor(255, 0, 0)
        
        print('    ✅ 用信前提条件替换成功')

def insert_continuous_table(doc, table_data):
    """插入持续条件表格"""
    print('  🔄 插入持续条件表格（13行x4列）...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    # 添加持续条件标题
    title_para = target_cell.add_paragraph()
    title_run = title_para.add_run('\n\n二、单户综合融资总量方案申报书中列明的持续条件及落实情况\n\n')
    
    # 插入持续条件表格
    if table_data and len(table_data) > 0:
        continuous_table = target_cell.add_table(rows=len(table_data), cols=len(table_data[0]))
        continuous_table.style = 'Table Grid'
        
        # 填入数据
        for row_idx, row_data in enumerate(table_data):
            for col_idx, cell_content in enumerate(row_data):
                if col_idx < len(continuous_table.rows[row_idx].cells):
                    cell = continuous_table.rows[row_idx].cells[col_idx]
                    paragraph = cell.paragraphs[0]
                    run = paragraph.add_run(cell_content)
                    
                    # 表头不标红，数据标红
                    if row_idx > 0:
                        run.font.color.rgb = RGBColor(255, 0, 0)
                    else:
                        run.font.color.rgb = RGBColor(0, 0, 0)
    
    print('    ✅ 持续条件表格插入成功')

def insert_loan_conditions_table(doc, loan_table_data):
    """插入贷款条件表格 - 关键修正"""
    print('  🔄 插入贷款条件表格（5行x2列）...')
    
    target_table = doc.tables[0]
    target_cell = target_table.rows[8].cells[0]
    
    # 添加贷款条件标题
    title_para = target_cell.add_paragraph()
    title_run = title_para.add_run('\n\n三、单笔业务申报书中列明的贷款条件及落实情况\n\n')
    
    # 插入贷款条件表格
    if loan_table_data and len(loan_table_data) > 0:
        loan_table = target_cell.add_table(rows=len(loan_table_data), cols=len(loan_table_data[0]))
        loan_table.style = 'Table Grid'
        
        # 填入数据
        for row_idx, row_data in enumerate(loan_table_data):
            for col_idx, cell_content in enumerate(row_data):
                if col_idx < len(loan_table.rows[row_idx].cells):
                    cell = loan_table.rows[row_idx].cells[col_idx]
                    paragraph = cell.paragraphs[0]
                    run = paragraph.add_run(cell_content)
                    run.font.color.rgb = RGBColor(255, 0, 0)  # 所有内容标红
    
    print('    ✅ 贷款条件表格插入成功')

def replace_basic_info(doc, company_data):
    """替换基础信息"""
    if not company_data:
        return
    
    print('  🔄 替换基础信息...')
    
    replacements = {
        '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
        '杨伟': company_data['legal_representative'],
        '王斯颖': company_data['spouse_name']
    }
    
    target_table = doc.tables[0]
    for row in target_table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                for old_text, new_text in replacements.items():
                    if old_text in paragraph.text and old_text != new_text:
                        paragraph.text = paragraph.text.replace(old_text, new_text)

if __name__ == "__main__":
    final_correct_replacement()
