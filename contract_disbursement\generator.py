#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合同放款模块 - 正式生成器
提供标准化接口供前端调用，支持多种文档类型生成
"""

import logging
import sqlite3
import docx
from docx.enum.text import WD_COLOR_INDEX
from docx.shared import Pt
from pathlib import Path
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContractDisbursementGenerator:
    """合同放款模块生成器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "templates" / "contract_disbursement"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        
        # 确保输出目录存在
        self.output_dir.mkdir(exist_ok=True)
        
        # 颜色定义
        self.yellow_color = WD_COLOR_INDEX.YELLOW  # 黄色 - 替换内容标记
        self.green_color = WD_COLOR_INDEX.BRIGHT_GREEN  # 绿色 - 新增内容标记
        
        # 模板映射
        self.template_mapping = {
            'condition_checklist': 'disbursement_condition_checklist_blueprint.docx',
            'contract_info': 'disbursement_contract_info_blueprint.xlsm',
            'review_ledger': 'disbursement_review_ledger_blueprint.xlsm',
            'drawdown_application': 'disbursement_drawdown_application_blueprint.docx',
            'scale_forecast': 'disbursement_scale_forecast_blueprint.xlsm'
        }
    
    def generate_contract_disbursement(self, company_id, document_type='condition_checklist', **kwargs):
        """
        生成合同放款文档 - 主要接口
        
        Args:
            company_id: 企业ID
            document_type: 文档类型 ('condition_checklist', 'contract_info', 'review_ledger', 
                          'drawdown_application', 'scale_forecast')
            **kwargs: 其他参数（如loan_amount等）
        
        Returns:
            tuple: (output_path, summary)
        """
        logger.info(f"🏦 开始生成合同放款文档，类型: {document_type}, 企业ID: {company_id}")
        
        if document_type not in self.template_mapping:
            raise ValueError(f"不支持的文档类型: {document_type}")
        
        # 根据文档类型调用相应的生成方法
        if document_type == 'condition_checklist':
            return self._generate_condition_checklist(company_id, kwargs.get('loan_amount'))
        elif document_type == 'contract_info':
            return self._generate_contract_info(company_id, kwargs)
        elif document_type == 'review_ledger':
            return self._generate_review_ledger(company_id, kwargs)
        elif document_type == 'drawdown_application':
            return self._generate_drawdown_application(company_id, kwargs)
        elif document_type == 'scale_forecast':
            return self._generate_scale_forecast(company_id, kwargs)
        else:
            raise ValueError(f"未实现的文档类型: {document_type}")
    
    def _generate_condition_checklist(self, company_id, loan_amount=None):
        """生成条件落实情况表"""
        logger.info(f"📋 生成条件落实情况表")
        
        # 1. 获取企业数据
        company_data = self._get_company_data(company_id)
        if not company_data:
            raise ValueError(f"未找到企业信息: {company_id}")
        
        # 2. 加载模板
        template_path = self.templates_dir / self.template_mapping['condition_checklist']
        if not template_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
        
        doc = docx.Document(template_path)
        
        # 3. 准备替换数据
        fill_data = self._prepare_replacement_data(company_data, loan_amount)
        
        # 4. 执行字段替换
        replacement_count = self._execute_field_replacements(doc, fill_data)
        
        # 5. 保存文件
        output_filename = f"条件落实情况表_{company_data['company_name']}.docx"
        output_path = self.output_dir / output_filename
        doc.save(output_path)
        
        logger.info(f"✅ 条件落实情况表生成完成: {output_path}")
        
        return output_path, {
            'company_name': company_data['company_name'],
            'loan_amount': loan_amount or '待填写',
            'total_replacements': replacement_count,
            'template_type': 'condition_checklist',
            'output_path': str(output_path)
        }
    
    def _generate_contract_info(self, company_id, params):
        """生成合同信息表（Excel）"""
        logger.info(f"📊 生成合同信息表")
        # TODO: 实现Excel文件处理逻辑
        raise NotImplementedError("合同信息表生成功能待实现")
    
    def _generate_review_ledger(self, company_id, params):
        """生成审查台账（Excel）"""
        logger.info(f"📊 生成审查台账")
        # TODO: 实现Excel文件处理逻辑
        raise NotImplementedError("审查台账生成功能待实现")
    
    def _generate_drawdown_application(self, company_id, params):
        """生成提款申请书（Word）"""
        logger.info(f"📄 生成提款申请书")
        # TODO: 实现Word文件处理逻辑
        raise NotImplementedError("提款申请书生成功能待实现")
    
    def _generate_scale_forecast(self, company_id, params):
        """生成规模预测表（Excel）"""
        logger.info(f"📈 生成规模预测表")
        # TODO: 实现Excel文件处理逻辑
        raise NotImplementedError("规模预测表生成功能待实现")
    
    def _get_company_data(self, company_id):
        """获取企业数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, unified_social_credit_code, legal_representative,
                       registered_address, contact_phone, contact_email
                FROM companies 
                WHERE id = ?
            """, (company_id,))
            
            result = cursor.fetchone()
            if not result:
                return None
            
            return {
                'company_name': result[0],
                'unified_social_credit_code': result[1],
                'legal_representative': result[2],
                'registered_address': result[3],
                'contact_phone': result[4],
                'contact_email': result[5]
            }
        except Exception as e:
            logger.error(f"获取企业数据失败: {e}")
            return None
        finally:
            if 'conn' in locals():
                conn.close()
    
    def _prepare_replacement_data(self, company_data, loan_amount):
        """准备替换数据"""
        current_date = datetime.now()
        
        # 生成业务编号
        business_number = f"KHED{company_data['unified_social_credit_code'][-6:]}{current_date.strftime('%Y%m%d')}"
        
        replacements = {
            # 基础企业信息
            '成都中科卓尔智能科技集团有限公司': company_data['company_name'],
            '杨伟': company_data['legal_representative'],
            '91510100MA6CGUGA1W': company_data['unified_social_credit_code'],
            
            # 业务信息
            'PIFU5100000002025N00G8': business_number,
            'C类': 'ESG绿色',
            '2025年3月': current_date.strftime('%Y年%m月'),
            
            # 金额信息
            '        万元': f'{loan_amount}万元' if loan_amount else '        万元',
        }
        
        return replacements
    
    def _execute_field_replacements(self, doc, fill_data):
        """执行字段替换"""
        replacement_count = 0
        
        for old_text, new_text in fill_data.items():
            if new_text and old_text != new_text:
                count = self._replace_text_in_document(doc, old_text, new_text)
                if count > 0:
                    replacement_count += count
                    logger.info(f"   ✅ 替换: {old_text[:15]}... → {new_text[:15]}... ({count}处)")
        
        return replacement_count
    
    def _replace_text_in_document(self, doc, old_text, new_text):
        """在文档中替换文本"""
        replacement_count = 0
        
        # 遍历段落
        for paragraph in doc.paragraphs:
            if old_text in paragraph.text:
                replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)
        
        # 遍历表格
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        if old_text in paragraph.text:
                            replacement_count += self._replace_text_in_paragraph(paragraph, old_text, new_text)
        
        return replacement_count
    
    def _replace_text_in_paragraph(self, paragraph, old_text, new_text):
        """在段落中替换文本"""
        if old_text not in paragraph.text:
            return 0
        
        for run in paragraph.runs:
            if old_text in run.text:
                # 保存原有格式
                original_format = {
                    'bold': run.font.bold,
                    'italic': run.font.italic,
                    'underline': run.font.underline
                }
                
                # 替换文本并标记
                run.text = run.text.replace(old_text, new_text)
                run.font.highlight_color = self.yellow_color
                
                # 设置字体
                try:
                    run.font.name = '宋体'
                    run.font.size = Pt(14)
                    if original_format['bold'] is not None:
                        run.font.bold = original_format['bold']
                    if original_format['italic'] is not None:
                        run.font.italic = original_format['italic']
                    if original_format['underline'] is not None:
                        run.font.underline = original_format['underline']
                except Exception as e:
                    logger.warning(f"字体格式设置失败: {e}")
                
                return 1
        
        return 0


def main():
    """测试主接口"""
    print("🏦 合同放款模块生成器测试")
    print("="*50)
    
    generator = ContractDisbursementGenerator()
    company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    
    try:
        # 测试条件落实情况表生成
        output_path, summary = generator.generate_contract_disbursement(
            company_id=company_id,
            document_type='condition_checklist',
            loan_amount=1500
        )
        
        print(f"✅ 生成成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"💰 放款金额: {summary['loan_amount']}")
        print(f"📊 替换字段: {summary['total_replacements']}个")
        print(f"🎯 模板类型: {summary['template_type']}")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
