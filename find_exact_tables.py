#!/usr/bin/env python3
"""
精确找到您截图中的三个表格
"""

import docx
from pathlib import Path

def find_exact_tables():
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    print('=== 寻找您截图中的精确表格 ===\n')
    
    # 1. 寻找"单户综合融资总量用信持续条件"表格
    print('🎯 寻找"单户综合融资总量用信持续条件"表格：')
    find_continuous_conditions_table(quota_file)
    
    print('\n' + '='*80 + '\n')
    
    # 2. 寻找包含"贸易背景真实性"的贷款条件表格
    print('🎯 寻找包含"贸易背景真实性"的贷款条件表格：')
    find_loan_conditions_table(business_file)

def find_continuous_conditions_table(file_path):
    """寻找持续条件表格"""
    if not file_path.exists():
        print('额度申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    
    for table_idx, table in enumerate(doc.tables):
        # 检查表格是否包含"持续条件"相关内容
        table_text = ""
        for row in table.rows:
            for cell in row.cells:
                table_text += cell.text.strip() + " "
        
        if "持续条件" in table_text and ("持续的资本水平" in table_text or "持续的流动性水平" in table_text):
            print(f'✅ 找到表格{table_idx+1}！这是持续条件表格')
            print(f'表格{table_idx+1}完整内容：')
            
            for row_idx, row in enumerate(table.rows):
                print(f'\n  行{row_idx+1}:')
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f'    列{cell_idx+1}: {cell_text}')
            
            print('\n' + '-'*60)
            return table_idx + 1
    
    print('❌ 未找到持续条件表格')

def find_loan_conditions_table(file_path):
    """寻找贷款条件表格"""
    if not file_path.exists():
        print('业务申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    
    for table_idx, table in enumerate(doc.tables):
        # 检查表格是否包含"贸易背景真实性"
        table_text = ""
        for row in table.rows:
            for cell in row.cells:
                table_text += cell.text.strip() + " "
        
        if "贸易背景真实性" in table_text and "担保专营条件" in table_text:
            print(f'✅ 找到表格{table_idx+1}！这是贷款条件表格')
            print(f'表格{table_idx+1}完整内容：')
            
            for row_idx, row in enumerate(table.rows):
                print(f'\n  行{row_idx+1}:')
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        print(f'    列{cell_idx+1}: {cell_text}')
            
            print('\n' + '-'*60)
            return table_idx + 1
    
    print('❌ 未找到贷款条件表格')

if __name__ == "__main__":
    find_exact_tables()
