#!/usr/bin/env python3
"""
修复数据库表结构 - 逐个添加字段
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.database import db_manager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_column_if_not_exists(table_name, column_name, column_type):
    """安全地添加字段"""
    try:
        # 检查字段是否存在
        check_query = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
        """
        
        result = db_manager.execute_query(check_query, (table_name, column_name))
        
        if not result:
            # 字段不存在，添加它
            alter_query = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
            logger.info(f"添加字段: {table_name}.{column_name}")

            # 使用execute_ddl来执行DDL语句
            success = db_manager.execute_ddl(alter_query)
            if success:
                logger.info(f"✅ 字段 {column_name} 添加成功")
            else:
                logger.error(f"❌ 字段 {column_name} 添加失败")
        else:
            logger.info(f"⏭️ 字段 {column_name} 已存在，跳过")
            
    except Exception as e:
        logger.error(f"❌ 添加字段 {column_name} 失败: {e}")

def fix_database():
    """修复数据库表结构"""
    try:
        logger.info("开始修复数据库表结构...")
        
        # 要添加的字段列表
        fields_to_add = [
            ('registered_address', 'TEXT'),
            ('communication_address', 'TEXT'),
            ('business_description', 'TEXT'),
            ('legal_representative', 'VARCHAR(100)'),
            ('registration_date', 'DATE'),
            ('registered_capital', 'DECIMAL(15,2)'),
            ('business_scope', 'TEXT'),
            ('contact_phone', 'VARCHAR(50)'),
            ('contact_email', 'VARCHAR(100)'),
            ('website', 'VARCHAR(200)'),
            ('industry_category', 'VARCHAR(100)'),
            ('company_type', 'VARCHAR(50)'),
            ('business_status', "VARCHAR(20) DEFAULT 'active'"),
            ('created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
            ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        ]
        
        # 为companies表添加字段
        logger.info("为companies表添加字段...")
        for field_name, field_type in fields_to_add:
            add_column_if_not_exists('companies', field_name, field_type)
        
        # 为companies_history表添加字段（除了默认值）
        logger.info("为companies_history表添加字段...")
        history_fields = [
            ('registered_address', 'TEXT'),
            ('communication_address', 'TEXT'),
            ('business_description', 'TEXT'),
            ('legal_representative', 'VARCHAR(100)'),
            ('registration_date', 'DATE'),
            ('registered_capital', 'DECIMAL(15,2)'),
            ('business_scope', 'TEXT'),
            ('contact_phone', 'VARCHAR(50)'),
            ('contact_email', 'VARCHAR(100)'),
            ('website', 'VARCHAR(200)'),
            ('industry_category', 'VARCHAR(100)'),
            ('company_type', 'VARCHAR(50)'),
            ('business_status', 'VARCHAR(20)')
        ]
        
        for field_name, field_type in history_fields:
            add_column_if_not_exists('companies_history', field_name, field_type)
        
        logger.info("数据库表结构修复完成！")
        
        # 更新现有数据
        update_existing_data()
        
    except Exception as e:
        logger.error(f"数据库修复失败: {e}")
        raise

def update_existing_data():
    """更新现有数据"""
    try:
        logger.info("开始更新现有数据...")
        
        # 更新成都卫讯科技有限公司
        update_weixun = """
        UPDATE companies 
        SET 
            registered_address = %s,
            communication_address = %s,
            business_description = %s,
            legal_representative = %s,
            registration_date = %s,
            registered_capital = %s,
            business_scope = %s,
            contact_phone = %s,
            contact_email = %s,
            industry_category = %s,
            company_type = %s,
            business_status = %s,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """
        
        weixun_data = (
            '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
            '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
            '通信设备制造、软件开发、技术服务',
            '万明刚',
            '2020-03-15',
            1000000.00,
            '通信设备制造；软件开发；技术服务；计算机系统服务；数据处理和存储服务',
            '028-85123456',
            '<EMAIL>',
            '制造业',
            '有限责任公司',
            'active',
            '34af7659-d69a-4c05-a697-6ae6eb00aad3'
        )
        
        # 使用新的execute_ddl方法执行UPDATE
        success1 = db_manager.execute_ddl(update_weixun, weixun_data)
        if success1:
            logger.info("✅ 成都卫讯科技有限公司数据更新成功")
        else:
            logger.error("❌ 成都卫讯科技有限公司数据更新失败")

        # 更新成都中科卓尔智能科技集团有限公司
        update_zkzr = """
        UPDATE companies
        SET
            registered_address = %s,
            communication_address = %s,
            business_description = %s,
            legal_representative = %s,
            registration_date = %s,
            registered_capital = %s,
            business_scope = %s,
            contact_phone = %s,
            contact_email = %s,
            industry_category = %s,
            company_type = %s,
            business_status = %s,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        zkzr_data = (
            '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
            '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
            '智能科技研发、软件开发、技术咨询服务',
            '杨伟',
            '2019-08-20',
            5000000.00,
            '智能科技研发；软件开发；技术咨询；人工智能技术服务；大数据服务',
            '028-86789012',
            '<EMAIL>',
            '科技服务业',
            '有限责任公司',
            'active',
            'a1b2c3d4-e5f6-7890-1234-567890abcdef'
        )

        success2 = db_manager.execute_ddl(update_zkzr, zkzr_data)
        if success2:
            logger.info("✅ 成都中科卓尔智能科技集团有限公司数据更新成功")
        else:
            logger.error("❌ 成都中科卓尔智能科技集团有限公司数据更新失败")
        
        logger.info("现有数据更新完成！")
        
    except Exception as e:
        logger.error(f"更新现有数据失败: {e}")
        raise

def verify_fix():
    """验证修复结果"""
    try:
        logger.info("验证修复结果...")
        
        # 查询更新后的数据
        query = """
        SELECT 
            company_name,
            registered_address,
            communication_address,
            business_description,
            legal_representative
        FROM companies 
        WHERE id IN (
            '34af7659-d69a-4c05-a697-6ae6eb00aad3',
            'a1b2c3d4-e5f6-7890-1234-567890abcdef'
        )
        """
        
        results = db_manager.execute_query(query)
        
        logger.info("修复后的公司信息:")
        for row in results:
            company_data = dict(row)
            logger.info(f"公司: {company_data.get('company_name', 'N/A')}")
            logger.info(f"  注册地址: {company_data.get('registered_address', 'N/A')}")
            logger.info(f"  通讯地址: {company_data.get('communication_address', 'N/A')}")
            logger.info(f"  业务描述: {company_data.get('business_description', 'N/A')}")
            logger.info(f"  法定代表人: {company_data.get('legal_representative', 'N/A')}")
            logger.info("-" * 50)
        
        logger.info("验证完成！")
        
    except Exception as e:
        logger.error(f"验证失败: {e}")

def main():
    """主函数"""
    try:
        fix_database()
        verify_fix()
        logger.info("🎉 数据库修复成功完成！")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库修复失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
