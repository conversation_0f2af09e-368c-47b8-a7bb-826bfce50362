-- =====================================================
-- 合同支用模块数据库扩展脚本
-- 创建日期: 2025-08-04
-- 描述: 为合同支用业务创建相关表结构和数据
-- =====================================================

-- 1. 创建合同信息表
CREATE TABLE IF NOT EXISTS contracts (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    contract_number VARCHAR(100) NOT NULL,     -- 合同编号 (如: 建八卓尔（2025）001号)
    contract_type VARCHAR(50),                 -- 合同类型 (如: 流动资金贷款)
    contract_amount DECIMAL(15,2),             -- 合同金额
    contract_term INTEGER,                     -- 合同期限(月)
    contract_date DATE,                        -- 合同签订日期
    maturity_date DATE,                        -- 到期日期
    contract_status VARCHAR(20) DEFAULT 'active', -- 合同状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 创建放款信息表
CREATE TABLE IF NOT EXISTS disbursements (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    contract_id TEXT REFERENCES contracts(id),
    disbursement_amount DECIMAL(15,2),         -- 本次支用金额
    disbursement_date DATE,                    -- 放款日期
    account_number VARCHAR(50),                -- 放款账户
    loan_purpose TEXT,                         -- 贷款用途
    disbursement_status VARCHAR(20) DEFAULT 'pending', -- 放款状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 创建审批信息表
CREATE TABLE IF NOT EXISTS approvals (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    approval_number VARCHAR(100),              -- 审批文号
    approval_type VARCHAR(50),                 -- 审批类型 (额度/业务)
    approved_amount DECIMAL(15,2),             -- 批复金额
    approval_date DATE,                        -- 审批日期
    validity_start DATE,                       -- 有效期开始
    validity_end DATE,                         -- 有效期结束
    approval_status VARCHAR(20) DEFAULT 'active', -- 审批状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 扩展企业表 - 添加新字段
ALTER TABLE companies ADD COLUMN total_assets DECIMAL(15,2);              -- 总资产
ALTER TABLE companies ADD COLUMN total_liabilities DECIMAL(15,2);         -- 总负债
ALTER TABLE companies ADD COLUMN main_business TEXT;                      -- 主营业务描述
ALTER TABLE companies ADD COLUMN business_license_number VARCHAR(50);     -- 营业执照号
ALTER TABLE companies ADD COLUMN primary_account VARCHAR(50);             -- 主要账户
ALTER TABLE companies ADD COLUMN account_manager VARCHAR(100);            -- 客户经理
ALTER TABLE companies ADD COLUMN account_manager_phone VARCHAR(20);       -- 客户经理电话
ALTER TABLE companies ADD COLUMN standard_loan_purpose TEXT;              -- 标准贷款用途

-- 5. 更新现有公司数据
-- 更新中科卓尔的信息
UPDATE companies 
SET 
    primary_account = '51050148850800008651',
    standard_loan_purpose = '用于支付公司及其下属公司的员工工资、社保、缴纳税金、房租等日常经营周转，以及用于向控股（参股）公司提供公司经营的流动性支持，偿还其他银行流动资金贷款。',
    account_manager = '李睿杰',
    account_manager_phone = '***********'
WHERE id = 'a1b2c3d4-e5f6-7890-1234-567890abcdef';

-- 更新神光光学的信息
UPDATE companies 
SET 
    primary_account = '51050111128900000943'
WHERE id = '14371dda-2f8f-4d4c-82e6-c431dcf3b146';

-- 更新卫讯科技的信息
UPDATE companies 
SET 
    primary_account = '5105014885080008705'
WHERE id = '34af7659-d69a-4c05-a697-6ae6eb00aad3';

-- 6. 插入示例合同数据 (中科卓尔)
INSERT INTO contracts (
    id, 
    company_id, 
    contract_number, 
    contract_type, 
    contract_amount, 
    contract_term, 
    contract_date, 
    maturity_date, 
    contract_status
) VALUES (
    'contract-zkzr-2025-001',
    'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    '建八卓尔（2025）001号',
    '人民币流动资金贷款合同',
    2000.00,
    13,
    '2025-03-01',
    '2026-04-01',
    'active'
);

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_contracts_company ON contracts(company_id);
CREATE INDEX IF NOT EXISTS idx_contracts_number ON contracts(contract_number);
CREATE INDEX IF NOT EXISTS idx_disbursements_company ON disbursements(company_id);
CREATE INDEX IF NOT EXISTS idx_disbursements_contract ON disbursements(contract_id);
CREATE INDEX IF NOT EXISTS idx_approvals_company ON approvals(company_id);

-- 8. 添加表注释
-- SQLite不支持表注释，但我们在这里记录表的用途
-- contracts: 存储合同基本信息
-- disbursements: 存储放款/支用记录
-- approvals: 存储审批信息

COMMIT;
