-- =====================================================
-- 企业信息核心库 - 初始数据插入脚本
-- 创建日期: 2025-07-29
-- 描述: 插入成都中科卓尔智能科技集团有限公司的初始数据
-- =====================================================

-- 定义固定的UUID常量（在实际应用中应由程序动态生成）
-- 注意：PostgreSQL中需要使用具体的UUID值，这里提供示例值

-- 1. 插入角色和标签的基础定义数据
INSERT INTO roles (role_name) VALUES 
('法定代表人'), 
('授权经办人'), 
('财务负责人');

INSERT INTO tags (tag_name, tag_category) VALUES 
('国家高新技术企业', '企业资质'),
('省级"专精特新"企业', '企业资质'),
('核心战略客户', '关系类型');

-- 2. 插入公司主体信息
-- 使用固定UUID: a1b2c3d4-e5f6-7890-1234-567890abcdef
INSERT INTO companies (id, unified_social_credit_code, company_name)
VALUES ('a1b2c3d4-e5f6-7890-1234-567890abcdef', '91510100MA6CL77FXX', '成都中科卓尔智能科技集团有限公司');

-- 3. 为公司信息创建第一条历史记录
-- 使用系统管理员UUID: c3d4e5f6-a7b8-9012-3456-7890abcdef01
INSERT INTO companies_history (
    history_id, 
    company_id, 
    version_number, 
    unified_social_credit_code, 
    company_name, 
    operation_type, 
    operated_by_user_id, 
    operation_reason, 
    effective_from
)
VALUES (
    gen_random_uuid(), 
    'a1b2c3d4-e5f6-7890-1234-567890abcdef', 
    1, 
    '91510100MA6CL77FXX', 
    '成都中科卓尔智能科技集团有限公司', 
    'INSERT', 
    'c3d4e5f6-a7b8-9012-3456-7890abcdef01', 
    '初始数据录入', 
    CURRENT_TIMESTAMP
);

-- 4. 插入法人信息
-- 使用法人UUID: b2c3d4e5-f6a7-8901-2345-67890abcdef0
-- 注意：身份证号为示例，非真实
INSERT INTO persons (id, id_number, id_type, person_name)
VALUES ('b2c3d4e5-f6a7-8901-2345-67890abcdef0', '51010019800101XXXX', '身份证', '杨伟');

-- 5. 为法人信息创建第一条历史记录
INSERT INTO persons_history (
    history_id, 
    person_id, 
    version_number, 
    id_number, 
    id_type, 
    person_name, 
    operation_type, 
    operated_by_user_id, 
    operation_reason, 
    effective_from
)
VALUES (
    gen_random_uuid(), 
    'b2c3d4e5-f6a7-8901-2345-67890abcdef0', 
    1, 
    '51010019800101XXXX', 
    '身份证', 
    '杨伟', 
    'INSERT', 
    'c3d4e5f6-a7b8-9012-3456-7890abcdef01', 
    '初始数据录入', 
    CURRENT_TIMESTAMP
);

-- 6. 建立公司与法人的关系
INSERT INTO company_person_roles (
    id, 
    company_id, 
    person_id, 
    role_id, 
    start_date, 
    is_active
)
VALUES (
    gen_random_uuid(), 
    'a1b2c3d4-e5f6-7890-1234-567890abcdef', 
    'b2c3d4e5-f6a7-8901-2345-67890abcdef0', 
    (SELECT id FROM roles WHERE role_name = '法定代表人'), 
    '2018-01-01', 
    true
);

-- 7. 为公司打上标签
INSERT INTO company_tags (id, company_id, tag_id)
VALUES 
(gen_random_uuid(), 'a1b2c3d4-e5f6-7890-1234-567890abcdef', (SELECT id FROM tags WHERE tag_name = '国家高新技术企业')),
(gen_random_uuid(), 'a1b2c3d4-e5f6-7890-1234-567890abcdef', (SELECT id FROM tags WHERE tag_name = '省级"专精特新"企业')),
(gen_random_uuid(), 'a1b2c3d4-e5f6-7890-1234-567890abcdef', (SELECT id FROM tags WHERE tag_name = '核心战略客户'));

-- 验证数据插入结果的查询语句
-- 可以在数据插入后运行这些查询来验证结果

-- 查看插入的公司信息
-- SELECT * FROM companies WHERE unified_social_credit_code = '91510100MA6CL77FXX';

-- 查看公司的历史记录
-- SELECT * FROM companies_history WHERE company_id = 'a1b2c3d4-e5f6-7890-1234-567890abcdef';

-- 查看法人信息
-- SELECT * FROM persons WHERE person_name = '杨伟';

-- 查看公司与法人的关系
-- SELECT c.company_name, p.person_name, r.role_name, cpr.start_date
-- FROM company_person_roles cpr
-- JOIN companies c ON cpr.company_id = c.id
-- JOIN persons p ON cpr.person_id = p.id
-- JOIN roles r ON cpr.role_id = r.id
-- WHERE c.unified_social_credit_code = '91510100MA6CL77FXX';

-- 查看公司的标签
-- SELECT c.company_name, t.tag_name, t.tag_category
-- FROM company_tags ct
-- JOIN companies c ON ct.company_id = c.id
-- JOIN tags t ON ct.tag_id = t.id
-- WHERE c.unified_social_credit_code = '91510100MA6CL77FXX';
