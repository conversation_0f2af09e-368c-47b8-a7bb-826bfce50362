#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Excel文件格式问题
"""

import sys
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment

def create_simple_credit_template():
    """创建简单的信贷业务申请书模板（.xlsx格式）"""
    
    print("🔧 创建简单的信贷业务申请书模板")
    print("=" * 50)
    
    try:
        # 创建新的工作簿
        workbook = openpyxl.Workbook()
        sheet = workbook.active
        sheet.title = "信贷业务申请书"
        
        # 设置基本样式
        title_font = Font(name='宋体', size=16, bold=True)
        normal_font = Font(name='宋体', size=11)
        yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 设置列宽
        sheet.column_dimensions['A'].width = 20
        sheet.column_dimensions['B'].width = 50
        
        # 添加内容
        sheet['A1'] = "信贷业务申请书"
        sheet['A1'].font = title_font
        sheet['A1'].alignment = center_alignment
        sheet.merge_cells('A1:B1')
        
        sheet['A2'] = "填报日期："
        sheet['B2'] = "2025年3月     日"
        sheet['B2'].fill = yellow_fill
        
        sheet['A3'] = "借款人："
        sheet['B3'] = "成都中科卓尔智能科技集团有限公司"
        sheet['B3'].fill = yellow_fill
        
        sheet['A4'] = "额度编号："
        sheet['B4'] = "PIFU510000000N202407210（额度）"
        sheet['B4'].fill = yellow_fill
        
        sheet['A5'] = "业务编号："
        sheet['B5'] = "PIFU5100000002025N00G8（业务）"
        sheet['B5'].fill = yellow_fill
        
        sheet['A6'] = "担保方式："
        sheet['B6'] = "信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押"
        sheet['B6'].fill = yellow_fill
        
        sheet['A7'] = "本次支用金额："
        sheet['B7'] = "        万元"
        sheet['B7'].fill = yellow_fill
        
        sheet['A8'] = "期限："
        sheet['B8'] = "13个月"
        sheet['B8'].fill = yellow_fill
        
        sheet['A9'] = "环保分类："
        sheet['B9'] = "借款人环保分类为C类"
        sheet['B9'].fill = yellow_fill
        
        sheet['A10'] = "客户经理签字日期："
        sheet['B10'] = "2025年7月日"
        sheet['B10'].fill = yellow_fill
        
        sheet['A11'] = "分管领导签字日期："
        sheet['B11'] = "2025年7月日"
        sheet['B11'].fill = yellow_fill
        
        # 设置所有单元格字体
        for row in sheet.iter_rows():
            for cell in row:
                if cell.font == Font():  # 如果没有设置字体
                    cell.font = normal_font
        
        # 保存为.xlsx格式
        output_file = Path("test_output/信贷业务申请书_简单模板.xlsx")
        workbook.save(output_file)
        workbook.close()
        
        print(f"✅ 简单模板创建成功: {output_file}")
        
        # 验证文件
        try:
            test_workbook = openpyxl.load_workbook(output_file)
            test_sheet = test_workbook.active
            print(f"✅ 验证成功")
            print(f"📋 工作表: {test_sheet.title}")
            print(f"📏 尺寸: {test_sheet.max_row} 行 x {test_sheet.max_column} 列")
            print(f"📝 标题: {test_sheet['A1'].value}")
            print(f"📝 借款人: {test_sheet['B3'].value}")
            test_workbook.close()
            
            return output_file
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 创建模板失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_template_processing():
    """测试模板处理"""
    
    print(f"\n🧪 测试模板处理")
    print("=" * 30)
    
    try:
        # 导入我们的模板引擎
        sys.path.insert(0, str(Path(__file__).parent))
        from services.template_engine import TemplateEngine
        from services.data_service import UnifiedDataService
        
        # 初始化服务
        data_service = UnifiedDataService()
        template_engine = TemplateEngine()
        
        # 获取企业数据
        company_data = data_service.get_company_data("a1b2c3d4-e5f6-7890-1234-567890abcdef")
        if not company_data:
            print("❌ 获取企业数据失败")
            return
        
        # 添加用户输入数据
        company_data.update({
            'guarantee_method': '信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押',
            'loan_amount': 2000,
            'loan_term_months': 13
        })
        
        print(f"📊 企业数据: {company_data['company_name']}")
        
        # 字段映射
        field_mappings = {
            "2025年3月     日": "application_date",
            "成都中科卓尔智能科技集团有限公司": "company_name",
            "PIFU510000000N202407210（额度）": "credit_line_number",
            "PIFU5100000002025N00G8（业务）": "business_number",
            "信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押": "guarantee_method",
            "        万元": "loan_amount",
            "13个月": "loan_term_months",
            "借款人环保分类为C类": "environmental_category",
            "2025年7月日": "manager_sign_date"
        }
        
        # 处理模板
        template_path = Path("test_output/信贷业务申请书_简单模板.xlsx")
        output_path = Path("test_output/信贷业务申请书_测试输出.xlsx")
        
        result = template_engine.process_excel_template(
            template_path=template_path,
            output_path=output_path,
            data=company_data,
            field_mappings=field_mappings
        )
        
        print(f"📊 处理结果:")
        print(f"  - 成功: {result['success']}")
        print(f"  - 替换次数: {result.get('total_replacements', 0)}")
        print(f"  - 成功字段: {result.get('successful_fields', 0)}")
        
        if result['success']:
            print(f"✅ 输出文件: {output_path}")
        else:
            print(f"❌ 处理失败: {result.get('error', '未知错误')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 1. 创建简单模板
    template_file = create_simple_credit_template()
    
    if template_file:
        print(f"\n✅ 模板创建成功: {template_file}")
        
        # 2. 测试模板处理
        test_template_processing()
        
        print(f"\n💡 建议:")
        print(f"1. 先尝试打开简单模板文件: {template_file}")
        print(f"2. 如果能打开，说明格式正确")
        print(f"3. 然后检查处理后的输出文件")

if __name__ == "__main__":
    main()
