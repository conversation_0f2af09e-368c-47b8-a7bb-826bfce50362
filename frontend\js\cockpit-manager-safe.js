/**
 * 安全版驾驶舱管理器
 * 解决this绑定问题的完全重写版本
 */

class SafeCockpitManager {
    constructor() {
        this.currentCustomer = null;
        this.workflowTasks = [
            { id: 'service-agreement', title: '生成《服务协议》', completed: false },
            { id: 'oa-document', title: '生成《OA正文》', completed: false },
            { id: 'authorization-form', title: '下载《对公客户授权及承诺书》', completed: true },
            { id: 'application-form', title: '下载《对公综合服务申请书》', completed: true }
        ];
        
        // 绑定所有方法到this
        this.init = this.init.bind(this);
        this.bindEvents = this.bindEvents.bind(this);
        this.loadCustomers = this.loadCustomers.bind(this);
        this.selectCustomer = this.selectCustomer.bind(this);
        this.handleTaskAction = this.handleTaskAction.bind(this);
        this.generateServiceAgreement = this.generateServiceAgreement.bind(this);
        this.addLog = this.addLog.bind(this);
        this.updateStatusFeedback = this.updateStatusFeedback.bind(this);
        
        this.init();
    }

    init() {
        console.log('SafeCockpitManager 初始化开始');

        try {
            // 首先确保正确的初始显示状态
            this.ensureCorrectInitialState();

            this.bindEvents();
            this.updateProgress();

            // 检查是否从统一驾驶舱跳转而来
            this.checkCockpitParams();

            // 延迟加载客户数据，确保DOM完全加载
            setTimeout(() => {
                this.loadCustomers();
            }, 1000);

            this.addLog('info', '驾驶舱已启动');
            console.log('SafeCockpitManager 初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    ensureCorrectInitialState() {
        console.log('确保正确的初始显示状态');

        // 强制设置初始显示状态
        const customerStage = document.getElementById('customer-selection-stage');
        const moduleStage = document.getElementById('module-selection-stage');
        const workflowStage = document.getElementById('workflow-stage');
        const sidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');

        // 确保客户选择界面显示，其他隐藏
        if (customerStage) {
            customerStage.style.display = 'block';
            console.log('客户选择界面已显示');
        }
        if (moduleStage) {
            moduleStage.style.display = 'none';
            console.log('模块选择界面已隐藏');
        }
        if (workflowStage) {
            workflowStage.style.display = 'none';
            console.log('工作流程界面已隐藏');
        }
        if (sidebar) {
            sidebar.style.display = 'none';
            console.log('侧边栏已隐藏');
        }
        if (logsPanel) {
            logsPanel.style.display = 'block';
            console.log('日志面板已显示');
        }
    }

    checkCockpitParams() {
        // 检查URL参数，看是否从统一驾驶舱跳转而来
        const urlParams = new URLSearchParams(window.location.search);
        const customerId = urlParams.get('customer_id');
        const customerName = urlParams.get('customer_name');
        const customerCode = urlParams.get('customer_code');
        const fromCockpit = urlParams.get('from_cockpit');
        const lockCustomer = urlParams.get('lock_customer');

        console.log('URL参数检查:', {
            customerId, customerName, customerCode, fromCockpit, lockCustomer
        });

        if (fromCockpit === 'true' && customerId) {
            console.log('检测到来自统一驾驶舱的跳转');
            this.addLog('info', `来自统一驾驶舱，客户: ${customerName || customerId}`);

            // 添加返回按钮
            this.addReturnButton();

            // 预设客户信息
            this.presetCustomer = {
                id: customerId,
                company_name: customerName || '未知企业',
                unified_social_credit_code: customerCode || ''
            };

            // 如果锁定客户，直接选择该客户并显示模块选择界面
            if (lockCustomer === 'true') {
                this.isCustomerLocked = true;
                console.log('客户已锁定，自动选择客户');

                // 延迟自动选择客户，确保页面加载完成
                setTimeout(() => {
                    this.autoSelectLockedCustomer();
                }, 1000);
            }
        } else {
            console.log('无URL参数或非统一驾驶舱跳转，正常启动');
        }
    }

    async autoSelectLockedCustomer() {
        if (!this.presetCustomer) return;

        try {
            // 直接设置当前客户
            this.currentCustomer = this.presetCustomer;

            // 尝试从API获取完整信息
            try {
                const response = await fetch(`http://localhost:5000/api/company/${this.presetCustomer.id}`);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.status === 'success') {
                        this.currentCustomer = apiData.data;
                    }
                }
            } catch (apiError) {
                console.log('API调用失败，使用预设数据');
            }

            // 显示模块选择界面，而不是直接进入银企直联
            this.showModuleSelection();
            this.updateCustomerSidebar();

            // 隐藏客户选择界面
            const selectionStage = document.getElementById('customer-selection-stage');
            if (selectionStage) {
                selectionStage.style.display = 'none';
            }

            this.addLog('success', `已锁定客户: ${this.currentCustomer.company_name}`);
            this.updateStatusFeedback(`已为 ${this.currentCustomer.company_name} 准备就绪，请选择业务模块`);

        } catch (error) {
            console.error('自动选择客户失败:', error);
            this.addLog('error', '自动选择客户失败: ' + error.message);
        }
    }

    addReturnButton() {
        // 在页面标题旁添加返回按钮
        const header = document.querySelector('.cockpit-header');
        if (header) {
            const returnBtn = document.createElement('button');
            returnBtn.innerHTML = '← 返回驾驶舱';
            returnBtn.className = 'return-cockpit-btn';
            returnBtn.style.cssText = `
                position: absolute;
                top: 1rem;
                left: 1rem;
                background: #3498db;
                color: white;
                border: none;
                padding: 0.8rem 1.5rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.9rem;
                transition: all 0.3s ease;
            `;

            returnBtn.addEventListener('click', () => {
                window.location.href = 'index_unified_cockpit.html';
            });

            returnBtn.addEventListener('mouseenter', () => {
                returnBtn.style.background = '#2980b9';
            });

            returnBtn.addEventListener('mouseleave', () => {
                returnBtn.style.background = '#3498db';
            });

            header.style.position = 'relative';
            header.appendChild(returnBtn);
        }
    }

    bindEvents() {
        // 客户选择
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.addEventListener('change', (e) => {
                this.selectCustomer(e.target.value);
            });
        }

        // 更换客户按钮
        const changeBtn = document.getElementById('change-customer-btn');
        if (changeBtn) {
            changeBtn.addEventListener('click', () => {
                this.showCustomerSelection();
            });
        }

        // 更换模块按钮
        const changeModuleBtn = document.getElementById('change-module-btn');
        if (changeModuleBtn) {
            changeModuleBtn.addEventListener('click', () => {
                this.showModuleSelection();
                this.updateSidebarModule('未选择模块', '请先选择要使用的业务模块');
                this.addLog('info', '返回模块选择界面');
            });
        }

        // 任务按钮
        document.addEventListener('click', (e) => {
            const actionBtn = e.target.closest('.action-btn');
            if (actionBtn && actionBtn.dataset.action) {
                e.preventDefault();
                this.handleTaskAction(actionBtn.dataset.action);
            }
        });

        // 清空日志
        const clearBtn = document.getElementById('clear-logs-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearLogs();
            });
        }
    }

    async loadCustomers() {
        try {
            console.log('开始加载客户数据');

            // 直接使用固定的客户数据，确保系统能正常工作
            const companies = [
                {
                    id: '34af7659-d69a-4c05-a697-6ae6eb00aad3',
                    company_name: '成都中科卓尔智能科技集团有限公司',
                    unified_social_credit_code: '91510100MA6CGUGA1W'
                },
                {
                    id: 'a806f8c4-b681-4bed-90b3-cadf2170826f',
                    company_name: '四川至臻精密光学有限公司',
                    unified_social_credit_code: '91510100MA6CGUGA2X'
                },
                {
                    id: 'test-company-1',
                    company_name: '神光光学集团有限公司',
                    unified_social_credit_code: '91510100MA6CGUGA3Y'
                },
                {
                    id: 'test-company-2',
                    company_name: '成都卫讯科技有限公司',
                    unified_social_credit_code: '91510100MA6CGUGA4Z'
                }
            ];

            console.log('使用固定客户数据:', companies);
            this.addLog('success', `加载了 ${companies.length} 个客户`);

            // 尝试从API加载更多数据（可选）
            try {
                const response = await fetch('http://localhost:5000/api/companies');
                if (response.ok) {
                    const data = await response.json();
                    const apiCompanies = data.data || data;
                    if (apiCompanies && apiCompanies.length > 0) {
                        console.log('API数据也可用，合并数据');
                        // 可以选择合并或替换数据
                    }
                }
            } catch (apiError) {
                console.log('API不可用，继续使用固定数据');
            }
            
            // 填充选择框
            const select = document.getElementById('customer-select');
            console.log('客户选择框元素:', select);
            console.log('企业数据:', companies);

            if (select) {
                select.innerHTML = '<option value="">请选择客户...</option>';
                companies.forEach(company => {
                    const option = document.createElement('option');
                    option.value = company.id;
                    option.textContent = company.company_name;
                    select.appendChild(option);
                });
                console.log(`✅ 已添加 ${companies.length} 个客户到选择框`);
                this.addLog('success', `客户列表加载完成，共 ${companies.length} 个客户`);
            } else {
                console.error('❌ 找不到客户选择框元素');
                this.addLog('error', '找不到客户选择框元素');
            }
            
        } catch (error) {
            console.error('加载客户失败:', error);
            this.addLog('error', '加载客户失败: ' + error.message);
        }
    }

    async selectCustomer(customerId) {
        if (!customerId) return;

        try {
            this.addLog('info', '正在加载客户信息...');
            console.log('选择的客户ID:', customerId);

            // 尝试从API获取真实数据
            let customerData = null;

            try {
                const response = await fetch(`http://localhost:5000/api/company/${customerId}`);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.status === 'success') {
                        customerData = apiData.data;
                        console.log('从API获取客户数据:', customerData);
                    }
                }
            } catch (apiError) {
                console.log('API调用失败，使用模拟数据:', apiError.message);
            }

            // 如果API失败，使用模拟数据
            if (!customerData) {
                console.log('使用模拟数据');
                customerData = this.getMockCustomerData(customerId);
            }

            this.currentCustomer = customerData;
            console.log('最终客户数据:', this.currentCustomer);

            // 显示模块选择界面
            this.showModuleSelection();
            this.updateCustomerSidebar();
            this.updateStatusFeedback('客户信息已加载，请选择业务模块');
            this.addLog('success', `已选择客户: ${this.currentCustomer.company_name}`);

        } catch (error) {
            console.error('选择客户失败:', error);
            this.addLog('error', '选择客户失败: ' + error.message);
        }
    }

    getMockCustomerData(customerId) {
        // 根据客户ID返回对应的真实公司模拟数据
        const mockData = {
            '34af7659-d69a-4c05-a697-6ae6eb00aad3': {
                id: '34af7659-d69a-4c05-a697-6ae6eb00aad3',
                company_name: '成都卫讯科技有限公司',
                unified_social_credit_code: '915101003320526751',
                registered_address: '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                communication_address: '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                business_description: '通信设备制造、软件开发'
            },
            'a1b2c3d4-e5f6-7890-1234-567890abcdef': {
                id: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
                company_name: '成都中科卓尔智能科技集团有限公司',
                unified_social_credit_code: '91510100MA61R8LU8K',
                registered_address: '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
                communication_address: '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
                business_description: '智能科技、软件开发、技术服务'
            },
            // 向后兼容旧的mock ID
            'mock1': {
                id: 'mock1',
                company_name: '成都卫讯科技有限公司',
                unified_social_credit_code: '915101003320526751',
                registered_address: '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                communication_address: '中国（四川）自由贸易试验区成都市天府新区兴隆街道湖畔路西段99号',
                business_description: '通信设备制造、软件开发'
            },
            'mock2': {
                id: 'mock2',
                company_name: '成都中科卓尔智能科技集团有限公司',
                unified_social_credit_code: '91510100MA61R8LU8K',
                registered_address: '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
                communication_address: '中国（四川）自由贸易试验区成都高新区天府五街200号3号楼B区6楼',
                business_description: '智能科技、软件开发、技术服务'
            }
        };

        return mockData[customerId] || mockData['34af7659-d69a-4c05-a697-6ae6eb00aad3'];
    }

    showModuleSelection() {
        console.log('🎯 显示模块选择界面');

        const selectionStage = document.getElementById('customer-selection-stage');
        const moduleStage = document.getElementById('module-selection-stage');
        const workflowStage = document.getElementById('workflow-stage');
        const sidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');

        console.log('DOM元素检查:', {
            selectionStage: !!selectionStage,
            moduleStage: !!moduleStage,
            workflowStage: !!workflowStage,
            sidebar: !!sidebar,
            logsPanel: !!logsPanel
        });

        if (selectionStage) {
            selectionStage.style.display = 'none';
            console.log('✅ 隐藏客户选择阶段');
        }

        if (moduleStage) {
            moduleStage.style.display = 'block';
            console.log('✅ 显示模块选择阶段');

            // 检查模块卡片是否存在
            const moduleCards = moduleStage.querySelectorAll('.module-card');
            console.log(`📋 找到 ${moduleCards.length} 个模块卡片`);
            moduleCards.forEach((card, index) => {
                const moduleType = card.getAttribute('data-module');
                const title = card.querySelector('.module-title')?.textContent;
                console.log(`  模块${index + 1}: ${title} (${moduleType})`);
            });
        } else {
            console.error('❌ 找不到模块选择阶段元素');
        }

        if (workflowStage) {
            workflowStage.style.display = 'none';
            console.log('✅ 隐藏工作流程阶段');
        }

        if (sidebar) {
            sidebar.style.display = 'block';
            console.log('✅ 显示侧边栏');
        }

        if (logsPanel) {
            logsPanel.style.display = 'block';
            console.log('✅ 显示日志面板');
        }

        console.log('🎯 模块选择界面显示完成');
    }

    selectModule(moduleType) {
        this.addLog('info', `选择模块: ${moduleType}`);

        // 检查当前客户是否存在
        if (!this.currentCustomer) {
            this.addLog('error', '错误：未选择客户，无法进入模块');
            alert('请先选择一个客户企业');
            return;
        }

        console.log('当前客户信息:', this.currentCustomer);
        console.log('选择的模块:', moduleType);

        if (moduleType === 'yingqi_zhilian') {
            // 更新侧边栏模块信息
            this.updateSidebarModule('银企直联', '银行系统与企业财务系统直连服务');

            // 银企直联模块 - 显示当前工作流程
            this.showWorkflowStage();
            this.updateStatusFeedback('已进入银企直联模块，可以开始办理业务');

        } else if (moduleType === 'customer_engagement') {
            // 更新侧边栏模块信息
            this.updateSidebarModule('客户接洽与资料准备', '客户接洽流程管理，包含检查清单、管护权确认函、服务方案等');

            try {
                // 客户接洽模块 - 跳转到客户接洽页面
                const params = new URLSearchParams({
                    customer_id: this.currentCustomer.id,
                    customer_name: this.currentCustomer.company_name,
                    customer_code: this.currentCustomer.unified_social_credit_code || '',
                    from_cockpit: 'true'
                });

                this.addLog('info', '正在跳转到客户接洽模块...');
                console.log('跳转参数:', params.toString());
                window.location.href = `customer_engagement_workspace.html?${params.toString()}`;

            } catch (error) {
                this.addLog('error', `客户接洽模块跳转失败: ${error.message}`);
                console.error('客户接洽模块跳转错误:', error);
                alert('跳转到客户接洽模块失败，请重试');
            }

        } else if (moduleType === 'deposit_services') {
            // 更新侧边栏模块信息
            this.updateSidebarModule('协定存款业务', '协定存款协议生成与管理，包含协议生成、记录管理、配置设置等');

            try {
                // 协定存款模块 - 跳转到协定存款页面
                const params = new URLSearchParams({
                    customer_id: this.currentCustomer.id,
                    customer_name: this.currentCustomer.company_name,
                    customer_code: this.currentCustomer.unified_social_credit_code || '',
                    from_cockpit: 'true'
                });

                // 跳转到协定存款工作台
                this.addLog('info', '正在跳转到协定存款模块...');
                console.log('跳转参数:', params.toString());
                window.location.href = `deposit_services_workspace.html?${params.toString()}`;

            } catch (error) {
                this.addLog('error', `协定存款模块跳转失败: ${error.message}`);
                console.error('协定存款模块跳转错误:', error);
                alert('跳转到协定存款模块失败，请重试');
            }

        } else {
            this.addLog('error', `未知的模块类型: ${moduleType}`);
            alert(`未知的模块类型: ${moduleType}`);
        }
    }

    showWorkflowStage() {
        const selectionStage = document.getElementById('customer-selection-stage');
        const moduleStage = document.getElementById('module-selection-stage');
        const workflowStage = document.getElementById('workflow-stage');
        const sidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');

        if (selectionStage) selectionStage.style.display = 'none';
        if (moduleStage) moduleStage.style.display = 'none';
        if (workflowStage) workflowStage.style.display = 'block';
        if (sidebar) sidebar.style.display = 'block';
        if (logsPanel) logsPanel.style.display = 'block';
    }

    showCustomerSelection() {
        const selectionStage = document.getElementById('customer-selection-stage');
        const moduleStage = document.getElementById('module-selection-stage');
        const workflowStage = document.getElementById('workflow-stage');
        const sidebar = document.getElementById('customer-sidebar');
        const logsPanel = document.getElementById('logs-panel');

        if (selectionStage) selectionStage.style.display = 'block';
        if (moduleStage) moduleStage.style.display = 'none';
        if (workflowStage) workflowStage.style.display = 'none';
        if (sidebar) sidebar.style.display = 'none';
        if (logsPanel) logsPanel.style.display = 'none';

        this.currentCustomer = null;
        const select = document.getElementById('customer-select');
        if (select) select.value = '';
    }

    updateCustomerSidebar() {
        if (!this.currentCustomer) return;

        console.log('更新侧边栏，客户数据:', this.currentCustomer);

        // 更新客户卡片
        const nameEl = document.getElementById('sidebar-company-name');
        const codeEl = document.getElementById('sidebar-credit-code');

        if (nameEl) {
            nameEl.textContent = this.currentCustomer.company_name || '未知企业';
            console.log('更新企业名称:', this.currentCustomer.company_name);
        }
        if (codeEl) {
            codeEl.textContent = this.currentCustomer.unified_social_credit_code || '未提供';
            console.log('更新信用代码:', this.currentCustomer.unified_social_credit_code);
        }

        // 更新详细信息
        const registeredAddressEl = document.getElementById('sidebar-registered-address');
        const communicationAddressEl = document.getElementById('sidebar-communication-address');
        const businessDescEl = document.getElementById('sidebar-business-description');

        if (registeredAddressEl) {
            registeredAddressEl.textContent = this.currentCustomer.registered_address || '未提供';
        }
        if (communicationAddressEl) {
            communicationAddressEl.textContent = this.currentCustomer.communication_address || '未提供';
        }
        if (businessDescEl) {
            businessDescEl.textContent = this.currentCustomer.business_description || '未提供';
        }

        console.log('侧边栏更新完成');
    }

    updateSidebarModule(moduleName, moduleDescription) {
        const moduleNameEl = document.getElementById('sidebar-module-name');
        const moduleDescEl = document.getElementById('sidebar-module-description');

        if (moduleNameEl) {
            moduleNameEl.textContent = moduleName;
        }
        if (moduleDescEl) {
            moduleDescEl.textContent = moduleDescription;
        }

        console.log('侧边栏模块信息已更新:', moduleName);
    }

    resetToModuleSelection() {
        // 返回到统一驾驶舱进行模块选择
        this.addLog('info', '正在返回统一驾驶舱进行模块选择...');

        // 跳转到统一驾驶舱，保持当前客户信息
        const params = new URLSearchParams({
            customer_id: this.currentCustomer.id,
            customer_name: this.currentCustomer.company_name,
            customer_code: this.currentCustomer.unified_social_credit_code || '',
            auto_select: 'true'
        });

        window.location.href = `index_unified_cockpit.html?${params.toString()}`;
    }

    async handleTaskAction(action) {
        if (!this.currentCustomer) {
            this.addLog('error', '请先选择客户');
            return;
        }

        this.addLog('info', `正在执行: ${action}`);

        try {
            switch (action) {
                case 'generate-agreement':
                    await this.generateServiceAgreement();
                    break;
                case 'generate-oa':
                    await this.openOAWorkspace();
                    break;
                case 'download-auth':
                    this.addLog('success', '《授权书》下载完成（模拟）');
                    break;
                case 'download-app':
                    this.addLog('success', '《申请书》下载完成（模拟）');
                    break;
                default:
                    this.addLog('error', '未知操作: ' + action);
            }
        } catch (error) {
            console.error('操作失败:', error);
            this.addLog('error', '操作失败: ' + error.message);
        }
    }

    async generateServiceAgreement() {
        this.updateStatusFeedback('正在生成《服务协议》...', 'processing');

        try {
            console.log('开始生成服务协议...');
            console.log('客户信息:', this.currentCustomer);

            const requestData = {
                company_id: this.currentCustomer.id
            };

            console.log('请求数据:', requestData);

            const response = await fetch('http://localhost:5000/api/documents/generate_agreement', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            console.log('响应状态:', response.status);
            console.log('响应头:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                // 尝试获取错误详情
                let errorMessage = `HTTP ${response.status}`;
                try {
                    const errorText = await response.text();
                    console.log('错误响应内容:', errorText);
                    errorMessage += `: ${errorText}`;
                } catch (e) {
                    console.log('无法读取错误响应');
                }
                throw new Error(errorMessage);
            }

            console.log('开始处理响应blob...');
            const blob = await response.blob();
            console.log('Blob大小:', blob.size);

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentCustomer.company_name}_服务协议.docx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.addLog('success', '《服务协议》生成成功，已自动下载');
            this.updateStatusFeedback('文档生成完成！', 'success');

            // 标记任务完成
            const checkbox = document.getElementById('task-agreement');
            if (checkbox) checkbox.checked = true;

        } catch (error) {
            console.error('生成文档失败:', error);
            this.addLog('error', '生成文档失败: ' + error.message);
            this.updateStatusFeedback('生成失败，请重试', 'error');
        }
    }

    updateProgress() {
        const completed = this.workflowTasks.filter(t => t.completed).length;
        const total = this.workflowTasks.length;
        const percentage = (completed / total) * 100;

        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressFill) progressFill.style.width = `${percentage}%`;
        if (progressText) progressText.textContent = `${completed}/${total} 已完成`;
    }

    updateStatusFeedback(message, type = 'info') {
        const feedback = document.getElementById('status-feedback');
        if (!feedback) return;

        const textEl = feedback.querySelector('.feedback-text');
        const iconEl = feedback.querySelector('.feedback-icon');

        if (textEl) textEl.textContent = message;
        if (iconEl) {
            const icons = { info: '✨', success: '✅', error: '❌', processing: '⏳' };
            iconEl.textContent = icons[type] || '✨';
        }
    }

    addLog(type, message) {
        try {
            console.log(`[${type.toUpperCase()}] ${message}`);
            
            const container = document.getElementById('operation-logs');
            if (!container) return;

            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `
                <div style="font-weight: 500; margin-bottom: 0.2rem;">
                    ${new Date().toLocaleTimeString()} - ${this.getLogIcon(type)}
                </div>
                <div>${message}</div>
            `;

            container.insertBefore(entry, container.firstChild);

            // 限制日志数量
            const logs = container.querySelectorAll('.log-entry');
            if (logs.length > 20) {
                logs[logs.length - 1].remove();
            }
        } catch (error) {
            console.error('添加日志失败:', error);
        }
    }

    getLogIcon(type) {
        const icons = {
            success: '✅ 成功',
            error: '❌ 错误', 
            info: 'ℹ️ 信息',
            warning: '⚠️ 警告'
        };
        return icons[type] || 'ℹ️ 信息';
    }

    clearLogs() {
        const container = document.getElementById('operation-logs');
        if (container) {
            container.innerHTML = '';
            this.addLog('info', '日志已清空');
        }
    }

    showError(message) {
        const stage = document.getElementById('customer-selection-stage');
        if (stage) {
            stage.innerHTML = `
                <div class="selection-content">
                    <div class="selection-icon" style="color: #f44336;">❌</div>
                    <h2 style="color: #f44336;">启动失败</h2>
                    <p style="color: #666;">${message}</p>
                    <button onclick="location.reload()" 
                            style="padding: 0.8rem 2rem; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; margin-top: 1rem;">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    // =====================================================
    // OA正文人机协作工作台方法
    // =====================================================

    async openOAWorkspace() {
        console.log('🤖 OA工作台v2.0 - 开始执行新的工作台逻辑');
        this.addLog('info', '正在打开OA协作工作台v2.0...');

        try {
            // 显示OA工作台模态窗口
            const modal = document.getElementById('oa-workspace-modal');
            if (modal) {
                modal.style.display = 'flex';

                // 显示加载状态
                this.showOAWorkspaceLoading();

                // 准备半成品OA文档
                await this.prepareOADocument();

                this.addLog('success', 'OA协作工作台已打开');
            } else {
                throw new Error('OA工作台模态窗口未找到');
            }
        } catch (error) {
            this.addLog('error', `打开OA工作台失败: ${error.message}`);
            this.showOAWorkspaceError(error.message);
        }
    }

    showOAWorkspaceLoading() {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = `
                <div class="oa-workspace-loading">
                    <div class="loading-header">
                        <h2>🤖 OA正文人机协作工作台</h2>
                        <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                    </div>
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <p>正在为 <strong>${this.currentCustomer.company_name}</strong> 准备半成品OA文档...</p>
                        <p class="loading-detail">系统正在自动填充客户信息并高亮标记...</p>
                    </div>
                </div>
            `;
        }
    }

    async prepareOADocument() {
        try {
            // 调用后端API准备半成品文档
            const response = await fetch('http://localhost:5000/api/documents/prepare_oa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const result = await response.json();
            if (result.status === 'success') {
                // 显示完整的工作台界面
                this.showFullOAWorkspace(result.data);
            } else {
                throw new Error(result.message || '准备文档失败');
            }

        } catch (error) {
            console.error('准备OA文档失败:', error);
            throw error;
        }
    }

    showFullOAWorkspace(oaData) {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = this.generateOAWorkspaceHTML(oaData);

            // 绑定工作台事件
            this.bindOAWorkspaceEvents(oaData);
        }
    }

    generateOAWorkspaceHTML(oaData) {
        const geminiPrompt = this.generateGeminiPrompt(oaData.company_info);

        return `
            <div class="oa-workspace-container">
                <!-- 工作台头部 -->
                <div class="workspace-header">
                    <div class="header-left">
                        <h2>🤖 OA正文人机协作工作台</h2>
                        <p class="customer-info">当前客户：<strong>${oaData.company_info.company_name}</strong></p>
                    </div>
                    <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                </div>

                <!-- 工作台主体 -->
                <div class="workspace-body">
                    <!-- 左侧：半成品文档展示 -->
                    <div class="document-panel">
                        <div class="panel-header">
                            <h3>📄 半成品文档预览</h3>
                            <div class="replacement-info">
                                <span class="highlight-legend">🟡 黄色高亮 = 系统自动填充</span>
                                <span class="replacement-count">已替换 ${oaData.replacements_made.length} 处内容</span>
                            </div>
                        </div>
                        <div class="document-content" id="oa-document-preview">
                            ${oaData.html_content}
                        </div>
                    </div>

                    <!-- 右侧：AI协作面板 -->
                    <div class="ai-panel">
                        <!-- Gemini提示词区域 -->
                        <div class="prompt-section">
                            <div class="section-header">
                                <h3>🎯 Gemini提示词</h3>
                                <button class="copy-btn" id="copy-prompt-btn">📋 复制提示词</button>
                            </div>
                            <div class="prompt-content">
                                <textarea id="gemini-prompt" readonly>${geminiPrompt}</textarea>
                            </div>
                        </div>

                        <!-- AI回复输入区域 -->
                        <div class="ai-input-section">
                            <div class="section-header">
                                <h3>🤖 AI生成内容</h3>
                                <p class="instruction">请将Gemini的回复内容粘贴到下方文本框中</p>
                            </div>
                            <div class="ai-input-content">
                                <textarea id="ai-response" placeholder="请在此粘贴Gemini生成的内容...&#10;&#10;提示：&#10;1. 复制上方的提示词到Gemini&#10;2. 获取AI回复后粘贴到此处&#10;3. 点击下方按钮完成文档合成"></textarea>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->
                        <div class="action-section">
                            <button class="action-btn secondary" id="preview-final-btn" disabled>
                                👁️ 预览最终文档
                            </button>
                            <button class="action-btn primary" id="generate-final-btn" disabled>
                                ✅ 完成并生成文档
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 工作台底部状态 -->
                <div class="workspace-footer">
                    <div class="status-info">
                        <span class="status-item">📊 模板加载完成</span>
                        <span class="status-item">🔄 自动替换完成</span>
                        <span class="status-item" id="ai-status">⏳ 等待AI内容</span>
                    </div>
                </div>
            </div>
        `;
    }

    generateGeminiPrompt(companyInfo) {
        const companyName = companyInfo.company_name || '【公司全称】';

        return `请扮演一名专业的银行客户经理。我正在为客户"${companyName}"撰写一份关于开通"银企直联"服务的内部请示文件（OA）。请为我草拟其中"一、客户综合情况"和"二、业务价值及费用减免说明"这两个部分的正文。

要求：
1. 语言专业、逻辑清晰
2. 重点突出该客户的战略价值
3. 强调此项业务对我行的潜在收益
4. 内容应当具体且有说服力
5. 请直接输出正文内容，无需添加任何额外的标题或解释

客户基本信息：
- 公司名称：${companyName}
- 行业分类：${companyInfo.industry_category || '制造业'}
- 注册资本：${companyInfo.registered_capital || ''}万元
- 经营范围：${companyInfo.business_scope || ''}

请基于以上信息生成专业的OA正文内容。`;
    }

    bindOAWorkspaceEvents(oaData) {
        // 复制提示词按钮
        const copyPromptBtn = document.getElementById('copy-prompt-btn');
        if (copyPromptBtn) {
            copyPromptBtn.addEventListener('click', () => {
                const promptTextarea = document.getElementById('gemini-prompt');
                if (promptTextarea) {
                    promptTextarea.select();
                    document.execCommand('copy');

                    // 显示复制成功提示
                    copyPromptBtn.textContent = '✅ 已复制';
                    setTimeout(() => {
                        copyPromptBtn.textContent = '📋 复制提示词';
                    }, 2000);
                }
            });
        }

        // AI回复输入监听
        const aiResponseTextarea = document.getElementById('ai-response');
        if (aiResponseTextarea) {
            aiResponseTextarea.addEventListener('input', () => {
                const hasContent = aiResponseTextarea.value.trim().length > 0;

                // 启用/禁用按钮
                const previewBtn = document.getElementById('preview-final-btn');
                const generateBtn = document.getElementById('generate-final-btn');

                if (previewBtn) previewBtn.disabled = !hasContent;
                if (generateBtn) generateBtn.disabled = !hasContent;

                // 更新状态
                const aiStatus = document.getElementById('ai-status');
                if (aiStatus) {
                    if (hasContent) {
                        aiStatus.textContent = '✅ AI内容已输入';
                        aiStatus.style.color = '#4CAF50';
                    } else {
                        aiStatus.textContent = '⏳ 等待AI内容';
                        aiStatus.style.color = '#666';
                    }
                }
            });
        }

        // 预览最终文档按钮
        const previewBtn = document.getElementById('preview-final-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => {
                this.previewFinalOADocument(oaData);
            });
        }

        // 生成最终文档按钮
        const generateBtn = document.getElementById('generate-final-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateFinalOADocument(oaData);
            });
        }
    }

    previewFinalOADocument(oaData) {
        const aiContent = document.getElementById('ai-response').value.trim();
        if (!aiContent) {
            alert('请先输入AI生成的内容');
            return;
        }

        // 在新窗口中显示预览
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>OA文档预览 - ${oaData.company_info.company_name}</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; padding: 2rem; line-height: 1.6; }
                        .preview-header { border-bottom: 2px solid #333; padding-bottom: 1rem; margin-bottom: 2rem; }
                        .auto-replaced { background-color: yellow; padding: 2px 4px; }
                        .ai-content { background-color: #f0f8ff; padding: 1rem; border-left: 4px solid #2196F3; margin: 1rem 0; }
                        .doc-paragraph { margin: 0.5rem 0; }
                        .doc-heading { font-weight: bold; margin: 1rem 0 0.5rem 0; }
                    </style>
                </head>
                <body>
                    <div class="preview-header">
                        <h1>OA正文预览</h1>
                        <p>客户：${oaData.company_info.company_name}</p>
                    </div>
                    <div class="document-preview">
                        ${oaData.html_content}
                        <div class="ai-content">
                            <h3>AI生成内容：</h3>
                            <div>${aiContent.replace(/\n/g, '<br>')}</div>
                        </div>
                    </div>
                </body>
            </html>
        `);
        previewWindow.document.close();
    }

    async generateFinalOADocument(oaData) {
        const aiContent = document.getElementById('ai-response').value.trim();
        if (!aiContent) {
            alert('请先输入AI生成的内容');
            return;
        }

        try {
            // 显示生成中状态
            const generateBtn = document.getElementById('generate-final-btn');
            const originalText = generateBtn.textContent;
            generateBtn.textContent = '🔄 生成中...';
            generateBtn.disabled = true;

            // 调用后端API合成最终文档
            const response = await fetch('http://localhost:5000/api/documents/finalize_oa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id,
                    temp_doc_path: oaData.temp_doc_path,
                    ai_content: aiContent
                })
            });

            if (response.ok) {
                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.currentCustomer.company_name}_OA正文.docx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                // 显示成功消息
                this.addLog('success', 'OA正文文档生成成功');

                // 关闭工作台
                document.getElementById('oa-workspace-modal').style.display = 'none';

                // 标记任务完成
                this.toggleTask('oa-document', true);

            } else {
                throw new Error(`生成失败: ${response.status}`);
            }

        } catch (error) {
            console.error('生成最终文档失败:', error);
            this.addLog('error', `生成最终文档失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            const generateBtn = document.getElementById('generate-final-btn');
            if (generateBtn) {
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;
            }
        }
    }

    showOAWorkspaceError(errorMessage) {
        const modalContent = document.querySelector('.oa-workspace-content');
        if (modalContent) {
            modalContent.innerHTML = `
                <div class="oa-workspace-error">
                    <div class="error-header">
                        <h2>❌ OA工作台加载失败</h2>
                        <button class="close-btn" onclick="this.closest('.oa-workspace-modal').style.display='none'">×</button>
                    </div>
                    <div class="error-content">
                        <p>错误信息：${errorMessage}</p>
                        <div class="error-actions">
                            <button onclick="location.reload()" class="retry-btn">🔄 重新加载</button>
                            <button onclick="this.closest('.oa-workspace-modal').style.display='none'" class="close-error-btn">关闭</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }
}

// 安全初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('开始初始化安全版驾驶舱');
        window.safeCockpitManager = new SafeCockpitManager();
        console.log('安全版驾驶舱初始化成功');
    } catch (error) {
        console.error('安全版驾驶舱初始化失败:', error);
        alert('驾驶舱启动失败: ' + error.message);
    }
});
