#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新落实情况表模板 - 填入中科卓尔的具体信息
根据用户要求：更新现有的落实情况表.docx模板，填入三段具体内容
"""

import docx
from docx.shared import RGBColor
from pathlib import Path
from datetime import datetime
import shutil
import sqlite3

class ImplementationTableUpdater:
    """落实情况表更新器"""
    
    def __init__(self):
        """初始化"""
        self.project_root = Path(__file__).parent
        self.template_dir = self.project_root / "templates" / "contract_disbursement"
        self.test_output_dir = self.project_root / "test_output"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        
        # 确保输出目录存在
        self.test_output_dir.mkdir(exist_ok=True)
        
        # 中科卓尔公司ID
        self.zkzr_company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
        
        # 绿色RGB值
        self.green_rgb = RGBColor(0, 128, 0)
    
    def get_company_data(self):
        """获取中科卓尔公司数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT company_name, company_short_name, legal_representative,
                       registered_capital, business_scope
                FROM companies
                WHERE id = ?
            """, (self.zkzr_company_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'company_name': result[0],
                    'company_short_name': result[1],
                    'legal_representative': result[2],
                    'registered_capital': result[3],
                    'business_scope': result[4]
                }
            else:
                print(f"❌ 未找到公司数据: {self.zkzr_company_id}")
                return None
                
        except Exception as e:
            print(f"❌ 获取公司数据失败: {e}")
            return None
    
    def replace_text_in_paragraph_with_green(self, paragraph, old_text, new_text):
        """在段落中替换文本并标记为绿色"""
        if old_text in paragraph.text:
            # 清空段落内容
            paragraph.clear()
            
            # 重新构建段落，将替换的部分标记为绿色
            full_text = paragraph.text if hasattr(paragraph, 'text') else ""
            if old_text in full_text:
                parts = full_text.split(old_text)
                
                for i, part in enumerate(parts):
                    if part:
                        run = paragraph.add_run(part)
                    
                    if i < len(parts) - 1:  # 不是最后一部分
                        green_run = paragraph.add_run(new_text)
                        green_run.font.color.rgb = self.green_rgb
            else:
                # 如果没有找到要替换的文本，直接添加新文本并标记为绿色
                green_run = paragraph.add_run(new_text)
                green_run.font.color.rgb = self.green_rgb
    
    def update_implementation_table_content(self):
        """更新落实情况表的三段内容"""
        print("🔧 开始更新落实情况表模板...")
        print("📋 用户要求: 更新现有模板，填入中科卓尔的具体信息")
        
        company_data = self.get_company_data()
        if not company_data:
            print("❌ 无法获取公司数据")
            return None
        
        try:
            # 源文件路径
            template_path = self.template_dir / "落实情况表.docx"
            
            if not template_path.exists():
                print(f"❌ 落实情况表模板不存在: {template_path}")
                return None
            
            # 创建输出文件
            company_short = company_data.get('company_short_name', '卓尔')
            output_path = self.test_output_dir / f"落实情况表_{company_short}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            shutil.copy2(template_path, output_path)
            
            # 打开目标文档
            doc = docx.Document(output_path)
            
            print("\n📝 开始更新三段关键内容...")
            
            # 定义三段具体内容
            content_updates = {
                # 1. 持续条件内容
                "持续条件": {
                    "search_text": "单户综合融资总量方案申报书中列明的持续条件及落实情况",
                    "new_content": """持续条件落实情况：
1. 持续的评级水平：在我行单户综合融资总量有效期内，我行内部评级不得低于12级，若客户评级低于11级，应符合相关要求。当前公司有效评级为10级，科创评级为T5级，符合要求。
2. 持续的资产负债率水平：分阶段调整，A+轮融资到账前不高于75%，到账后不高于65%。
3. 持续的流动性水平：分阶段调整，A+轮融资到账前不低于1，到账后不低于1.1。
4. 持续的或有负债水平：在单户综合融资总量有效期内，客户对集团外企业新增或有负债、对集团外企业提供担保，需提前告知我行。
5. 持续的股权结构：在单户综合融资总量有效期内，客户股权结构或主要管理者若发生重大调整，我行将报有权审批机构变更方案或重检。
6. 对盈利能力的要求：在单户综合融资总量有效期内，我行将持续关注客户盈利能力的变化，由于客户目前处于高速发展期，故暂不对盈利能力进行限制。
7. 对长期投资的限制：在单户综合融资总量有效期内，客户对其并表范围外的公司进行对外投资需征得我行同意。
8. 对发行优先权债务的限制：在单户综合融资总量有效期内，客户在他行同类型新增贷款的担保方式不得强于我行。
9. 对账户管理要求：客户在我行开立存款账户，且将贷转存账户设置网银受控。
10. 其他条件：本次新增贷款的最终支用日不晚于2025年9月30日；A+轮融资到账前的临时性条款有效期最长不超过6个月；分阶段调整有息负债总金额，A+轮融资到账前不高于7500万元，到账后不高于2.2亿元。"""
                },
                
                # 2. 前提条件内容
                "前提条件": {
                    "search_text": "单户综合融资总量方案申报书中列明的用信前提条件及落实情况",
                    "new_content": """前提条件落实情况：
流动资金贷款（及可串用该额度的其他业务品种）：在我行流动资金贷款支用时，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。我行将在每次放款前审核落实。"""
                },
                
                # 3. 担保措施落实计划内容
                "担保措施": {
                    "search_text": "单笔业务申报书中列明的贷款条件及落实情况",
                    "new_content": """担保措施落实计划：
1. 担保措施落实计划：担保方式为信用，追加公司实际控制人提供连带责任保证及部分专利产权质押作为风险缓释措施。我行将在放款前落实实控人担保情况及部分专利质押情况。
2. 贸易背景条件：作为流动资金贷款的支用前提，公司须提供其在手订单清单，并满足以下条件：该等订单的指定收款账户须为我行账户，且其项下尚未收回的应收账款总额，不得低于我行届时全部贷款余额（含本次拟发放金额）的1.2倍。
3. 存量压缩条件（如有）：/
4. 支付方式条件：/
5. 账户管理措施：在放款前对我行贷转存账户设置网银受控"""
                }
            }
            
            # 基础信息替换
            current_date = datetime.now()
            contract_number = f"建八{company_short}（2025）001号"
            
            basic_replacements = {
                '【公司名称】': company_data.get('company_name', ''),
                '【当前年份】': str(current_date.year),
                '【当前月份】': str(current_date.month),
                '【支用金额万元】': '10000000',
                '【支用金额】': '1000',
                '【合同编号】': contract_number,
                '【合同编号前缀】': f"建八{company_short}",
                '【合同编号后缀】': "（2025）001号",
                '【审批文号额度】': 'PIFU510000000N202407210',
                '【审批文号业务】': 'PIFU5100000002025N00G8',
                '【贷款类型】': '流动资金贷款',
                '【合同金额】': '2000',
                '【贷款期限】': '13个月',
                '【有效期开始】': '2024-03-06',
                '【有效期结束】': '2025-03-06'
            }
            
            # 执行基础信息替换
            replacement_count = 0
            content_update_count = 0
            
            # 替换段落中的内容
            for para in doc.paragraphs:
                # 基础信息替换
                for placeholder, value in basic_replacements.items():
                    if placeholder in para.text:
                        self.replace_text_in_paragraph_with_green(para, placeholder, str(value))
                        replacement_count += 1
                
                # 三段内容更新
                for content_type, content_info in content_updates.items():
                    if content_info["search_text"] in para.text:
                        print(f"✅ 找到{content_type}段落，开始更新...")
                        self.replace_text_in_paragraph_with_green(para, content_info["search_text"], content_info["new_content"])
                        content_update_count += 1
            
            # 替换表格中的内容
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        # 基础信息替换
                        for para in cell.paragraphs:
                            for placeholder, value in basic_replacements.items():
                                if placeholder in para.text:
                                    self.replace_text_in_paragraph_with_green(para, placeholder, str(value))
                                    replacement_count += 1
                        
                        # 三段内容更新
                        cell_text = cell.text.strip()
                        for content_type, content_info in content_updates.items():
                            if content_info["search_text"] in cell_text:
                                print(f"✅ 找到{content_type}表格单元格，开始更新...")
                                # 清空单元格并添加新内容
                                cell.text = ""
                                para = cell.paragraphs[0]
                                green_run = para.add_run(content_info["new_content"])
                                green_run.font.color.rgb = self.green_rgb
                                content_update_count += 1
            
            print(f"✅ 基础信息替换完成，共替换 {replacement_count} 处")
            print(f"✅ 三段内容更新完成，共更新 {content_update_count} 处")
            
            # 保存文档
            doc.save(output_path)
            
            print(f"\n✅ 落实情况表更新完成: {output_path.name}")
            print(f"📝 更新内容:")
            print(f"  ✅ 基础信息替换 (绿色标记)")
            print(f"  ✅ 持续条件内容更新 (绿色标记)")
            print(f"  ✅ 前提条件内容更新 (绿色标记)")
            print(f"  ✅ 担保措施落实计划更新 (绿色标记)")
            print(f"📁 文件位置: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            import traceback
            traceback.print_exc()
            return None

def main():
    """主函数"""
    updater = ImplementationTableUpdater()
    
    print("=" * 80)
    print("📋 落实情况表模板更新器")
    print("=" * 80)
    print("🎯 用户要求:")
    print("  ✅ 更新现有的落实情况表.docx模板")
    print("  ✅ 填入中科卓尔的具体信息")
    print("  ✅ 更新三段关键内容:")
    print("    1. 持续条件及落实情况")
    print("    2. 前提条件及落实情况")
    print("    3. 担保措施落实计划")
    print("=" * 80)
    
    result = updater.update_implementation_table_content()
    
    if result:
        print(f"\n🎉 落实情况表更新完成！")
        print(f"📄 文件位置: {result}")
        print(f"📝 请检查:")
        print(f"  ✅ 基础信息是否正确填入")
        print(f"  ✅ 三段内容是否符合要求")
        print(f"  ✅ 绿色标记是否正确")
    else:
        print(f"\n❌ 更新失败")

if __name__ == "__main__":
    main()
