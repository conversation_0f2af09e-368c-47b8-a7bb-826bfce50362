#!/usr/bin/env python3
"""
精确提取您截图中显示的三个表格内容
"""

import docx
from pathlib import Path

def analyze_documents_precisely():
    quota_file = Path('templates/contract_disbursement/额度申报书.docx')
    business_file = Path('templates/contract_disbursement/业务申报书.docx')
    
    print('=== 精确分析源文档中的表格 ===\n')
    
    # 分析额度申报书
    print('【额度申报书分析】')
    analyze_quota_document(quota_file)
    
    print('\n' + '='*80 + '\n')
    
    # 分析业务申报书
    print('【业务申报书分析】')
    analyze_business_document(business_file)

def analyze_quota_document(file_path):
    """分析额度申报书，寻找用信前提条件和持续条件表格"""
    if not file_path.exists():
        print('额度申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    print(f'额度申报书包含 {len(doc.tables)} 个表格\n')
    
    # 寻找包含"用信前提条件"的表格
    print('🔍 寻找"用信前提条件"表格：')
    for table_idx, table in enumerate(doc.tables):
        found_precondition = False
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if '用信前提条件' in text or '前提条件' in text:
                    print(f'表格{table_idx+1} - 行{row_idx+1} - 列{cell_idx+1}: 找到相关内容')
                    print(f'内容: {text[:100]}...')
                    found_precondition = True
                    
                    # 显示整个表格结构
                    print(f'\n表格{table_idx+1}完整内容:')
                    display_table_content(table, table_idx+1)
                    break
            if found_precondition:
                break
        if found_precondition:
            break
    
    print('\n' + '-'*60 + '\n')
    
    # 寻找包含"持续条件"的表格
    print('🔍 寻找"持续条件"表格：')
    for table_idx, table in enumerate(doc.tables):
        found_continuous = False
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if '持续条件' in text and '单户' in text:
                    print(f'表格{table_idx+1} - 行{row_idx+1} - 列{cell_idx+1}: 找到相关内容')
                    print(f'内容: {text[:100]}...')
                    found_continuous = True
                    
                    # 显示整个表格结构
                    print(f'\n表格{table_idx+1}完整内容:')
                    display_table_content(table, table_idx+1)
                    break
            if found_continuous:
                break
        if found_continuous:
            break

def analyze_business_document(file_path):
    """分析业务申报书，寻找贷款条件表格"""
    if not file_path.exists():
        print('业务申报书文件不存在')
        return
    
    doc = docx.Document(file_path)
    print(f'业务申报书包含 {len(doc.tables)} 个表格\n')
    
    # 寻找包含"贷款条件"的表格
    print('🔍 寻找"贷款条件"表格：')
    for table_idx, table in enumerate(doc.tables):
        found_loan_condition = False
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                text = cell.text.strip()
                if '贷款条件' in text or ('贸易背景' in text and '条件' in text):
                    print(f'表格{table_idx+1} - 行{row_idx+1} - 列{cell_idx+1}: 找到相关内容')
                    print(f'内容: {text[:100]}...')
                    found_loan_condition = True
                    
                    # 显示整个表格结构
                    print(f'\n表格{table_idx+1}完整内容:')
                    display_table_content(table, table_idx+1)
                    break
            if found_loan_condition:
                break
        if found_loan_condition:
            break

def display_table_content(table, table_num):
    """显示表格的完整内容"""
    print(f'表格{table_num} ({len(table.rows)}行 x {len(table.columns)}列):')
    
    for row_idx, row in enumerate(table.rows):
        print(f'\n  行{row_idx+1}:')
        for cell_idx, cell in enumerate(row.cells):
            cell_text = cell.text.strip()
            if cell_text:
                # 限制显示长度，但保持完整性
                if len(cell_text) > 200:
                    display_text = cell_text[:200] + '...'
                else:
                    display_text = cell_text
                print(f'    列{cell_idx+1}: {display_text}')
    print()

if __name__ == "__main__":
    analyze_documents_precisely()
