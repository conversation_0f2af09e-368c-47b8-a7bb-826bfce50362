#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业服务系统 - 桌面版主程序
三步骤流程：1.选择客户 -> 2.选择模块 -> 3.生成文件
"""

import sys
import sqlite3
from pathlib import Path
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QGridLayout, QStackedWidget, QLabel, QPushButton, QListWidget, 
    QListWidgetItem, QLineEdit, QTextEdit, QProgressBar, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QStatusBar, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

class DataManager:
    """数据管理器 - 处理数据库操作"""
    
    def __init__(self):
        self.db_path = Path(__file__).parent / "database" / "enterprise_service.db"
        
    def get_all_customers(self):
        """获取所有客户 - 适配companies表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, company_name, legal_representative, contact_phone,
                       registered_address, industry_category, business_description,
                       finance_manager_name, finance_manager_phone, account_manager,
                       account_manager_phone, company_short_name
                FROM companies
                WHERE business_status = 'active'
                ORDER BY company_name
            """)

            customers = []
            for row in cursor.fetchall():
                customers.append({
                    'id': row[0],
                    'name': row[1],  # company_name
                    'contact_person': row[2] or row[7],  # legal_representative or finance_manager_name
                    'phone': row[3] or row[8],  # contact_phone or finance_manager_phone
                    'address': row[4],  # registered_address
                    'industry': row[5],  # industry_category
                    'description': row[6],  # business_description
                    'finance_manager': row[7],  # finance_manager_name
                    'finance_phone': row[8],  # finance_manager_phone
                    'account_manager': row[9],  # account_manager
                    'account_phone': row[10],  # account_manager_phone
                    'short_name': row[11]  # company_short_name
                })

            conn.close()
            return customers

        except Exception as e:
            print(f"获取客户数据失败: {e}")
            return []
    
    def add_customer(self, customer_data):
        """添加新客户 - 适配companies表"""
        try:
            import uuid
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            customer_id = str(uuid.uuid4())
            cursor.execute("""
                INSERT INTO companies (
                    id, company_name, legal_representative, contact_phone,
                    registered_address, industry_category, business_status,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, 'active', datetime('now'), datetime('now'))
            """, (
                customer_id,
                customer_data['name'],
                customer_data['contact_person'],
                customer_data['phone'],
                customer_data['address'],
                customer_data['industry']
            ))

            conn.commit()
            conn.close()

            customer_data['id'] = customer_id
            return customer_data

        except Exception as e:
            print(f"添加客户失败: {e}")
            return None

class CustomerSelectionWidget(QWidget):
    """步骤1: 客户选择页面"""
    customer_selected = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.data_manager = DataManager()
        self.setup_ui()
        self.load_customers()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("步骤 1: 选择客户")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索客户:")
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("输入客户名称或联系人...")
        self.search_box.textChanged.connect(self.filter_customers)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_box)
        
        # 客户列表
        self.customer_list = QListWidget()
        self.customer_list.setMinimumHeight(300)
        self.customer_list.itemDoubleClicked.connect(self.select_customer)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        self.add_customer_btn = QPushButton("+ 新增客户")
        self.add_customer_btn.clicked.connect(self.add_new_customer)
        self.select_btn = QPushButton("选择此客户")
        self.select_btn.clicked.connect(self.select_customer)
        self.select_btn.setEnabled(False)
        
        button_layout.addWidget(self.add_customer_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.select_btn)
        
        # 客户信息显示
        self.customer_info = QTextEdit()
        self.customer_info.setMaximumHeight(100)
        self.customer_info.setPlaceholderText("选择客户后显示详细信息...")
        
        # 布局
        layout.addWidget(title)
        layout.addLayout(search_layout)
        layout.addWidget(QLabel("客户列表:"))
        layout.addWidget(self.customer_list)
        layout.addWidget(QLabel("客户信息:"))
        layout.addWidget(self.customer_info)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 连接选择事件
        self.customer_list.itemClicked.connect(self.show_customer_info)
        
    def load_customers(self):
        """加载客户列表"""
        self.customers = self.data_manager.get_all_customers()
        self.display_customers(self.customers)
        
    def display_customers(self, customers):
        """显示客户列表"""
        self.customer_list.clear()
        for customer in customers:
            item_text = f"{customer['name']} - {customer['contact_person']} ({customer['phone']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, customer)
            self.customer_list.addItem(item)
            
    def filter_customers(self, text):
        """过滤客户列表"""
        if not text:
            self.display_customers(self.customers)
            return
            
        filtered = []
        for customer in self.customers:
            if (text.lower() in customer['name'].lower() or 
                text.lower() in customer['contact_person'].lower()):
                filtered.append(customer)
        
        self.display_customers(filtered)
        
    def show_customer_info(self, item):
        """显示客户详细信息"""
        customer = item.data(Qt.UserRole)
        info_text = f"""
客户名称: {customer['name']}
联系人: {customer['contact_person']}
联系电话: {customer['phone']}
地址: {customer['address']}
行业: {customer['industry']}
财务经理: {customer.get('finance_manager', '未设置')}
财务电话: {customer.get('finance_phone', '未设置')}
客户经理: {customer.get('account_manager', '未设置')}
客户经理电话: {customer.get('account_phone', '未设置')}
        """
        self.customer_info.setText(info_text.strip())
        self.select_btn.setEnabled(True)
        
    def select_customer(self, item=None):
        """选择客户"""
        if item is None:
            item = self.customer_list.currentItem()
            
        if item:
            customer = item.data(Qt.UserRole)
            self.customer_selected.emit(customer)
            
    def add_new_customer(self):
        """添加新客户"""
        dialog = AddCustomerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            customer_data = dialog.get_customer_data()
            new_customer = self.data_manager.add_customer(customer_data)
            if new_customer:
                self.load_customers()
                QMessageBox.information(self, "成功", "客户添加成功！")
            else:
                QMessageBox.warning(self, "错误", "客户添加失败！")

class AddCustomerDialog(QDialog):
    """添加客户对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加新客户")
        self.setModal(True)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 表单
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.contact_edit = QLineEdit()
        self.phone_edit = QLineEdit()
        self.address_edit = QLineEdit()
        self.industry_edit = QLineEdit()
        
        form_layout.addRow("客户名称*:", self.name_edit)
        form_layout.addRow("联系人*:", self.contact_edit)
        form_layout.addRow("联系电话:", self.phone_edit)
        form_layout.addRow("地址:", self.address_edit)
        form_layout.addRow("行业:", self.industry_edit)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        layout.addLayout(form_layout)
        layout.addWidget(button_box)
        self.setLayout(layout)
        
    def get_customer_data(self):
        """获取客户数据"""
        return {
            'name': self.name_edit.text().strip(),
            'contact_person': self.contact_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'address': self.address_edit.text().strip(),
            'industry': self.industry_edit.text().strip()
        }
        
    def accept(self):
        """验证并接受"""
        data = self.get_customer_data()
        if not data['name'] or not data['contact_person']:
            QMessageBox.warning(self, "错误", "客户名称和联系人为必填项！")
            return
        super().accept()

class ModuleSelectionWidget(QWidget):
    """步骤2: 模块选择页面"""
    module_selected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_customer = None
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("步骤 2: 选择业务模块")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        
        # 客户信息显示
        self.customer_info = QLabel("当前客户: 未选择")
        self.customer_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        
        # 模块选择区域
        modules_widget = QWidget()
        modules_layout = QGridLayout()
        
        # 四大模块定义
        self.modules = {
            'yingqi_zhilian': {
                'name': '银企直联',
                'icon': '🏦',
                'description': '生成服务协议、申请表单、授权文档'
            },
            'customer_engagement': {
                'name': '客户接洽准备',
                'icon': '👥',
                'description': '客户资料管理、接洽清单、服务方案'
            },
            'contract_disbursement': {
                'name': '合同支用',
                'icon': '📋',
                'description': '落实情况表、业务申报、合同制作'
            },
            'deposit_services': {
                'name': '协定存款业务',
                'icon': '💰',
                'description': '协定存款合同、条件管理、格式验证'
            }
        }
        
        # 创建模块按钮
        for i, (key, module) in enumerate(self.modules.items()):
            btn = self.create_module_button(key, module)
            row, col = divmod(i, 2)
            modules_layout.addWidget(btn, row, col)
        
        modules_widget.setLayout(modules_layout)
        
        # 布局
        layout.addWidget(title)
        layout.addWidget(self.customer_info)
        layout.addWidget(QLabel("请选择业务模块:"))
        layout.addWidget(modules_widget)
        layout.addStretch()
        
        self.setLayout(layout)
        
    def create_module_button(self, key, module):
        """创建模块选择按钮"""
        btn = QPushButton()
        btn.setFixedSize(280, 120)
        btn.setText(f"{module['icon']}\n{module['name']}\n{module['description']}")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-size: 12px;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #2196F3;
            }
            QPushButton:pressed {
                background-color: #2196F3;
                color: white;
            }
        """)
        btn.clicked.connect(lambda: self.select_module(key))
        return btn
        
    def select_module(self, module_key):
        """选择模块"""
        self.module_selected.emit(module_key)
        
    def set_customer(self, customer):
        """设置当前客户"""
        self.current_customer = customer
        self.customer_info.setText(f"当前客户: {customer['name']} - {customer['contact_person']}")

class FileGenerationWidget(QWidget):
    """步骤3: 文件生成页面"""
    
    def __init__(self):
        super().__init__()
        self.current_customer = None
        self.current_module = None
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("步骤 3: 生成文件")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2196F3; margin: 20px;")
        
        # 信息显示
        self.customer_info = QLabel("当前客户: 未选择")
        self.module_info = QLabel("当前模块: 未选择")
        
        info_style = "background-color: #f0f0f0; padding: 8px; border-radius: 5px; margin: 2px;"
        self.customer_info.setStyleSheet(info_style)
        self.module_info.setStyleSheet(info_style)
        
        # 文件类型列表
        self.file_list = QListWidget()
        self.file_list.setMinimumHeight(200)
        
        # 生成按钮
        self.generate_btn = QPushButton("生成选中文档")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 12px;
                border-radius: 6px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.generate_btn.clicked.connect(self.generate_document)
        self.generate_btn.setEnabled(False)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 结果显示
        self.result_area = QTextEdit()
        self.result_area.setMaximumHeight(120)
        self.result_area.setPlaceholderText("生成结果将在这里显示...")
        
        # 布局
        layout.addWidget(title)
        layout.addWidget(self.customer_info)
        layout.addWidget(self.module_info)
        layout.addWidget(QLabel("可生成的文件类型:"))
        layout.addWidget(self.file_list)
        layout.addWidget(self.generate_btn)
        layout.addWidget(self.progress_bar)
        layout.addWidget(QLabel("生成结果:"))
        layout.addWidget(self.result_area)
        
        self.setLayout(layout)
        
        # 连接事件
        self.file_list.itemClicked.connect(self.enable_generate_button)
        
    def set_customer(self, customer):
        """设置当前客户"""
        self.current_customer = customer
        self.customer_info.setText(f"当前客户: {customer['name']} - {customer['contact_person']}")
        
    def set_module(self, module_key):
        """设置当前模块并加载文件类型"""
        self.current_module = module_key
        module_names = {
            'yingqi_zhilian': '银企直联',
            'customer_engagement': '客户接洽准备',
            'contract_disbursement': '合同支用',
            'deposit_services': '协定存款业务'
        }
        self.module_info.setText(f"当前模块: {module_names.get(module_key, module_key)}")
        self.load_file_types(module_key)
        
    def load_file_types(self, module_key):
        """根据模块加载可生成的文件类型"""
        file_types = {
            'yingqi_zhilian': [
                '银企直联服务协议',
                '申请表单',
                '授权表单',
                'OA文本蓝图'
            ],
            'customer_engagement': [
                '客户接洽检查清单',
                '托管函',
                '服务方案PPT'
            ],
            'contract_disbursement': [
                '落实情况表',
                '业务申报书',
                '信贷业务申请书',
                '合同制作清单',
                '提款申请书'
            ],
            'deposit_services': [
                '协定存款合同',
                '条件汇总表',
                '格式验证报告'
            ]
        }
        
        self.file_list.clear()
        for file_type in file_types.get(module_key, []):
            item = QListWidgetItem(file_type)
            self.file_list.addItem(item)
            
    def enable_generate_button(self):
        """启用生成按钮"""
        self.generate_btn.setEnabled(True)
        
    def generate_document(self):
        """生成文档 - 集成真实文档生成器"""
        current_item = self.file_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择要生成的文件类型！")
            return

        file_type = current_item.text()

        # 显示进度
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.generate_btn.setEnabled(False)

        try:
            # 调用真实的文档生成器
            output_path, summary = self.call_real_generator(file_type)

            if output_path:
                result_text = f"""
✅ 生成成功！
客户: {self.current_customer['name']}
模块: {self.current_module}
文件类型: {file_type}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
输出路径: {output_path}

📊 生成详情:
{self.format_summary(summary)}

🎉 文件已保存到 test_output 目录！
                """
                self.result_area.setText(result_text.strip())

                # 询问是否打开文件
                reply = QMessageBox.question(self, "生成成功",
                                           f"文档生成成功！\n文件位置: {output_path}\n\n是否打开文件？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.Yes:
                    self.open_generated_file(output_path)
            else:
                self.result_area.setText("❌ 生成失败：未知错误")

        except Exception as e:
            error_text = f"""
❌ 生成失败！
错误信息: {str(e)}
客户: {self.current_customer['name']}
模块: {self.current_module}
文件类型: {file_type}
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 建议检查:
1. 模板文件是否存在
2. 客户数据是否完整
3. 输出目录权限是否正确
            """
            self.result_area.setText(error_text.strip())
            QMessageBox.critical(self, "生成失败", f"文档生成失败：\n{str(e)}")

        finally:
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            self.generate_btn.setEnabled(True)

    def call_real_generator(self, file_type):
        """调用真实的文档生成器"""
        company_id = self.current_customer['id']

        # 根据模块和文件类型调用对应的生成器
        if self.current_module == 'deposit_services':
            return self.generate_deposit_document(company_id, file_type)
        elif self.current_module == 'contract_disbursement':
            return self.generate_contract_document(company_id, file_type)
        elif self.current_module == 'yingqi_zhilian':
            return self.generate_yingqi_document(company_id, file_type)
        elif self.current_module == 'customer_engagement':
            return self.generate_customer_document(company_id, file_type)
        else:
            raise ValueError(f"不支持的模块: {self.current_module}")

    def generate_deposit_document(self, company_id, file_type):
        """生成协定存款文档"""
        if file_type == '协定存款合同':
            # 导入协定存款生成器
            import sys
            sys.path.append(str(Path(__file__).parent))
            from deposit_services.generators.agreed_deposit_generator import AgreedDepositGenerator

            generator = AgreedDepositGenerator()
            return generator.generate_deposit_agreement(company_id, deposit_amount=1000)
        else:
            raise NotImplementedError(f"协定存款模块暂不支持: {file_type}")

    def generate_contract_document(self, company_id, file_type):
        """生成合同支用文档"""
        if file_type == '落实情况表':
            # 导入合同支用生成器
            import sys
            sys.path.append(str(Path(__file__).parent))
            from contract_disbursement.generator import ContractDisbursementGenerator

            generator = ContractDisbursementGenerator()
            return generator.generate_contract_disbursement(
                company_id=company_id,
                document_type='condition_checklist',
                loan_amount=1500
            )
        else:
            raise NotImplementedError(f"合同支用模块暂不支持: {file_type}")

    def generate_yingqi_document(self, company_id, file_type):
        """生成银企直联文档"""
        # TODO: 集成银企直联生成器
        raise NotImplementedError(f"银企直联模块暂不支持: {file_type}")

    def generate_customer_document(self, company_id, file_type):
        """生成客户接洽文档"""
        # TODO: 集成客户接洽生成器
        raise NotImplementedError(f"客户接洽模块暂不支持: {file_type}")

    def format_summary(self, summary):
        """格式化生成摘要"""
        if not summary:
            return "无详细信息"

        formatted = []
        for key, value in summary.items():
            if key == 'company_name':
                formatted.append(f"企业名称: {value}")
            elif key == 'total_replacements':
                formatted.append(f"替换字段: {value}个")
            elif key == 'template_type':
                formatted.append(f"模板类型: {value}")
            elif key == 'output_path':
                continue  # 已在上面显示
            else:
                formatted.append(f"{key}: {value}")

        return "\n".join(formatted)

    def open_generated_file(self, file_path):
        """打开生成的文件"""
        try:
            import os
            import subprocess

            if os.path.exists(file_path):
                # Windows系统使用默认程序打开
                os.startfile(file_path)
            else:
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
        except Exception as e:
            QMessageBox.warning(self, "警告", f"无法打开文件: {str(e)}")

class NavigationWidget(QWidget):
    """导航控制器"""
    step_changed = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        self.current_step = 0
        self.setup_ui()

    def setup_ui(self):
        layout = QHBoxLayout()

        # 步骤指示器
        self.step_indicators = []
        steps = ["1. 选择客户", "2. 选择模块", "3. 生成文件"]

        for i, step in enumerate(steps):
            indicator = QLabel(step)
            indicator.setFixedSize(120, 40)
            indicator.setAlignment(Qt.AlignCenter)
            indicator.setStyleSheet("""
                QLabel {
                    border: 2px solid #dee2e6;
                    border-radius: 5px;
                    background-color: #f8f9fa;
                    margin: 2px;
                }
            """)
            self.step_indicators.append(indicator)
            layout.addWidget(indicator)

        layout.addStretch()

        # 导航按钮
        self.prev_btn = QPushButton("← 上一步")
        self.next_btn = QPushButton("下一步 →")

        button_style = """
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
        """
        self.prev_btn.setStyleSheet(button_style + "background-color: #6c757d; color: white;")
        self.next_btn.setStyleSheet(button_style + "background-color: #007bff; color: white;")

        self.prev_btn.clicked.connect(self.prev_step)
        self.next_btn.clicked.connect(self.next_step)

        layout.addWidget(self.prev_btn)
        layout.addWidget(self.next_btn)

        self.setLayout(layout)
        self.update_step_indicators()

    def update_step_indicators(self):
        """更新步骤指示器"""
        for i, indicator in enumerate(self.step_indicators):
            if i == self.current_step:
                indicator.setStyleSheet("""
                    QLabel {
                        border: 2px solid #007bff;
                        border-radius: 5px;
                        background-color: #007bff;
                        color: white;
                        font-weight: bold;
                        margin: 2px;
                    }
                """)
            elif i < self.current_step:
                indicator.setStyleSheet("""
                    QLabel {
                        border: 2px solid #28a745;
                        border-radius: 5px;
                        background-color: #28a745;
                        color: white;
                        margin: 2px;
                    }
                """)
            else:
                indicator.setStyleSheet("""
                    QLabel {
                        border: 2px solid #dee2e6;
                        border-radius: 5px;
                        background-color: #f8f9fa;
                        margin: 2px;
                    }
                """)

        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_step > 0)
        self.next_btn.setEnabled(self.current_step < 2)

    def prev_step(self):
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self.update_step_indicators()
            self.step_changed.emit(self.current_step)

    def next_step(self):
        """下一步"""
        if self.current_step < 2:
            self.current_step += 1
            self.update_step_indicators()
            self.step_changed.emit(self.current_step)

    def set_step(self, step):
        """设置当前步骤"""
        if 0 <= step <= 2:
            self.current_step = step
            self.update_step_indicators()

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.current_customer = None
        self.current_module = None
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("企业服务系统 - 桌面版")
        self.setGeometry(100, 100, 900, 700)

        # 中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题栏
        title_label = QLabel("企业服务系统")
        title_label.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2196F3; margin: 20px; padding: 10px;")

        # 堆叠窗口 - 三个步骤页面
        self.stacked_widget = QStackedWidget()

        # 创建三个页面
        self.customer_page = CustomerSelectionWidget()
        self.module_page = ModuleSelectionWidget()
        self.generation_page = FileGenerationWidget()

        # 添加到堆叠窗口
        self.stacked_widget.addWidget(self.customer_page)
        self.stacked_widget.addWidget(self.module_page)
        self.stacked_widget.addWidget(self.generation_page)

        # 导航控制器
        self.navigation = NavigationWidget()

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)

        # 布局
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.stacked_widget)
        main_layout.addWidget(separator)
        main_layout.addWidget(self.navigation)

        central_widget.setLayout(main_layout)

        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("欢迎使用企业服务系统！请选择客户开始...")

    def setup_connections(self):
        """设置信号连接"""
        # 导航控制
        self.navigation.step_changed.connect(self.change_step)

        # 客户选择
        self.customer_page.customer_selected.connect(self.on_customer_selected)

        # 模块选择
        self.module_page.module_selected.connect(self.on_module_selected)

    def change_step(self, step):
        """切换步骤"""
        self.stacked_widget.setCurrentIndex(step)

        # 更新状态栏
        status_messages = [
            "请选择客户...",
            "请选择业务模块...",
            "请选择要生成的文件类型..."
        ]
        self.status_bar.showMessage(status_messages[step])

    def on_customer_selected(self, customer):
        """客户选择完成"""
        self.current_customer = customer
        self.module_page.set_customer(customer)
        self.generation_page.set_customer(customer)

        # 自动跳转到下一步
        self.navigation.next_step()
        self.status_bar.showMessage(f"已选择客户: {customer['name']}")

    def on_module_selected(self, module_key):
        """模块选择完成"""
        self.current_module = module_key
        self.generation_page.set_module(module_key)

        # 自动跳转到下一步
        self.navigation.next_step()

        module_names = {
            'yingqi_zhilian': '银企直联',
            'customer_engagement': '客户接洽准备',
            'contract_disbursement': '合同支用',
            'deposit_services': '协定存款业务'
        }
        self.status_bar.showMessage(f"已选择模块: {module_names.get(module_key, module_key)}")

class MainApplication(QApplication):
    """主应用程序"""

    def __init__(self):
        super().__init__(sys.argv)
        self.setApplicationName("企业服务系统")
        self.setApplicationVersion("1.0.0")
        self.setup_style()

        # 创建主窗口
        self.main_window = MainWindow()

    def setup_style(self):
        """设置应用样式"""
        style = """
        QMainWindow {
            background-color: #ffffff;
        }

        QWidget {
            font-family: 'Microsoft YaHei';
            font-size: 11px;
        }

        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #0056b3;
        }

        QPushButton:pressed {
            background-color: #004085;
        }

        QPushButton:disabled {
            background-color: #6c757d;
        }

        QLineEdit {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
        }

        QLineEdit:focus {
            border-color: #007bff;
            outline: none;
        }

        QListWidget {
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
        }

        QListWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        QListWidget::item:selected {
            background-color: #007bff;
            color: white;
        }

        QListWidget::item:hover {
            background-color: #f8f9fa;
        }

        QTextEdit {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
            font-family: 'Consolas', monospace;
        }
        """
        self.setStyleSheet(style)

    def run(self):
        """运行应用"""
        self.main_window.show()
        return self.exec_()

def main():
    """主函数"""
    app = MainApplication()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
