#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模板文件中的占位符格式
"""

import docx
from pathlib import Path

def check_template_placeholders():
    """检查模板中的占位符"""
    template_path = Path("templates/contract_disbursement/disbursement_condition_checklist_blueprint.docx")
    
    print("🔍 检查模板文件中的占位符...")
    print(f"📁 模板文件: {template_path}")
    print("="*60)
    
    try:
        doc = docx.Document(template_path)
        
        print("📋 段落中的内容:")
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text and ("还款" in text or "{{" in text or "}}" in text or "来源" in text):
                print(f"   段落{i}: {text}")

        print("\n📊 表格中的内容:")
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    text = cell.text.strip()
                    if text and ("还款" in text or "{{" in text or "}}" in text or "来源" in text or "审查" in text):
                        print(f"   表格{table_idx}-行{row_idx}-列{cell_idx}: {text}")

        print("\n🔍 查找所有可能的插入位置:")
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    text = cell.text.strip()
                    if text and len(text) > 5 and len(text) < 50:
                        if any(keyword in text for keyword in ["还款", "来源", "审查", "落实", "情况"]):
                            print(f"   可能位置-表格{table_idx}-行{row_idx}-列{cell_idx}: {text}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_template_placeholders()
