#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析申报书文档，确定需要提取的信息和格式
"""

from docx import Document
from pathlib import Path
import json

class ApplicationAnalyzer:
    """申报书文档分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.quota_application = self.project_root / "templates" / "contract_disbursement" / "额度申报书.docx"
        self.business_application = self.project_root / "templates" / "contract_disbursement" / "业务申报书.doc"
        
    def analyze_document_structure(self, doc_path):
        """分析文档结构"""
        print(f"\n📋 分析文档: {doc_path.name}")
        print("=" * 60)
        
        if not doc_path.exists():
            print(f"❌ 文件不存在: {doc_path}")
            return
        
        try:
            doc = Document(doc_path)
            
            # 分析段落
            print("📝 段落内容分析:")
            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():
                    text = para.text.strip()
                    if len(text) > 100:
                        text = text[:100] + "..."
                    print(f"  {i+1:2d}. {text}")
                    
                    # 标记关键段落
                    if any(keyword in para.text for keyword in [
                        "前提条件", "持续条件", "贷款条件", "用信条件"
                    ]):
                        print(f"      ⭐ 关键段落!")
            
            # 分析表格
            print(f"\n📊 表格结构分析:")
            for i, table in enumerate(doc.tables):
                print(f"\n  表格 {i+1}: {len(table.rows)}行 x {len(table.columns)}列")
                
                # 显示表格内容
                print(f"    表格内容预览:")
                for row_idx, row in enumerate(table.rows[:5]):  # 只显示前5行
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if len(cell_text) > 30:
                            cell_text = cell_text[:30] + "..."
                        row_data.append(cell_text)
                    print(f"      行{row_idx+1}: {' | '.join(row_data)}")
                
                if len(table.rows) > 5:
                    print(f"      ... (还有 {len(table.rows)-5} 行)")
                
                # 检查是否包含关键字
                table_text = self.get_table_text(table)
                keywords_found = []
                for keyword in ["前提条件", "持续条件", "贷款条件", "用信条件", "担保", "评级"]:
                    if keyword in table_text:
                        keywords_found.append(keyword)
                
                if keywords_found:
                    print(f"      ⭐ 包含关键字: {', '.join(keywords_found)}")
                    
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_table_text(self, table):
        """获取表格的所有文本内容"""
        text_parts = []
        try:
            for row in table.rows:
                for cell in row.cells:
                    text_parts.append(cell.text.strip())
        except:
            pass
        return " ".join(text_parts)
    
    def extract_table_structure(self, table, table_name):
        """提取表格结构"""
        print(f"\n🔍 详细分析表格: {table_name}")
        
        structure = {
            "table_name": table_name,
            "rows": len(table.rows),
            "columns": len(table.columns),
            "headers": [],
            "data": []
        }
        
        try:
            # 提取表头
            if table.rows:
                headers = []
                for cell in table.rows[0].cells:
                    headers.append(cell.text.strip())
                structure["headers"] = headers
                print(f"  表头: {headers}")
            
            # 提取数据行
            for row_idx, row in enumerate(table.rows[1:], 1):  # 跳过表头
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())
                structure["data"].append(row_data)
                
                if row_idx <= 3:  # 只显示前3行数据
                    print(f"  数据行{row_idx}: {row_data}")
            
            if len(table.rows) > 4:
                print(f"  ... (还有 {len(table.rows)-4} 行数据)")
                
        except Exception as e:
            print(f"❌ 提取表格结构失败: {e}")
        
        return structure
    
    def identify_target_tables(self, doc_path):
        """识别目标表格"""
        print(f"\n🎯 识别目标表格: {doc_path.name}")
        
        if not doc_path.exists():
            print(f"❌ 文件不存在")
            return []
        
        try:
            doc = Document(doc_path)
            target_tables = []
            
            for i, table in enumerate(doc.tables):
                table_text = self.get_table_text(table)
                
                # 判断表格类型
                table_type = None
                if "前提条件" in table_text or "流动资金" in table_text:
                    table_type = "前提条件表格"
                elif "持续条件" in table_text or "评级" in table_text:
                    table_type = "持续条件表格"
                elif "贷款条件" in table_text or "担保" in table_text:
                    table_type = "贷款条件表格"
                
                if table_type:
                    print(f"✅ 表格{i+1} 识别为: {table_type}")
                    structure = self.extract_table_structure(table, table_type)
                    target_tables.append(structure)
                else:
                    print(f"⚪ 表格{i+1} 不是目标表格")
            
            return target_tables
            
        except Exception as e:
            print(f"❌ 识别失败: {e}")
            return []
    
    def analyze_all_documents(self):
        """分析所有申报书文档"""
        print("🔍 申报书文档分析报告")
        print("=" * 80)
        
        # 分析额度申报书
        self.analyze_document_structure(self.quota_application)
        quota_tables = self.identify_target_tables(self.quota_application)
        
        # 分析业务申报书
        self.analyze_document_structure(self.business_application)
        business_tables = self.identify_target_tables(self.business_application)
        
        # 生成总结报告
        print(f"\n📊 分析总结")
        print("=" * 60)
        print(f"额度申报书目标表格数: {len(quota_tables)}")
        for table in quota_tables:
            print(f"  - {table['table_name']}: {table['rows']}行 x {table['columns']}列")
        
        print(f"业务申报书目标表格数: {len(business_tables)}")
        for table in business_tables:
            print(f"  - {table['table_name']}: {table['rows']}行 x {table['columns']}列")
        
        # 保存分析结果
        analysis_result = {
            "quota_application": {
                "file": str(self.quota_application),
                "tables": quota_tables
            },
            "business_application": {
                "file": str(self.business_application),
                "tables": business_tables
            }
        }
        
        output_file = self.project_root / "test_output" / "document_analysis.json"
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 分析结果已保存到: {output_file}")
        
        return analysis_result

def main():
    """主函数"""
    analyzer = ApplicationAnalyzer()
    result = analyzer.analyze_all_documents()
    
    print(f"\n🎯 提取信息确认:")
    print("=" * 60)
    print("需要提取的信息:")
    print("1. 前提条件表格 - 包含用信前提条件的具体内容")
    print("2. 持续条件表格 - 包含持续条件的具体要求") 
    print("3. 贷款条件表格 - 包含业务申报书中的贷款条件")
    print("\n存储格式:")
    print("- 表格结构: 行列数据")
    print("- 表头信息: 列标题")
    print("- 数据内容: 每行的具体数据")
    print("- 元数据: 公司名称、文件来源、创建时间")

if __name__ == "__main__":
    main()
