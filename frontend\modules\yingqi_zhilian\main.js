/**
 * 银企直联模块 - 独立插件
 * 符合外壳+插件架构的标准接口
 */

class YingqiZhilianModule {
    constructor() {
        this.container = null;
        this.customerData = null;
        this.eventListeners = [];
        this.workflowTasks = [
            { id: 'service-agreement', title: '生成《服务协议》', completed: false },
            { id: 'oa-document', title: '生成《OA正文》', completed: false },
            { id: 'authorization-form', title: '下载《对公客户授权及承诺书》', completed: true },
            { id: 'application-form', title: '下载《对公综合服务申请书》', completed: true }
        ];
    }

    /**
     * 标准初始化接口
     * @param {HTMLElement} containerElement - 外壳提供的容器
     * @param {Object} customerData - 外壳提供的客户数据
     */
    async init(containerElement, customerData) {
        console.log('🏦 银企直联模块初始化...', { containerElement, customerData });
        
        this.container = containerElement;
        this.customerData = customerData;
        
        try {
            // 渲染模块界面
            this.renderInterface();
            
            // 绑定事件
            this.bindEvents();
            
            console.log('✅ 银企直联模块初始化完成');
            
        } catch (error) {
            console.error('❌ 银企直联模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 标准销毁接口
     */
    async destroy() {
        console.log('🗑️ 银企直联模块销毁...');
        
        try {
            // 清除事件监听
            this.clearEventListeners();
            
            // 清空容器
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            // 重置状态
            this.container = null;
            this.customerData = null;
            
            console.log('✅ 银企直联模块销毁完成');
            
        } catch (error) {
            console.error('❌ 银企直联模块销毁失败:', error);
        }
    }

    renderInterface() {
        if (!this.container) {
            throw new Error('容器未初始化');
        }

        this.container.innerHTML = `
            <div class="yingqi-zhilian-module">
                <!-- 模块头部 -->
                <div class="module-header">
                    <div class="header-left">
                        <h2 class="module-title">🏦 银企直联服务</h2>
                        <p class="module-subtitle">为 ${this.customerData.company_name} 提供银企直联服务</p>
                    </div>
                    <div class="header-right">
                        <div class="progress-info">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <span class="progress-text" id="progress-text">0/4 已完成</span>
                        </div>
                    </div>
                </div>

                <!-- 业务流程列表 -->
                <div class="workflow-container">
                    <div class="workflow-list" id="workflow-list">
                        ${this.renderWorkflowItems()}
                    </div>
                </div>

                <!-- 状态反馈区域 -->
                <div class="status-feedback" id="status-feedback">
                    <div class="feedback-content">
                        <span class="feedback-icon">ℹ️</span>
                        <span class="feedback-text">请选择要执行的业务操作</span>
                    </div>
                </div>

                <!-- OA工作台模态窗口 -->
                <div id="oa-workspace-modal" class="oa-workspace-modal" style="display: none;">
                    <div class="oa-workspace-content">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>
        `;

        // 更新进度
        this.updateProgress();
    }

    renderWorkflowItems() {
        return this.workflowTasks.map(task => `
            <div class="workflow-item" data-task="${task.id}">
                <div class="task-info">
                    <div class="task-checkbox">
                        <input type="checkbox" id="task-${task.id}" class="task-check" ${task.completed ? 'checked' : ''}>
                        <label for="task-${task.id}" class="check-label"></label>
                    </div>
                    <div class="task-content">
                        <h3 class="task-title">${task.title}</h3>
                        <p class="task-description">${this.getTaskDescription(task.id)}</p>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="action-btn ${this.getActionButtonClass(task.id)}" 
                            data-action="${this.getTaskAction(task.id)}"
                            ${task.completed ? 'disabled' : ''}>
                        ${this.getActionButtonText(task.id)}
                    </button>
                </div>
            </div>
        `).join('');
    }

    getTaskDescription(taskId) {
        const descriptions = {
            'service-agreement': '为客户生成银企直联服务协议文档',
            'oa-document': '使用AI协作生成OA办公文档',
            'authorization-form': '标准PDF表单，已预先准备',
            'application-form': '标准PDF表单，已预先准备'
        };
        return descriptions[taskId] || '';
    }

    getTaskAction(taskId) {
        const actions = {
            'service-agreement': 'generate-agreement',
            'oa-document': 'generate-oa',
            'authorization-form': 'download-auth',
            'application-form': 'download-app'
        };
        return actions[taskId] || '';
    }

    getActionButtonClass(taskId) {
        if (taskId === 'service-agreement' || taskId === 'oa-document') {
            return 'primary';
        }
        return 'secondary';
    }

    getActionButtonText(taskId) {
        const texts = {
            'service-agreement': '生成协议',
            'oa-document': '生成文档',
            'authorization-form': '下载表单',
            'application-form': '下载表单'
        };
        return texts[taskId] || '执行';
    }

    bindEvents() {
        // 绑定任务按钮点击事件
        const actionButtons = this.container.querySelectorAll('.action-btn');
        actionButtons.forEach(button => {
            const clickHandler = (e) => {
                const action = e.target.getAttribute('data-action');
                if (action) {
                    this.handleTaskAction(action);
                }
            };
            button.addEventListener('click', clickHandler);
            this.eventListeners.push({ element: button, event: 'click', handler: clickHandler });
        });
    }

    async handleTaskAction(action) {
        console.log('🎯 执行任务:', action);
        
        try {
            switch (action) {
                case 'generate-agreement':
                    await this.generateServiceAgreement();
                    break;
                case 'generate-oa':
                    await this.openOAWorkspace();
                    break;
                case 'download-auth':
                    this.downloadAuthForm();
                    break;
                case 'download-app':
                    this.downloadAppForm();
                    break;
                default:
                    console.warn('未知操作:', action);
            }
        } catch (error) {
            console.error('❌ 任务执行失败:', error);
            this.updateStatusFeedback(`操作失败: ${error.message}`, 'error');
        }
    }

    async generateServiceAgreement() {
        this.updateStatusFeedback('正在生成《服务协议》...', 'processing');

        try {
            const response = await fetch('http://localhost:5000/api/documents/generate_agreement', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ company_id: this.customerData.id })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.customerData.company_name}_服务协议.docx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.updateStatusFeedback('《服务协议》生成成功，已自动下载', 'success');
            this.markTaskCompleted('service-agreement');

        } catch (error) {
            console.error('生成服务协议失败:', error);
            this.updateStatusFeedback(`生成文档失败: ${error.message}`, 'error');
        }
    }

    async openOAWorkspace() {
        this.updateStatusFeedback('正在打开OA协作工作台...', 'processing');
        
        try {
            // 显示OA工作台模态窗口
            const modal = this.container.querySelector('#oa-workspace-modal');
            if (modal) {
                modal.style.display = 'flex';
                
                // 这里可以加载OA工作台的具体实现
                // 为了简化，暂时显示一个占位符
                const modalContent = modal.querySelector('.oa-workspace-content');
                modalContent.innerHTML = `
                    <div class="oa-workspace-placeholder">
                        <h3>🤖 OA正文人机协作工作台</h3>
                        <p>此功能正在加载中...</p>
                        <button onclick="this.closest('.oa-workspace-modal').style.display='none'">关闭</button>
                    </div>
                `;
                
                this.updateStatusFeedback('OA协作工作台已打开', 'success');
            }
        } catch (error) {
            console.error('打开OA工作台失败:', error);
            this.updateStatusFeedback(`打开工作台失败: ${error.message}`, 'error');
        }
    }

    downloadAuthForm() {
        this.updateStatusFeedback('《授权书》下载完成（模拟）', 'success');
        this.markTaskCompleted('authorization-form');
    }

    downloadAppForm() {
        this.updateStatusFeedback('《申请书》下载完成（模拟）', 'success');
        this.markTaskCompleted('application-form');
    }

    markTaskCompleted(taskId) {
        // 更新任务状态
        const task = this.workflowTasks.find(t => t.id === taskId);
        if (task) {
            task.completed = true;
        }

        // 更新界面
        const checkbox = this.container.querySelector(`#task-${taskId}`);
        if (checkbox) {
            checkbox.checked = true;
        }

        const button = this.container.querySelector(`[data-action="${this.getTaskAction(taskId)}"]`);
        if (button) {
            button.disabled = true;
        }

        this.updateProgress();
    }

    updateProgress() {
        const completedTasks = this.workflowTasks.filter(task => task.completed).length;
        const totalTasks = this.workflowTasks.length;
        const percentage = (completedTasks / totalTasks) * 100;

        const progressFill = this.container.querySelector('#progress-fill');
        const progressText = this.container.querySelector('#progress-text');

        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }

        if (progressText) {
            progressText.textContent = `${completedTasks}/${totalTasks} 已完成`;
        }
    }

    updateStatusFeedback(message, type = 'info') {
        const feedback = this.container.querySelector('#status-feedback');
        if (!feedback) return;

        const icons = {
            info: 'ℹ️',
            success: '✅',
            error: '❌',
            processing: '⏳'
        };

        feedback.innerHTML = `
            <div class="feedback-content ${type}">
                <span class="feedback-icon">${icons[type] || icons.info}</span>
                <span class="feedback-text">${message}</span>
            </div>
        `;
    }

    clearEventListeners() {
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];
    }
}

// 将模块类暴露到全局作用域
window.YingqiZhilianModule = YingqiZhilianModule;
