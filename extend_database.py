#!/usr/bin/env python3
"""
扩展数据库表结构脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.database import db_manager
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extend_database():
    """扩展数据库表结构"""
    try:
        logger.info("开始扩展数据库表结构...")
        
        # 读取SQL文件
        sql_file = project_root / 'database' / '03_extend_companies_table.sql'
        logger.info(f"读取SQL文件: {sql_file}")
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        statements = []
        current_statement = ""
        
        for line in sql_content.split('\n'):
            line = line.strip()
            if not line or line.startswith('--'):
                continue
                
            current_statement += line + " "
            
            if line.endswith(';'):
                statements.append(current_statement.strip())
                current_statement = ""
        
        logger.info(f"准备执行 {len(statements)} 个SQL语句")
        
        # 执行SQL语句
        for i, statement in enumerate(statements, 1):
            if statement and statement != 'COMMIT':
                try:
                    logger.info(f"执行语句 {i}/{len(statements)}: {statement[:50]}...")
                    db_manager.execute_query(statement)
                    logger.info(f"语句 {i} 执行成功")
                except Exception as e:
                    logger.error(f"语句 {i} 执行失败: {e}")
                    logger.error(f"失败的语句: {statement}")
                    # 继续执行其他语句
        
        logger.info("数据库扩展完成！")
        
        # 验证扩展结果
        verify_extension()
        
    except Exception as e:
        logger.error(f"数据库扩展失败: {e}")
        raise

def verify_extension():
    """验证数据库扩展结果"""
    try:
        logger.info("验证数据库扩展结果...")
        
        # 查询扩展后的公司信息
        query = """
        SELECT 
            company_name,
            registered_address,
            communication_address,
            business_description,
            legal_representative
        FROM companies 
        WHERE id IN (
            '34af7659-d69a-4c05-a697-6ae6eb00aad3',
            'a1b2c3d4-e5f6-7890-1234-567890abcdef'
        )
        """
        
        results = db_manager.execute_query(query)
        
        logger.info("扩展后的公司信息:")
        for row in results:
            company_data = dict(row)
            logger.info(f"公司: {company_data.get('company_name', 'N/A')}")
            logger.info(f"  注册地址: {company_data.get('registered_address', 'N/A')}")
            logger.info(f"  通讯地址: {company_data.get('communication_address', 'N/A')}")
            logger.info(f"  业务描述: {company_data.get('business_description', 'N/A')}")
            logger.info(f"  法定代表人: {company_data.get('legal_representative', 'N/A')}")
            logger.info("-" * 50)
        
        logger.info("验证完成！")
        
    except Exception as e:
        logger.error(f"验证失败: {e}")

def main():
    """主函数"""
    try:
        extend_database()
        logger.info("✅ 数据库扩展成功完成！")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库扩展失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
