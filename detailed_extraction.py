#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细提取申报书中的所有关键信息
包括业务编号、贷款金额、有效期等
"""

import docx
from pathlib import Path
import re

def extract_detailed_info():
    """详细提取信息"""
    project_root = Path(__file__).parent
    test_output_dir = project_root / "test_output"
    
    print("🔍 详细信息提取")
    print("="*50)
    
    # 读取额度申报书
    quota_file = test_output_dir / "额度申报书.docx"
    if quota_file.exists():
        print(f"\n📄 分析额度申报书...")
        quota_doc = docx.Document(quota_file)
        quota_text = ""
        for para in quota_doc.paragraphs:
            quota_text += para.text + "\n"
        for table in quota_doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    quota_text += cell.text + " "
        
        print("🔍 搜索额度申报书中的关键信息:")
        
        # 搜索所有PIFU编号
        pifu_matches = re.findall(r'PIFU\d+[A-Z0-9]*', quota_text)
        if pifu_matches:
            print(f"   📋 发现PIFU编号: {set(pifu_matches)}")
        
        # 搜索日期范围
        date_matches = re.findall(r'(\d{4}[-年/]\d{1,2}[-月/]\d{1,2}[日]?)\s*[至到]\s*(\d{4}[-年/]\d{1,2}[-月/]\d{1,2}[日]?)', quota_text)
        if date_matches:
            print(f"   📅 发现日期范围: {date_matches}")
        
        # 搜索金额信息
        amount_matches = re.findall(r'([0-9,]+\.?[0-9]*)\s*万元', quota_text)
        if amount_matches:
            print(f"   💰 发现金额: {set(amount_matches)}")
    
    # 读取业务申报书
    business_file = test_output_dir / "业务申报书.docx"
    if business_file.exists():
        print(f"\n📄 分析业务申报书...")
        business_doc = docx.Document(business_file)
        business_text = ""
        for para in business_doc.paragraphs:
            business_text += para.text + "\n"
        for table in business_doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    business_text += cell.text + " "
        
        print("🔍 搜索业务申报书中的关键信息:")
        
        # 搜索所有PIFU编号
        pifu_matches = re.findall(r'PIFU\d+[A-Z0-9]*', business_text)
        if pifu_matches:
            print(f"   📋 发现PIFU编号: {set(pifu_matches)}")
        
        # 搜索贷款金额
        loan_matches = re.findall(r'([0-9,]+\.?[0-9]*)\s*万元', business_text)
        if loan_matches:
            print(f"   💰 发现金额: {set(loan_matches)}")
        
        # 搜索期限信息
        term_matches = re.findall(r'(\d+)\s*个月', business_text)
        if term_matches:
            print(f"   ⏰ 发现期限: {set(term_matches)}个月")
        
        # 搜索2000万元相关内容
        print(f"\n🔍 搜索'2000万元'相关内容:")
        lines = business_text.split('\n')
        for i, line in enumerate(lines):
            if '2000万元' in line or '2000' in line:
                print(f"   第{i+1}行: {line.strip()}")
        
        # 搜索业务编号相关
        print(f"\n🔍 搜索业务编号相关内容:")
        for i, line in enumerate(lines):
            if '业务编号' in line or 'PIFU' in line:
                print(f"   第{i+1}行: {line.strip()}")

if __name__ == "__main__":
    extract_detailed_info()
