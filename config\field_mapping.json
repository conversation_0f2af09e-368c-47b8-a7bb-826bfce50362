{"description": "企业服务文档生成系统 - 字段词典配置", "version": "2.0", "last_updated": "2025-01-01", "author": "Enterprise Service System", "field_dictionary": {"company_name": {"field": "company_name", "label": "公司名称", "description": "企业工商注册的完整法定名称", "required": true, "type": "text", "format": "max_length:500", "source": "database", "placeholders": ["【公司名称】", "【企业名称】", "【公司全称】", "待补充"], "validation": {"min_length": 2, "max_length": 500, "pattern": null}, "examples": ["成都中科卓尔智能科技集团有限公司", "神光光学集团有限公司"]}, "unified_social_credit_code": {"field": "unified_social_credit_code", "label": "统一社会信用代码", "description": "企业唯一的18位统一社会信用代码", "required": true, "type": "text", "format": "fixed_length:18", "source": "database", "placeholders": ["【统一社会信用代码】", "【信用代码】", "【社会信用代码】"], "validation": {"length": 18, "pattern": "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$"}, "examples": ["91510100MA6CL77FXX", "91510100MA6CGUGA1W"]}, "legal_representative": {"field": "legal_representative", "label": "法定代表人", "description": "企业法定代表人姓名", "required": true, "type": "text", "format": "max_length:100", "source": "database", "placeholders": ["【法定代表人】", "【法人代表】", "【法人姓名】"], "validation": {"min_length": 2, "max_length": 100, "pattern": "^[\\u4e00-\\u9fa5a-zA-Z\\s]{2,100}$"}, "examples": ["杨伟", "贾秉炜", "<PERSON>"]}, "registered_address": {"field": "registered_address", "label": "注册地址", "description": "企业工商注册地址", "required": false, "type": "text", "format": "max_length:500", "source": "database", "placeholders": ["【注册地址】", "【企业地址】"], "validation": {"max_length": 500}, "examples": ["中国（四川）自由贸易试验区成都高新区天府大道中段1366号1栋1单元5层501号"]}, "registered_capital": {"field": "registered_capital", "label": "注册资本", "description": "企业注册资本金额", "required": false, "type": "money", "format": "currency:CNY,decimal:2", "source": "database", "placeholders": ["【注册资本】", "【注册资金】"], "validation": {"min_value": 0, "max_value": 999999999999.99}, "examples": ["18607.4012万元", "5000万元"]}, "contact_phone": {"field": "contact_phone", "label": "联系电话", "description": "企业联系电话号码", "required": false, "type": "text", "format": "phone_number", "source": "database", "placeholders": ["【联系电话】", "【电话号码】"], "validation": {"pattern": "^1[3-9]\\d{9}$|^0\\d{2,3}-?\\d{7,8}$"}, "examples": ["028-86789012", "18665946767"]}, "contact_email": {"field": "contact_email", "label": "联系邮箱", "description": "企业联系邮箱地址", "required": false, "type": "text", "format": "email", "source": "database", "placeholders": ["【联系邮箱】", "【邮箱地址】"], "validation": {"pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"}, "examples": ["<EMAIL>", "<EMAIL>"]}, "deposit_amount": {"field": "deposit_amount", "label": "存款金额", "description": "协定存款基本额度（万元）", "required": true, "type": "money", "format": "currency:CNY,unit:万元,decimal:0", "source": "ai_generated", "placeholders": ["【存款金额】", "（大写）            万", "【基本存款额度】"], "validation": {"min_value": 100, "max_value": 100000}, "default_value": 1000, "examples": ["1000万元", "5000万元", "壹仟万元整"]}, "interest_rate_adjustment": {"field": "interest_rate_adjustment", "label": "利率调整", "description": "在基准利率基础上的调整幅度（基点）", "required": false, "type": "number", "format": "unit:bps,decimal:0", "source": "ai_generated", "placeholders": ["【利率调整】", "25 bps", "45 bps"], "validation": {"min_value": -200, "max_value": 200}, "default_value": 45, "examples": ["45 bps", "25 bps", "加45 bps"]}, "agreement_number": {"field": "agreement_number", "label": "协议编号", "description": "协定存款协议编号", "required": true, "type": "text", "format": "auto_generate", "source": "ai_generated", "placeholders": ["【协议编号】", "【合同编号】"], "validation": {"pattern": "^建八.+协定\\d{4}\\d{3}号$"}, "examples": ["建八卓尔协定2025001号", "建八神光协定2025002号"]}, "account_number": {"field": "account_number", "label": "账户号码", "description": "协定存款账户号码", "required": true, "type": "text", "format": "account_number", "source": "database", "placeholders": ["【账户号码】", "在乙方开立协定存款账户的账号：待补充", "活期存款账户（账号：待补充_）"], "validation": {"length": 20, "pattern": "^\\d{20}$"}, "default_value": "51050148850800008651", "examples": ["51050148850800008651", "51001234567890123456"]}, "agreement_date": {"field": "agreement_date", "label": "协议日期", "description": "协议签署日期", "required": false, "type": "date", "format": "YYYY年MM月DD日", "source": "ai_generated", "placeholders": ["【协议日期】", "【签署日期】", "年    月    日"], "validation": {"date_format": "YYYY-MM-DD"}, "default_value": "current_date", "examples": ["2025年1月1日", "2025年7月31日"]}, "loan_amount": {"field": "loan_amount", "label": "本次支用金额", "description": "本次申请的贷款金额（万元）", "required": true, "type": "money", "format": "currency:CNY,unit:万元,decimal:0", "source": "user_input", "placeholders": ["本次支用金额万元", "2000万元"], "validation": {"min_value": 1, "max_value": 100000}, "examples": ["2000万元"]}, "loan_purpose": {"field": "loan_purpose", "label": "贷款用途", "description": "贷款资金的具体用途说明", "required": true, "type": "text", "format": "max_length:500", "source": "user_input", "placeholders": ["【贷款用途】", "【资金用途】"], "validation": {"min_length": 10, "max_length": 500}, "examples": ["补充流动资金", "设备采购", "项目建设资金"]}, "repayment_source": {"field": "repayment_source", "label": "还款来源", "description": "贷款的还款资金来源说明", "required": true, "type": "text", "format": "max_length:1000", "source": "user_input", "placeholders": ["【还款来源】", "{{还款来源}}", "还款来源审查"], "validation": {"min_length": 20, "max_length": 1000}, "examples": ["经营收入", "销售回款", "项目收益"]}, "guarantee_method": {"field": "guarantee_method", "label": "担保方式", "description": "贷款担保方式详细描述", "required": true, "type": "text", "format": "max_length:500", "source": "user_input", "placeholders": ["信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押"], "validation": {"min_length": 5, "max_length": 500}, "examples": ["信用，同时追加公司实际控制人提供连带责任保证及部分专利产权质押"]}, "loan_term": {"field": "loan_term", "label": "贷款期限", "description": "贷款期限（月）", "required": true, "type": "number", "format": "unit:月,decimal:0", "source": "user_input", "placeholders": ["【贷款期限】", "【借款期限】"], "validation": {"min_value": 1, "max_value": 360}, "examples": ["12个月", "24个月", "36个月"]}, "interest_rate": {"field": "interest_rate", "label": "贷款利率", "description": "年化贷款利率（%）", "required": false, "type": "percent", "format": "decimal:2,unit:%", "source": "ai_generated", "placeholders": ["【贷款利率】", "【年利率】"], "validation": {"min_value": 0.01, "max_value": 24.0}, "examples": ["4.35%", "5.60%", "6.50%"]}, "engagement_date": {"field": "engagement_date", "label": "接洽日期", "description": "客户接洽的日期", "required": true, "type": "date", "format": "YYYY年MM月DD日", "source": "user_input", "placeholders": ["【接洽日期】", "【联系日期】"], "validation": {"date_format": "YYYY-MM-DD"}, "default_value": "current_date", "examples": ["2025年1月1日", "2025年7月31日"]}, "contact_person": {"field": "contact_person", "label": "联系人", "description": "企业方联系人姓名", "required": true, "type": "text", "format": "max_length:100", "source": "user_input", "placeholders": ["【联系人】", "【企业联系人】"], "validation": {"min_length": 2, "max_length": 100}, "examples": ["张经理", "李总", "王主任"]}, "engagement_purpose": {"field": "engagement_purpose", "label": "接洽目的", "description": "本次客户接洽的目的和内容", "required": true, "type": "text", "format": "max_length:500", "source": "user_input", "placeholders": ["【接洽目的】", "【接洽内容】"], "validation": {"min_length": 10, "max_length": 500}, "examples": ["业务咨询", "产品介绍", "合作洽谈"]}, "service_plan": {"field": "service_plan", "label": "服务方案", "description": "为客户制定的服务方案", "required": false, "type": "text", "format": "max_length:1000", "source": "ai_generated", "placeholders": ["【服务方案】", "【解决方案】"], "validation": {"max_length": 1000}, "examples": ["综合金融服务方案", "定制化产品组合"]}, "current_date": {"field": "current_date", "label": "当前日期", "description": "系统当前日期", "required": false, "type": "date", "format": "YYYY年MM月DD日", "source": "ai_generated", "placeholders": ["【当前日期】", "【申请日期】", "【签署日期】"], "validation": {"date_format": "YYYY-MM-DD"}, "default_value": "current_date", "examples": ["2025年1月1日"]}, "application_date": {"field": "application_date", "label": "申请日期", "description": "信贷业务申请日期（年月+空日）", "required": false, "type": "date", "format": "YYYY年MM月 日", "source": "ai_generated", "placeholders": ["2025年3月日", "填报日期：2025年3月     日"], "validation": {"date_format": "YYYY-MM"}, "default_value": "current_month", "examples": ["2025年8月 日"]}, "credit_line_number": {"field": "credit_line_number", "label": "额度编号", "description": "信贷额度编号", "required": false, "type": "text", "format": "fixed_value", "source": "database", "placeholders": ["PIFU510000000N202407210（额度）", "KHED5104885002025228054"], "validation": {"pattern": "^[A-Z0-9]+$"}, "default_value": "KHED5104885002025228054", "examples": ["KHED5104885002025228054"]}, "business_number": {"field": "business_number", "label": "业务编号", "description": "信贷业务申报批复号", "required": false, "type": "text", "format": "fixed_value", "source": "database", "placeholders": ["PIFU5100000002025N00G8（业务）", "批复号：KHED510488500202522805"], "validation": {"pattern": "^[A-Z0-9]+$"}, "default_value": "KHED510488500202522805", "examples": ["KHED510488500202522805"]}, "loan_term_months": {"field": "loan_term_months", "label": "贷款期限", "description": "贷款期限（月数）", "required": true, "type": "number", "format": "unit:个月,decimal:0", "source": "user_input", "placeholders": ["期限13个月", "13个月"], "validation": {"min_value": 1, "max_value": 360}, "examples": ["13个月"]}, "pledge_contract_number": {"field": "pledge_contract_number", "label": "质押合同编号", "description": "自动生成的质押合同编号", "required": false, "type": "text", "format": "auto_generate", "source": "ai_generated", "placeholders": ["建八卓尔专质（2024）001号", "建八卓尔专质(2024）001号"], "validation": {"pattern": "^建八.+\\d{4}\\d{3}号$"}, "default_value": "建八{company_short_name}专质（2025）001号", "examples": ["建八卓尔专质（2025）001号", "建八神光专质（2025）001号"]}, "guarantee_contract_number": {"field": "guarantee_contract_number", "label": "担保合同编号", "description": "自动生成的担保合同编号", "required": false, "type": "text", "format": "auto_generate", "source": "ai_generated", "placeholders": ["建八卓尔保（2024）001号"], "validation": {"pattern": "^建八.+保\\(\\d{4}\\)\\d{3}号$"}, "default_value": "建八{company_short_name}保（2025）001号", "examples": ["建八卓尔保（2025）001号", "建八神光保（2025）001号"]}, "guarantee_amount": {"field": "guarantee_amount", "label": "担保限额", "description": "担保限额金额（万元）", "required": false, "type": "money", "format": "currency:CNY,unit:万元,decimal:0", "source": "database", "placeholders": ["担保限额为4000万元", "4000万元"], "validation": {"min_value": 0}, "default_value": 4000, "examples": ["4000万元"]}, "pledge_value": {"field": "pledge_value", "label": "质押物价值", "description": "质押物评估价值（万元）", "required": false, "type": "money", "format": "currency:CNY,unit:万元,decimal:2", "source": "database", "placeholders": ["价值为328.98万元", "328.98万元"], "validation": {"min_value": 0}, "default_value": 328.98, "examples": ["328.98万元"]}, "environmental_category": {"field": "environmental_category", "label": "环保分类", "description": "环保/ESG分类", "required": false, "type": "text", "format": "fixed_value", "source": "ai_generated", "placeholders": ["借款人环保分类为C类", "ESG分类为绿色"], "validation": {"enum_values": ["ESG分类为绿色"]}, "default_value": "ESG分类为绿色", "examples": ["ESG分类为绿色"]}, "manager_sign_date": {"field": "manager_sign_date", "label": "客户经理签字日期", "description": "客户经理签字日期（年月+空日）", "required": false, "type": "date", "format": "YYYY年MM月 日", "source": "ai_generated", "placeholders": ["客户经理签字日期2025年7月日"], "validation": {"date_format": "YYYY-MM"}, "default_value": "current_month", "examples": ["2025年8月 日"]}, "leader_sign_date": {"field": "leader_sign_date", "label": "分管领导签字日期", "description": "分管经营行领导签字日期（年月+空日）", "required": false, "type": "date", "format": "YYYY年MM月 日", "source": "ai_generated", "placeholders": ["分管经营行领导签字日期2025年7月日"], "validation": {"date_format": "YYYY-MM"}, "default_value": "current_month", "examples": ["2025年8月 日"]}}, "field_categories": {"basic_info": ["company_name", "unified_social_credit_code", "legal_representative", "registered_address", "registered_capital", "contact_phone", "contact_email"], "deposit_services": ["deposit_amount", "interest_rate_adjustment", "agreement_number", "account_number", "agreement_date"], "contract_disbursement": ["loan_amount", "loan_purpose", "repayment_source", "guarantee_method", "loan_term", "interest_rate"], "customer_engagement": ["engagement_date", "contact_person", "engagement_purpose", "service_plan"], "system_generated": ["current_date", "agreement_number"]}, "placeholder_patterns": {"bracket_style": "【{field_name}】", "brace_style": "{{{field_name}}}", "placeholder_text": "待补充", "blank_space": "            ", "date_placeholder": "年    月    日"}, "data_sources": {"database": {"description": "从企业数据库直接获取", "reliability": "high", "fields": ["company_name", "unified_social_credit_code", "legal_representative", "registered_address", "registered_capital", "contact_phone", "contact_email", "account_number"]}, "user_input": {"description": "需要用户手动输入", "reliability": "medium", "fields": ["loan_amount", "loan_purpose", "repayment_source", "guarantee_method", "loan_term", "engagement_date", "contact_person", "engagement_purpose"]}, "ai_generated": {"description": "AI智能生成或推荐", "reliability": "medium", "fields": ["deposit_amount", "interest_rate_adjustment", "agreement_number", "agreement_date", "interest_rate", "service_plan", "current_date"]}}, "field_types": {"text": {"description": "文本类型", "validation": ["min_length", "max_length", "pattern"], "format_options": ["trim", "capitalize", "uppercase", "lowercase"]}, "money": {"description": "金额类型", "validation": ["min_value", "max_value", "decimal_places"], "format_options": ["currency", "unit", "chinese_amount"]}, "date": {"description": "日期类型", "validation": ["date_format", "min_date", "max_date"], "format_options": ["YYYY年MM月DD日", "YYYY-MM-DD", "MM/DD/YYYY"]}, "number": {"description": "数字类型", "validation": ["min_value", "max_value", "decimal_places"], "format_options": ["decimal", "integer", "unit"]}, "percent": {"description": "百分比类型", "validation": ["min_value", "max_value", "decimal_places"], "format_options": ["decimal", "unit"]}, "enum": {"description": "枚举类型", "validation": ["enum_values"], "format_options": ["single_select", "multi_select"]}}, "ai_completion_config": {"enabled": true, "confidence_threshold": 0.8, "auto_fill_fields": ["deposit_amount", "interest_rate_adjustment", "agreement_number", "current_date"], "suggest_fields": ["loan_purpose", "repayment_source", "service_plan"], "validation_fields": ["unified_social_credit_code", "contact_phone", "contact_email"]}, "template_integration": {"replacement_order": ["system_generated", "database", "ai_generated", "user_input"], "missing_field_handling": {"required_fields": "error", "optional_fields": "warning", "placeholder_retention": true}, "format_preservation": {"font_name": "宋体", "font_size": 14, "highlight_color": "YELLOW", "preserve_original": true}}}